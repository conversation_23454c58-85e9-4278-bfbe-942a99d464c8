import os
import json
import asyncio
import websockets
from pathlib import Path
from voice_interface import VoiceInterface
from git_integration import GitManager
from code_management import AdvancedCodeManager
from huggingface_integration import HuggingFaceIntegration

class AGIAssistant:
    def __init__(self):
        self.voice = VoiceInterface()
        self.git = GitManager()
        self.code_mgr = AdvancedCodeManager()
        self.hf = HuggingFaceIntegration()
        self.current_dir = os.getcwd()
        
        # Cargar modelos de Hugging Face en segundo plano
        asyncio.create_task(self.preload_models())
    
    async def preload_models(self):
        await self.hf.load_model('codegen')
        await self.hf.load_model('codet5')
    
    async def handle_command(self, command: str):
        cmd_lower = command.lower()
        
        # Comandos de voz
        if cmd_lower.startswith("escucha"):
            return await self.toggle_voice_listening()
        
        # Comandos Git
        elif cmd_lower.startswith("git status"):
            return await self.git.get_status()
        elif cmd_lower.startswith("git commit"):
            msg = command[11:].strip()
            return await self.git.commit_changes(msg)
        elif cmd_lower.startswith("git push"):
            return await self.git.push_changes()
        elif cmd_lower.startswith("git pull"):
            return await self.git.pull_changes()
        elif cmd_lower.startswith("git resolve"):
            return await self.git.resolve_conflicts()
        
        # Comandos de código avanzado
        elif cmd_lower.startswith("refactor"):
            file = command[8:].strip()
            return await self.code_mgr.refactor_code(file, self._get_refactor_patterns())
        elif cmd_lower.startswith("optimize"):
            file = command[8:].strip()
            return await self.code_mgr.optimize_code(file)
        elif cmd_lower.startswith("explain"):
            file = command[7:].strip()
            return await self.explain_file(file)
        
        # Comandos de Hugging Face
        elif cmd_lower.startswith("hf generate"):
            prompt = command[11:].strip()
            return await self.hf.generate_code(prompt)
        elif cmd_lower.startswith("hf explain"):
            code = command[10:].strip()
            return await self.hf.explain_code(code)
        elif cmd_lower.startswith("hf check"):
            file = command[8:].strip()
            return await self.check_vulnerabilities(file)
        
        else:
            return await super().handle_command(command)
    
    async def toggle_voice_listening(self):
        if self.voice.is_listening:
            await self.voice.stop_listening()
            return "Escucha desactivada"
        else:
            asyncio.create_task(self.voice.start_continuous_listening(
                self.process_voice_command
            ))
            return "Escucha activada - habla ahora"
    
    async def explain_file(self, file_path: str):
        with open(file_path, 'r') as f:
            code = f.read()
        
        explanation = await self.hf.explain_code(code)
        
        explanation_file = f"{file_path}.explanation.md"
        with open(explanation_file, 'w') as f:
            f.write(f"# Explicación de {file_path}\n\n")
            f.write(explanation)
        
        return f"Explicación generada en {explanation_file}"
    
    async def check_vulnerabilities(self, file_path: str):
        with open(file_path, 'r') as f:
            code = f.read()
        
        vulnerabilities = await self.hf.detect_vulnerabilities(code)
        
        if isinstance(vulnerabilities, list):
            report_file = f"{file_path}.security_audit.md"
            with open(report_file, 'w') as f:
                f.write("# Reporte de Seguridad\n\n")
                f.write("## Vulnerabilidades detectadas\n")
                f.write("\n".join(f"- {vuln}" for vuln in vulnerabilities))
            return f"Vulnerabilidades detectadas - ver {report_file}"
        return vulnerabilities
    
    def _get_refactor_patterns(self):
        return {
            'bad_names': ['temp', 'aux', 'x', 'var'],
            'replace_for_with_comprehension': True,
            'long_method_threshold': 20
        }