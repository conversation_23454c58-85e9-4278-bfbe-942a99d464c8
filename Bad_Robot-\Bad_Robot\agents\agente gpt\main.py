import platform
import os
import psutil
import csv
import random
from entornos import financiero
from agentes.base import Agente
from agentes.genetico import seleccionar_mejores, cruzar_genotipos, mutar_genotipo
from agentes.comunicacion import BusComunicacion
from config import seleccionar_entorno
from visualizacion import graficar_precios, graficar_recompensas




def detectar_entorno_maquina():
    info = {
        "sistema": platform.system(),
        "nucleo": platform.release(),
        "cpu_cores": psutil.cpu_count(logical=False),
        "ram_gb": round(psutil.virtual_memory().total / (1024**3), 2),
    }
    return info

def main():
    os.makedirs("logs", exist_ok=True)
    csv_log = open("logs/simulacion.csv", mode="w", newline="")
    writer = csv.writer(csv_log)
    writer.writerow(["ciclo", "agente_id", "accion", "precio_accion", "tendencia", "volatilidad", "noticias_positiva", "noticias_negativa"])

    info_maquina = detectar_entorno_maquina()
    entorno_id = seleccionar_entorno(info_maquina)

    if entorno_id == "financiero":
        entorno = financiero.MercadoFinanciero()
    else:
        raise ValueError("Solo soporte para entorno financiero en este demo")

    bus = BusComunicacion()
    agentes = [Agente(id=i, entorno=entorno, bus=bus) for i in range(5)]

    for ciclo in range(100):
        observaciones = entorno.obtener_estado()

        # Comunicación
        for agente in agentes:
            bus.enviar({"id": agente.id, "estado": observaciones})

        mensajes = bus.recibir_todos()

        for agente in agentes:
            agente.mensajes = mensajes
            accion = agente.decidir(observaciones)
            entorno.aplicar_accion(agente.id, accion)
            writer.writerow([
                ciclo,
                agente.id,
                accion,
                observaciones["precio_accion"],
                observaciones["tendencia"],
                round(observaciones["volatilidad"], 3),
                observaciones["noticias"]["positiva"],
                observaciones["noticias"]["negativa"]
            ])

        entorno.actualizar()

        # Evolución genética cada 20 ciclos
        if ciclo > 0 and ciclo % 20 == 0:
            top = seleccionar_mejores(agentes, entorno.recompensas)
            nuevos_agentes = []
            for i in range(len(agentes)):
                p1, p2 = random.sample(top, 2)
                nuevo_gen = mutar_genotipo(cruzar_genotipos(p1.genotipo, p2.genotipo))
                nuevos_agentes.append(Agente(id=i, entorno=entorno, genotipo=nuevo_gen, bus=bus))
            agentes = nuevos_agentes

    csv_log.close()
    entorno.mostrar_resultados()
    # Supón que entorno tiene un atributo `historial_precios`
    graficar_precios(entorno.historial_precios)

    # Supón que recolectaste recompensas por agente
    graficar_recompensas({a.id: a.recompensa_total for a in agentes})

if __name__ == "__main__":
    main()
