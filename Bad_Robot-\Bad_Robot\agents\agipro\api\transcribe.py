@app.route('/api/transcribe', methods=['POST'])
def transcribe_audio():
    if 'audio' not in request.files:
        return jsonify({'error': 'No audio file provided'}), 400
    
    audio_file = request.files['audio']
    
    try:
        # Convertir el audio a formato compatible
        audio_data = BytesIO(audio_file.read())
        
        # Usar la librería speech_recognition para procesar el audio
        with sr.AudioFile(audio_data) as source:
            audio = voice_recognizer.record(source)  # Grabación del audio
        
        # Reconocimiento usando Google Speech Recognition (requiere internet)
        text = voice_recognizer.recognize_google(audio, language='es-ES')
        
        return jsonify({
            'status': 'success',
            'text': text,
            'language': 'es-ES'
        })
    
    except sr.UnknownValueError:
        return jsonify({
            'status': 'error',
            'error': 'No se pudo entender el audio'
        }), 400
        
    except sr.RequestError as e:
        return jsonify({
            'status': 'error',
            'error': f"Error en el servicio de Google: {e}"
        }), 500
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': f"Error interno: {str(e)}"
        }), 500