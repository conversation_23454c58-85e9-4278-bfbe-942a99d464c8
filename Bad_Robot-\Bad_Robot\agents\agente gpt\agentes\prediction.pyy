import random
from sklearn.linear_model import LinearRegression
import numpy as np

modelo = LinearRegression()

def entrenar_modelo(precios):
    if len(precios) < 10:
        return  # no hay suficientes datos
    X = np.arange(len(precios)).reshape(-1, 1)
    y = np.array(precios)
    modelo.fit(X, y)

def predecir_precio_siguiente(precios):
    if len(precios) < 2:
        return precios[-1] if precios else 0
    X = np.array([[len(precios)]])
    prediccion = modelo.predict(X)
    return prediccion[0]

def predecir_tendencia(precios, n=5):
    """
    Predice si el mercado subirá o bajará.
    - Entrada: últimos `n` precios.
    - Salida: "sube", "baja" o "estable".
    """
    if len(precios) < n:
        return random.choice(["sube", "baja", "estable"])

    tendencia = sum(
        1 if precios[i] > precios[i - 1] else -1
        for i in range(1, n)
    )

    if tendencia > 2:
        return "sube"
    elif tendencia < -2:
        return "baja"
    else:
        return "estable"
