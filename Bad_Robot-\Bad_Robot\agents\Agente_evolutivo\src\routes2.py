from flask import Blueprint, request, jsonify, abort

tasks_bp = Blueprint('tasks', __name__)

# Base de datos en memoria (simple lista de diccionarios)
tasks = [
    # Añado un par de tareas de ejemplo para que sea más fácil probar
    {'id': 1, 'title': 'Aprender a usar Flask', 'completed': True},
    {'id': 2, 'title': 'Crear mi primera API', 'completed': False}
]
task_id_counter = 3 # Ajustamos el contador al siguiente ID disponible

# --- Función Auxiliar para encontrar una tarea ---
# He creado esta pequeña función para no repetir código.
# Busca una tarea por su ID en nuestra lista.
def find_task_by_id(task_id):
    task = next((task for task in tasks if task['id'] == task_id), None)
    return task

# --- Endpoints existentes (sin cambios) ---
@tasks_bp.route('/tasks', methods=['GET'])
def get_tasks():
    return jsonify(tasks)

@tasks_bp.route('/tasks', methods=['POST'])
def add_task():
    global task_id_counter
    data = request.get_json()
    # Una pequeña validación para asegurar que el título viene en la petición
    if not data or not 'title' in data:
        abort(400, description="El campo 'title' es requerido.")
    
    new_task = {
        'id': task_id_counter,
        'title': data['title'],
        'completed': False
    }
    tasks.append(new_task)
    task_id_counter += 1
    return jsonify(new_task), 201

# --- ¡NUEVOS Endpoints! ---

# Obtener una tarea específica
@tasks_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    task = find_task_by_id(task_id)
    if task is None:
        # Si la tarea no se encuentra, devolvemos un error 404
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    return jsonify(task)

# Actualizar una tarea existente
@tasks_bp.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    task = find_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    data = request.get_json()
    # Actualizamos los campos que vengan en la petición.
    # Si no vienen, conservamos el valor original.
    task['title'] = data.get('title', task['title'])
    task['completed'] = data.get('completed', task['completed'])
    
    return jsonify(task)

# Eliminar una tarea
@tasks_bp.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    task = find_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    tasks.remove(task)
    # Devolvemos una respuesta vacía con el código 204 No Content, una práctica común para DELETE.
    return '', 204