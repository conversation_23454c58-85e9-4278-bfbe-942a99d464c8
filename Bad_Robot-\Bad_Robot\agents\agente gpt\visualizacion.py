import matplotlib.pyplot as plt

def graficar_precios(precios):
    plt.plot(precios, label="Precio")
    plt.title("Evolución del precio del mercado")
    plt.xlabel("Ciclo")
    plt.ylabel("Precio")
    plt.legend()
    plt.show()

def graficar_recompensas(recompensas):
    plt.bar(range(len(recompensas)), list(recompensas.values()))
    plt.title("Recompensas por agente")
    plt.xlabel("Agente")
    plt.ylabel("Recompensa")
    plt.show()
