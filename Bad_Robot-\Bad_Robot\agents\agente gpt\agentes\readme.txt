Sistema de Agentes Evolutivos en Mercado Bursátil
Este proyecto simula un mercado financiero con agentes autónomos que interactúan mediante comunicación multiagente, usan predicción de mercado, se especializan genéticamente y se adaptan al entorno.

Estructura del proyecto
main.py
Punto de entrada. Detecta entorno, crea agentes, ejecuta simulación y registra resultados.

entornos/financiero.py
Simula un mercado bursátil con dinámica de oferta y demanda, variación de precios, y noticias económicas.

agentes/base.py
Definición del agente con:

Decisión basada en predicción y noticias.

Genotipo para especialización.

Registro de acciones en logs.

agentes/genetico.py
Funciones para evolución genética:

Selección de mejores agentes.

Cruce de genotipos.

Mutación de genotipo.

agentes/comunicacion.py
Bus de comunicación simple para intercambio de mensajes entre agentes.

Características
Predicción de mercado:
Los agentes anticipan la tendencia y deciden acciones acorde.

Especialización genética:
Tipos de agentes: inversor, especulador, conservador.
Cada uno con atributos como agresividad y paciencia.

Comunicación multiagente:
Compartición de estados y mensajes para toma de decisiones colaborativa.

Evolución y adaptación:
Cada 20 ciclos se seleccionan y reproducen agentes con mejor desempeño, mutando genotipos.

Requisitos
Python 3.8+

Paquetes: psutil (para detectar entorno), estándar para todo lo demás.

bash
Copiar código
pip install psutil
Cómo ejecutar
bash
Copiar código
python main.py
La simulación corre por 100 ciclos. Los logs de agentes se guardan en logs/agente_{id}.json.
Un resumen de la simulación queda en logs/simulacion.csv.

Extensiones posibles
Mejorar módulo de predicción con ML o redes neuronales.

Añadir más tipos de agentes o estrategias.

Incorporar más entornos (industrial, ecosistema, espacial).

Visualización gráfica de evolución de precios y acciones.

Mecanismos más complejos de comunicación y negociación.

¿Quieres que te ayude a preparar código para alguna extensión en particular?







Preguntar a ChatGPT
