import os
import datetime
import json
from flask import Flask, render_template, request, send_file
from dotenv import load_dotenv
import openai
import requests
from transformers import AutoTokenizer, AutoModelForCausalLM
from transformers.pipelines import pipeline

# Cargar variables de entorno
load_dotenv()

# Inicializar Flask
app = Flask(__name__)
AGI_NAME = "AOAD_WEB_AGI"

# Configurar modelos
openai.api_key = os.getenv("OPENAI_API_KEY")
huggingface_token = os.getenv("HUGGINGFACE_API_TOKEN")

# === FUNCIONES DE UTILIDAD ===

def log_message(message):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {AGI_NAME}: {message}")

def crear_archivo_con_codigo(nombre_archivo, codigo, lenguaje="desconocido"):
    try:
        with open(nombre_archivo, "w", encoding="utf-8") as f:
            f.write(f"# Archivo generado por {AGI_NAME} en {datetime.datetime.now()}\n")
            f.write(f"# Lenguaje: {lenguaje}\n\n")
            f.write(codigo)
        return nombre_archivo
    except Exception as e:
        log_message(f"Error al guardar archivo: {e}")
        return None

# === MODELOS DE GENERACIÓN ===

def generar_con_openai(prompt):
    try:
        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt=prompt,
            max_tokens=500,
            temperature=0.7
        )
        return response.choices[0].text.strip()
    except Exception as e:
        log_message(f"OpenAI falló: {e}")
        return None

def generar_con_github_copilot(prompt):
    """Simulación local o conexión real si se tiene acceso"""
    log_message("Usando GitHub Copilot (simulado)")
    return f"# Simulación de código generado por GitHub Copilot\n# Prompt:\n{prompt}\n\nprint('Hola desde GitHub Copilot simulado')"

def generar_con_starcoder(prompt):
    try:
        log_message("Generando con StarCoder...")
        tokenizer = AutoTokenizer.from_pretrained("bigcode/starcoder")
        model = AutoModelForCausalLM.from_pretrained("bigcode/starcoder")
        pipe = pipeline("text-generation", model=model, tokenizer=tokenizer, device=0)
        result = pipe(prompt, max_new_tokens=200, num_return_sequences=1)[0]['generated_text']
        return result[len(prompt):]
    except Exception as e:
        log_message(f"StarCoder falló: {e}")
        return None

def elegir_modelo_y_generar(prompt):
    for generador in [generar_con_openai, generar_con_starcoder, generar_con_github_copilot]:
        resultado = generador(prompt)
        if resultado:
            return resultado, generador.__name__.replace("generar_con_", "")
    return None, "ninguno"

# === RUTAS FLASK ===

@app.route("/", methods=["GET", "POST"])
def index():
    if request.method == "POST":
        peticion_usuario = request.form["peticion"]
        tipo_lenguaje = request.form["lenguaje"]

        plan = {
            "python": ".py",
            "bash": ".sh",
            "html": ".html",
            "javascript": ".js"
        }
        extension = plan.get(tipo_lenguaje.lower(), ".py")

        prompt = f"Escribe un ejemplo útil de código en {tipo_lenguaje} que resuelva: '{peticion_usuario}'."

        codigo_generado, modelo_usado = elegir_modelo_y_generar(prompt)

        if not codigo_generado:
            return render_template("index.html", error="No se pudo generar código con ningún modelo.")

        nombre_archivo = f"agi_codigo{extension}"
        ruta_archivo = crear_archivo_con_codigo(nombre_archivo, codigo_generado, tipo_lenguaje)

        if not ruta_archivo:
            return render_template("index.html", error="No se pudo guardar el archivo.")

        return send_file(ruta_archivo, as_attachment=True)

    return render_template("index.html")

# === INICIO ===

if __name__ == "__main__":
    app.run(debug=True)