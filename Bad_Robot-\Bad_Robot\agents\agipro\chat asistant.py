import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
import json
import ast
import subprocess
import sys
from pathlib import Path
from collections import deque
import sympy as sp  # Para razonamiento simbólico

class NeuroSymbolicAGI:
    def __init__(self):
        # Componente Neuro-Simbólico
        self.symbolic_knowledge = self._load_symbolic_knowledge()
        self.neural_processor = NeuralProcessor()
        
        # Componente de Aprendizaje por Refuerzo
        self.dqn_agent = DQNAgent(state_size=512, action_size=100)
        self.memory = deque(maxlen=10000)
        self.reward_system = MultiRewardSystem()
        
        # Entorno de Ejecución de Código
        self.code_environment = CodeExecutionEnv()
        
        # Configuraciones
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.gamma = 0.95
        self.batch_size = 32
        
    def _load_symbolic_knowledge(self):
        """Carga reglas y conocimiento simbólico"""
        return {
            "code_operations": {
                "read": {"params": ["file_path"], "description": "Lee un archivo"},
                "write": {"params": ["file_path", "content"], "description": "Escribe en un archivo"},
                "execute": {"params": ["code"], "description": "Ejecuta código"},
                "modify": {"params": ["file_path", "changes"], "description": "Modifica un archivo"}
            },
            "reasoning_rules": [
                {"if": "user_asks('how to')", "then": "provide_instructional_response"},
                {"if": "detect_code_request", "then": "activate_code_processor"}
            ]
        }

    def process_input(self, user_input):
        """Procesa la entrada del usuario usando múltiples enfoques"""
        # Análisis neuro-simbólico
        symbolic_analysis = self._symbolic_analysis(user_input)
        neural_analysis = self.neural_processor.analyze(user_input)
        
        # Decisión híbrida
        action = self._hybrid_decision(symbolic_analysis, neural_analysis)
        
        # Ejecutar acción y obtener recompensas
        result, rewards = self._execute_action(action, user_input)
        
        # Aprender de la experiencia
        self._learn_from_experience(user_input, action, result, rewards)
        
        return result

    def _symbolic_analysis(self, text):
        """Análisis basado en reglas simbólicas"""
        # Detección de operaciones de código
        code_ops = []
        for op, config in self.symbolic_knowledge["code_operations"].items():
            if any(keyword in text.lower() for keyword in config["keywords"] if "keywords" in config else [op]):
                code_ops.append(op)
        
        # Razonamiento lógico
        conclusions = []
        for rule in self.symbolic_knowledge["reasoning_rules"]:
            if eval(rule["if"], {}, {"user_input": text, "self": self}):
                conclusions.append(rule["then"])
        
        return {
            "code_operations": code_ops,
            "conclusions": conclusions,
            "symbolic_features": self._extract_symbolic_features(text)
        }

    def _extract_symbolic_features(self, text):
        """Extrae características simbólicas usando lógica formal"""
        try:
            # Análisis lógico-matemático
            expr = sp.sympify(text.replace("?", "").replace("¿", ""), evaluate=False)
            return {"mathematical": True, "expression": str(expr)}
        except:
            # Análisis lógico-semántico
            logic_parts = {
                "conditionals": sum(1 for word in ["if", "then", "else"] if word in text.lower()),
                "quantifiers": sum(1 for word in ["all", "every", "some", "none"] if word in text.lower())
            }
            return {"mathematical": False, "logical": logic_parts}

    def _hybrid_decision(self, symbolic, neural):
        """Combina análisis simbólico y subsimbólico"""
        # Si hay operaciones de código claras, priorizar enfoque simbólico
        if symbolic["code_operations"]:
            action_type = "symbolic"
            action = random.choice(symbolic["code_operations"])
        # Si el componente neural tiene alta confianza, usarlo
        elif neural["confidence"] > 0.8:
            action_type = "neural"
            action = neural["top_action"]
        # Exploración con epsilon-greedy
        else:
            if random.random() <= self.epsilon:
                action_type = "explore"
                action = random.randint(0, self.dqn_agent.action_size - 1)
            else:
                state = self._create_state_vector(symbolic, neural)
                action = self.dqn_agent.act(state)
                action_type = "dqn"
        
        return {"type": action_type, "action": action}

    def _execute_action(self, action, user_input):
        """Ejecuta la acción determinada"""
        rewards = {}
        result = ""
        
        try:
            # Acciones simbólicas (operaciones con código)
            if action["type"] == "symbolic":
                if action["action"] == "read":
                    file_path = self._extract_file_path(user_input)
                    result = self.code_environment.read_file(file_path)
                    rewards["correctness"] = 1.0 if result else -1.0
                
                elif action["action"] == "write":
                    file_path, content = self._extract_write_params(user_input)
                    success = self.code_environment.write_file(file_path, content)
                    result = f"File {'successfully' if success else 'unsuccessfully'} written"
                    rewards["correctness"] = 1.0 if success else -1.0
                
                # ... otras operaciones de código
                
                rewards["efficiency"] = 0.9
                rewards["symbolic_use"] = 1.0
            
            # Acciones neurales (respuestas conversacionales)
            elif action["type"] == "neural":
                result = self.neural_processor.generate_response(user_input)
                rewards["fluency"] = self.reward_system.evaluate_fluency(result)
                rewards["relevance"] = self.reward_system.evaluate_relevance(result, user_input)
            
            # Acciones DQN
            elif action["type"] == "dqn":
                state = self._create_state_vector(
                    self._symbolic_analysis(user_input),
                    self.neural_processor.analyze(user_input)
                )
                result = self.dqn_agent.execute_action(action["action"], state)
                rewards = self.reward_system.evaluate_dqn_action(result, user_input)
            
            # Acciones de exploración
            else:
                result = f"Exploratory action taken: {action}"
                rewards["exploration"] = 0.5
            
            # Recompensa cognitiva adicional
            rewards["cognitive"] = self.reward_system.evaluate_cognitive_value(result, user_input)
            
        except Exception as e:
            result = f"Error executing action: {str(e)}"
            rewards["error"] = -1.0
        
        return result, rewards

    def _learn_from_experience(self, user_input, action, result, rewards):
        """Aprende de la interacción"""
        # Actualizar DQN
        state = self._create_state_vector(
            self._symbolic_analysis(user_input),
            self.neural_processor.analyze(user_input)
        )
        next_state = self._create_state_vector(
            self._symbolic_analysis(result),
            self.neural_processor.analyze(result)
        )
        
        total_reward = self.reward_system.combine_rewards(rewards)
        self.memory.append((state, action["action"], total_reward, next_state, False))
        
        # Entrenamiento por lotes
        if len(self.memory) >= self.batch_size:
            batch = random.sample(self.memory, self.batch_size)
            self.dqn_agent.train(batch, self.gamma)
        
        # Decaimiento de epsilon
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
        
        # Aprendizaje simbólico (añadir nuevas reglas si es necesario)
        if rewards.get("symbolic_use", 0) > 0.7:
            self._update_symbolic_knowledge(user_input, action, result)

    def _update_symbolic_knowledge(self, user_input, action, result):
        """Añade nuevo conocimiento simbólico basado en interacciones exitosas"""
        new_rule = {
            "if": f"' {action['action']} ' in user_input.lower()",
            "then": f"perform_{action['action']}_operation"
        }
        
        if new_rule not in self.symbolic_knowledge["reasoning_rules"]:
            self.symbolic_knowledge["reasoning_rules"].append(new_rule)
            
            # Guardar conocimiento actualizado
            with open("symbolic_knowledge.json", "w") as f:
                json.dump(self.symbolic_knowledge, f)

    def _create_state_vector(self, symbolic, neural):
        """Crea vector de estado para DQN"""
        symbolic_vec = [
            len(symbolic["code_operations"]),
            len(symbolic["conclusions"]),
            int(symbolic["symbolic_features"]["mathematical"]),
            symbolic["symbolic_features"]["logical"]["conditionals"],
            symbolic["symbolic_features"]["logical"]["quantifiers"]
        ]
        
        neural_vec = [
            neural["confidence"],
            neural["sentiment"],
            len(neural["keywords"])
        ]
        
        return np.array(symbolic_vec + neural_vec + [self.epsilon])

class NeuralProcessor:
    """Componente subsimbólico (redes neuronales)"""
    def __init__(self):
        # Modelos neuronales (simplificado para ejemplo)
        self.analyzer = nn.Sequential(
            nn.Linear(768, 256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )
        
        self.generator = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, 768)
        )
        
        # Cargar embeddings (en realidad usar un modelo preentrenado)
        self.vocab = {"<UNK>": 0}
        self.embeddings = np.random.rand(1000, 768)
        
    def analyze(self, text):
        """Analiza texto usando enfoque subsimbólico"""
        tokens = self._tokenize(text)
        vec = self._text_to_vec(tokens)
        
        with torch.no_grad():
            features = self.analyzer(torch.FloatTensor(vec))
        
        return {
            "confidence": float(torch.sigmoid(features.mean())),
            "sentiment": float(torch.tanh(features[0])),
            "keywords": self._extract_keywords(tokens),
            "features": features.numpy(),
            "top_action": int(features.argmax())
        }
    
    def generate_response(self, prompt):
        """Genera respuesta usando el componente neural"""
        tokens = self._tokenize(prompt)
        vec = self._text_to_vec(tokens)
        
        with torch.no_grad():
            response_vec = self.generator(torch.FloatTensor(vec))
        
        # Decodificación simplificada
        return f"Respuesta neural a: {prompt[:20]}... (vector: {response_vec.mean().item():.2f})"
    
    def _tokenize(self, text):
        """Tokenización simplificada"""
        return text.lower().split()[:20]
    
    def _text_to_vec(self, tokens):
        """Convierte texto a vector (embedding promedio)"""
        vecs = [self.embeddings[self.vocab.get(t, 0)] for t in tokens]
        return np.mean(vecs, axis=0) if vecs else np.zeros(768)
    
    def _extract_keywords(self, tokens):
        """Extrae palabras clave (versión simplificada)"""
        keywords = {"code", "file", "write", "read", "execute", "function"}
        return [t for t in tokens if t in keywords]

class DQNAgent:
    """Agente de Deep Q-Network"""
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.95    # discount rate
        self.epsilon = 1.0  # exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.model = self._build_model()
        self.target_model = self._build_model()
        self.update_target_model()

    def _build_model(self):
        """Construye el modelo de la red neuronal"""
        model = nn.Sequential(
            nn.Linear(self.state_size, 64),
            nn.ReLU(),
            nn.Linear(64, 64),
            nn.ReLU(),
            nn.Linear(64, self.action_size)
        )
        return model

    def update_target_model(self):
        """Actualiza el modelo objetivo"""
        self.target_model.load_state_dict(self.model.state_dict())

    def act(self, state):
        """Selecciona acción usando política epsilon-greedy"""
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)
        state = torch.FloatTensor(state).unsqueeze(0)
        act_values = self.model(state)
        return torch.argmax(act_values[0]).item()

    def train(self, batch, gamma):
        """Entrena el modelo con experiencias pasadas"""
        states, actions, rewards, next_states, dones = zip(*batch)
        
        states = torch.FloatTensor(np.array(states))
        next_states = torch.FloatTensor(np.array(next_states))
        actions = torch.LongTensor(actions)
        rewards = torch.FloatTensor(rewards)
        dones = torch.FloatTensor(dones)
        
        current_q = self.model(states).gather(1, actions.unsqueeze(1))
        next_q = self.target_model(next_states).max(1)[0].detach()
        target = rewards + (gamma * next_q * (1 - dones))
        
        loss = nn.MSELoss()(current_q.squeeze(), target)
        
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    def execute_action(self, action, state):
        """Ejecuta una acción del espacio de acciones"""
        # Mapeo de acciones a comportamientos (simplificado)
        if action < 20:
            return f"Respuesta conversacional (acción {action})"
        elif action < 40:
            return f"Operación de código (acción {action})"
        else:
            return f"Acciones complejas (acción {action})"

class CodeExecutionEnv:
    """Entorno seguro para ejecución de código"""
    def __init__(self):
        self.safe_commands = {
            'read': self.read_file,
            'write': self.write_file,
            'execute': self.safe_execute
        }
        self.allowed_libs = ['math', 'datetime', 'json']
        
    def read_file(self, file_path):
        """Lee un archivo de manera segura"""
        try:
            path = Path(file_path)
            if not path.is_file():
                return "Error: Archivo no encontrado"
            
            with open(file_path, 'r') as f:
                return f.read()
        except Exception as e:
            return f"Error leyendo archivo: {str(e)}"
    
    def write_file(self, file_path, content):
        """Escribe en un archivo de manera segura"""
        try:
            path = Path(file_path)
            if not path.parent.exists():
                return False
            
            with open(file_path, 'w') as f:
                f.write(content)
            return True
        except:
            return False
    
    def safe_execute(self, code):
        """Ejecuta código Python de manera segura"""
        try:
            # Análisis ast para verificar seguridad
            parsed = ast.parse(code)
            for node in ast.walk(parsed):
                if isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    for n in node.names:
                        if n.name.split('.')[0] not in self.allowed_libs:
                            return f"Error: Librería no permitida: {n.name}"
            
            # Ejecución en entorno restringido
            loc = {}
            exec(code, {'__builtins__': None}, loc)
            return str(loc.get('result', 'Ejecutado sin errores'))
        except Exception as e:
            return f"Error ejecutando código: {str(e)}"

class MultiRewardSystem:
    """Sistema de recompensas múltiples"""
    def __init__(self):
        self.weights = {
            'correctness': 0.3,
            'efficiency': 0.2,
            'fluency': 0.15,
            'relevance': 0.2,
            'exploration': 0.05,
            'cognitive': 0.1,
            'error': -1.0
        }
        
    def evaluate_fluency(self, response):
        """Evalúa fluidez de la respuesta"""
        length = len(response.split())
        return min(1.0, length / 20)  # Normalizado a 20 palabras
    
    def evaluate_relevance(self, response, prompt):
        """Evalúa relevancia a la entrada"""
        prompt_words = set(prompt.lower().split())
        response_words = set(response.lower().split())
        common = prompt_words & response_words
        return len(common) / max(1, len(prompt_words))
    
    def evaluate_dqn_action(self, result, prompt):
        """Evalúa acciones del DQN"""
        return {
            'correctness': 0.7 if "Error" not in result else -0.5,
            'relevance': self.evaluate_relevance(result, prompt),
            'efficiency': 0.8
        }
    
    def evaluate_cognitive_value(self, result, prompt):
        """Evalúa valor cognitivo (simplificado)"""
        cognitive_keywords = {'por qué', 'cómo', 'explicar', 'razón'}
        return 0.5 if any(k in result.lower() for k in cognitive_keywords) else 0.2
    
    def combine_rewards(self, rewards):
        """Combina múltiples recompensas"""
        total = 0.0
        for k, v in rewards.items():
            total += self.weights.get(k, 0) * v
        return total

# Ejemplo de uso
if __name__ == "__main__":
    agi = NeuroSymbolicAGI()
    
    # Interacción de ejemplo
    user_inputs = [
        "Cómo puedo leer el archivo data.txt?",
        "Por favor escribe 'Hola mundo' en test.txt",
        "Ejecuta print(2+2)",
        "Explícame el teorema de Pitágoras"
    ]
    
    for input_text in user_inputs:
        print(f"\nUsuario: {input_text}")
        response = agi.process_input(input_text)
        print(f"AGI: {response}")