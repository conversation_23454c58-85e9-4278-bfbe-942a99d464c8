class EntornoIndustrial:
    def __init__(self):
        self.recursos = 1000
        self.produccion = 0

    def obtener_estado(self):
        return {"recursos": self.recursos, "produccion": self.produccion}

    def aplicar_accion(self, agente_id, accion):
        if accion == "producir":
            if self.recursos >= 10:
                self.recursos -= 10
                self.produccion += 1

    def actualizar(self):
        pass  # se pueden incluir eventos o accidentes

    def mostrar_resultados(self):
        print(f"Producción total: {self.produccion}")
