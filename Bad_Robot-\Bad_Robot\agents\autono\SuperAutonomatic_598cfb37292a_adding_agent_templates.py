"""adding agent templates

Revision ID: 598cfb37292a
Revises: 2f97c068fab9
Create Date: 2023-06-05 12:44:30.982492

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine import Inspector

# revision identifiers, used by Alembic.
revision = '598cfb37292a'
down_revision = '2cc1179834b0'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agent_template_steps',
                    sa.Column('created_at', sa.DateTime(), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), nullable=True),
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('agent_template_id', sa.Integer(), nullable=True),
                    sa.Column('unique_id', sa.String(), nullable=True),
                    sa.Column('prompt', sa.Text(), nullable=True),
                    sa.Column('variables', sa.Text(), nullable=True),
                    sa.Column('output_type', sa.String(), nullable=True),
                    sa.Column('step_type', sa.String(), nullable=True),
                    sa.Column('next_step_id', sa.Integer(), nullable=True),
                    sa.Column('history_enabled', sa.Boolean(), nullable=True),
                    sa.Column('completion_prompt', sa.Text(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )

    op.create_table('agent_templates',
                    sa.Column('created_at', sa.DateTime(), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), nullable=True),
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('name', sa.String(), nullable=True),
                    sa.Column('description', sa.Text(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )

    op.add_column('agent_executions', sa.Column('current_step_id', sa.Integer()))
    op.add_column('agents', sa.Column('agent_template_id', sa.Integer()))
    op.create_index("ix_agents_agnt_template_id", "agents", ['agent_template_id'])
    op.create_index("ix_aea_step_id", "agent_executions", ['current_step_id'])

    op.create_index("ix_ats_unique_id", "agent_template_steps", ['unique_id'])
    op.create_index("ix_at_name", "agent_templates", ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agents', 'agent_template_id')
    op.add_column('agent_executions', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('agent_executions', 'current_step_id')
    op.drop_table('agent_templates')
    op.drop_table('agent_template_steps')
    # ### end Alembic commands ###


