from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import numpy as np
import re
import requests
from bs4 import BeautifulSoup
from duckduckgo_search import DDGS
from PIL import Image
import cv2
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from collections import Counter

from dqn_agent_researcher import ResearchDQNAgent

# --- Configuración ---
app = Flask(__name__)
CORS(app)
WORKSPACE_DIR = os.path.abspath("ai_workspace")
YOLO_DIR = os.path.abspath("yolo_model")
if not os.path.exists(WORKSPACE_DIR): os.makedirs(WORKSPACE_DIR)

# Verificar si los archivos del modelo YOLO existen
yolo_weights = os.path.join(YOLO_DIR, "yolov3.weights")
yolo_cfg = os.path.join(YOLO_DIR, "yolov3.cfg")
coco_names = os.path.join(YOLO_DIR, "coco.names")

if not all(os.path.exists(p) for p in [yolo_weights, yolo_cfg, coco_names]):
    print("*"*60)
    print("ERROR: Faltan archivos del modelo YOLO en la carpeta 'yolo_model'.")
    print("Por favor, sigue las instrucciones de descarga en la Fase 1.")
    print("*"*60)
    exit()

# Cargar modelo YOLO
net = cv2.dnn.readNet(yolo_weights, yolo_cfg)
with open(coco_names, "r") as f:
    classes = [line.strip() for line in f.readlines()]

# --- Configuración de la IA ---
ACTIONS = {0: "BUSCAR_TEXTO", 1: "BUSCAR_IMAGEN", 2: "ANALIZAR_TEXTO", 3: "ANALIZAR_IMAGEN", 4: "SINTETIZAR"}
STATE_SIZE = 4  # has_text, has_image, text_analyzed, image_analyzed
ACTION_SIZE = len(ACTIONS)
agent = ResearchDQNAgent(STATE_SIZE, ACTION_SIZE)

# Contexto de la investigación actual
research_context = {}
last_step_data = {}

# --- Funciones de las Acciones ---
def extract_topic(command):
    # Intenta quitar palabras de acción para aislar el tema
    clean_cmd = re.sub(r'investiga sobre|busca|encuentra|dame|una foto de|una imagen de', '', command, flags=re.I)
    return clean_cmd.strip()

def search_text(topic):
    try:
        with DDGS() as ddgs:
            results = list(ddgs.text(topic, max_results=1))
            if not results: return "No se encontraron resultados de texto.", {}
            url = results[0]['href']
            response = requests.get(url, timeout=10, headers={'User-Agent': 'Mozilla/5.0'})
            soup = BeautifulSoup(response.text, 'html.parser')
            paragraphs = soup.find_all('p')
            full_text = ' '.join([p.get_text() for p in paragraphs])
            return f"Texto encontrado en {url}", {'text': full_text[:2000]} # Limitar a 2000 chars
    except Exception as e:
        return f"Error buscando texto: {e}", {}

def search_image(topic):
    try:
        with DDGS() as ddgs:
            results = list(ddgs.images(topic, max_results=1))
            if not results: return "No se encontraron imágenes.", {}
            image_url = results[0]['image']
            response = requests.get(image_url, stream=True, timeout=10)
            img_path = os.path.join(WORKSPACE_DIR, f"{topic.replace(' ', '_')}.jpg")
            with open(img_path, 'wb') as f:
                f.write(response.content)
            return f"Imagen descargada como {os.path.basename(img_path)}", {'image_path': img_path}
    except Exception as e:
        return f"Error buscando imagen: {e}", {}

def analyze_text(text):
    if not text: return "No hay texto para analizar.", {}
    stop_words = set(stopwords.words('spanish'))
    word_tokens = word_tokenize(text.lower())
    filtered_words = [w for w in word_tokens if w.isalpha() and w not in stop_words]
    keywords = [word for word, _ in Counter(filtered_words).most_common(5)]
    return "Análisis de texto completado.", {'keywords': keywords}

def analyze_image(image_path):
    if not image_path or not os.path.exists(image_path): return "No hay imagen para analizar.", {}
    img = cv2.imread(image_path)
    height, width, _ = img.shape
    blob = cv2.dnn.blobFromImage(img, 1/255.0, (416, 416), swapRB=True, crop=False)
    net.setInput(blob)
    layer_names = net.getUnconnectedOutLayersNames()
    outputs = net.forward(layer_names)
    
    objects = []
    for output in outputs:
        for detection in output:
            scores = detection[5:]
            class_id = np.argmax(scores)
            confidence = scores[class_id]
            if confidence > 0.5:
                objects.append(classes[class_id])
    
    unique_objects = list(set(objects))
    return "Análisis de imagen completado.", {'objects': unique_objects}

def synthesize(context):
    text_part = f"Del texto analizado, las palabras clave son: {', '.join(context.get('keywords', ['N/A']))}."
    image_part = f"En la imagen, se detectaron los siguientes objetos: {', '.join(context.get('objects', ['N/A']))}."
    summary = f"SÍNTESIS DE LA INVESTIGACIÓN:\n{text_part}\n{image_part}"
    return "Informe final generado.", {'synthesis': summary}

# --- Rutas del API ---
@app.route('/ai_workspace/<path:filename>')
def serve_workspace_file(filename):
    return send_from_directory(WORKSPACE_DIR, filename)

def get_state_vector(context):
    return np.array([
        1 if context.get('text') else 0,
        1 if context.get('image_path') else 0,
        1 if context.get('keywords') else 0,
        1 if context.get('objects') else 0
    ])

def execute_step(command):
    global research_context
    state = get_state_vector(research_context)
    action_idx, decision_type, reason = agent.act(state, command)
    action_name = ACTIONS[action_idx]
    
    topic = extract_topic(command)
    result_msg, updates = "", {}

    if action_name == "BUSCAR_TEXTO": result_msg, updates = search_text(topic)
    elif action_name == "BUSCAR_IMAGEN": result_msg, updates = search_image(topic)
    elif action_name == "ANALIZAR_TEXTO": result_msg, updates = analyze_text(research_context.get('text'))
    elif action_name == "ANALIZAR_IMAGEN": result_msg, updates = analyze_image(research_context.get('image_path'))
    elif action_name == "SINTETIZAR": result_msg, updates = synthesize(research_context)

    research_context.update(updates)
    
    return {
        'action_name': action_name,
        'decision_type': decision_type,
        'reason': reason,
        'result': {
            'message': result_msg,
            'text_summary': research_context.get('text', '')[:300] + '...', # Snippet
            'image_path': research_context.get('image_path'),
            'synthesis': research_context.get('synthesis')
        },
        'task_complete': action_name == "SINTETIZAR"
    }

@app.route('/start_task', methods=['POST'])
def start_task():
    global research_context, last_step_data
    research_context = {}
    data = request.json
    command = data['command']
    
    response_data = execute_step(command)
    
    last_step_data = {
        'state': get_state_vector({}),
        'action': list(ACTIONS.keys())[list(ACTIONS.values()).index(response_data['action_name'])]
    }
    return jsonify(response_data)

@app.route('/feedback_and_continue', methods=['POST'])
def feedback_and_continue():
    global last_step_data, research_context
    data = request.json
    reward = data['reward']
    command = data['command']

    current_state = get_state_vector(research_context)
    done = last_step_data.get('task_complete', False)
    
    # Entrenar con la experiencia anterior
    agent.remember(last_step_data['state'], last_step_data['action'], reward, current_state, done)
    agent.replay(16)
    
    response_data = execute_step(command)

    last_step_data = {
        'state': current_state,
        'action': list(ACTIONS.keys())[list(ACTIONS.values()).index(response_data['action_name'])],
        'task_complete': response_data['task_complete']
    }
    return jsonify(response_data)


if __name__ == '__main__':
    print("\nServidor de IA Investigador iniciado en http://127.0.0.1:5000")
    app.run(port=5000)