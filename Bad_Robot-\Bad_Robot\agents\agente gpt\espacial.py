import random

class MisionEspacial:
    def __init__(self):
        self.oxigeno = 100
        self.energia = 100
        self.exito = 0

    def obtener_estado(self):
        return {"oxigeno": self.oxigeno, "energia": self.energia}

    def aplicar_accion(self, agente_id, accion):
        if accion == "reparar":
            self.energia -= 5
            self.oxigeno -= 3
            self.exito += random.choice([0, 1])

    def actualizar(self):
        self.oxigeno -= 1
        self.energia -= 1

    def mostrar_resultados(self):
        print(f"Éxito misión: {self.exito}, oxígeno: {self.oxigeno}, energía: {self.energia}")
