import os
from dotenv import load_dotenv
from flask import Flask, render_template
from flask_socketio import SocketIO
import openai
import face_recognition
import pyttsx3
import speech_recognition as sr
from emotions import EmotionRecognizer

# Cargar variables de entorno y configuración
load_dotenv()
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
socketio = SocketIO(app, cors_allowed_origins="*")
openai.api_key = os.getenv('OPENAI_API_KEY')

# Inicialización de módulos
emotion_recognizer = EmotionRecognizer()
voice_recognizer = sr.Recognizer()
engine = pyttsx3.init()
voices = engine.getProperty('voices')
engine.setProperty('voice', voices[0].id)  # Cambiar por voz en español si disponible

# Emociones por sesión
user_emotions = {}        # emociones faciales
user_voice_emotions = {}  # emociones vocales

# Base de datos de rostros conocidos
known_face_encodings = []
known_face_names = []

def load_known_faces():
    for filename in os.listdir('known_faces'):
        image = face_recognition.load_image_file(f"known_faces/{filename}")
        encoding = face_recognition.face_encodings(image)[0]
        known_face_encodings.append(encoding)
        known_face_names.append(filename.split('.')[0])

load_known_faces()

@app.route('/')
def home():
    return render_template('index.html')

@socketio.on('face_capture')
def handle_face_capture(data):
    try:
        image_data = data.get('image')
        if not image_data:
            socketio.emit('error', {'text': 'No se recibió imagen'})
            return

        # Aquí deberías decodificar y analizar la imagen con OpenCV
        result = emotion_recognizer.analyze_face(image_data)
        user_emotions[data['sid']] = result
        socketio.emit('face_processed', {'status': 'success', 'emotion': result})
    except Exception as e:
        socketio.emit('error', {'text': str(e)})

@socketio.on('voice_message')
def handle_voice(data):
    try:
        result = emotion_recognizer.analyze_voice(data['audio'])
        user_voice_emotions[data['sid']] = result
        socketio.emit('voice_emotion_processed', {'status': 'success', 'emotion': result})
    except Exception as e:
        socketio.emit('error', {'text': str(e)})

@socketio.on('message')
def handle_message(data):
    try:
        user_sid = data.get('sid')
        message = data.get('message')
        
        # Preparar mensaje con contexto emocional si existe
        emotion_contexts = []
        if user_sid in user_emotions:
            e = user_emotions[user_sid]
            emotion_contexts.append(f"emoción facial: {e['emotion']} (confianza {e.get('confidence', 0):.2f})")
        if user_sid in user_voice_emotions:
            e = user_voice_emotions[user_sid]
            emotion_contexts.append(f"emoción vocal: {e['emotion']} (confianza {e.get('confidence', 0):.2f})")

        context = " ".join(emotion_contexts)

        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": f"Eres un asistente AGI. {context}"},
                {"role": "user", "content": message}
            ]
        )

        reply = response.choices[0].message['content']
        engine.say(reply)
        engine.runAndWait()

        socketio.emit('response', {'text': reply})
    except Exception as e:
        socketio.emit('error', {'text': str(e)})

if __name__ == '__main__':
    print("🚀 AGI Assistant ejecutándose en http://localhost:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
