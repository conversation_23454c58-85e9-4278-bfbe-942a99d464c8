"""Resource Modified

Revision ID: 2f97c068fab9
Revises: a91808a89623
Create Date: 2023-06-02 13:13:21.670935

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2f97c068fab9'
down_revision = 'a91808a89623'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('resources', sa.Column('agent_id', sa.Integer(), nullable=True))
    op.drop_column('resources', 'project_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('resources', sa.Column('project_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_column('resources', 'agent_id')
    # ### end Alembic commands ###
