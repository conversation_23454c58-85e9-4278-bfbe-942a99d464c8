# 🤖 Agente Evolutivo Multiagente

Sistema compuesto por:
- Un **Agente Robot** que percibe el entorno y actúa.
- Un **Agente Servidor** que predice, coordina y evoluciona.
- Comunicación mediante **MQTT**.

## 🧠 Funcionalidades

- Percepción ambiental (temperatura, movimiento, sonido).
- Predicción de temperatura futura usando aprendizaje automático.
- Comunicación entre agentes vía MQTT.
- Adaptación del modelo predictivo en tiempo real.
- Autoevaluación básica del sistema.

## 📦 Requisitos

Asegúrate de tener instalado:

- Python 3.8+
- <PERSON><PERSON><PERSON><PERSON> (Broker MQTT)
- Librerías: `paho-mqtt`, `numpy`, `scikit-learn`

Instala las dependencias:

```bash
pip install -r requirements.txt