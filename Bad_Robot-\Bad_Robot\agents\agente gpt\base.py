import random
import json
import os
from agentes.prediccion import predecir_estado
from sklearn.linear_model import LinearRegression
import numpy as np

modelo = LinearRegression()

def entrenar_modelo(precios):
    if len(precios) < 10:
        return  # no hay suficientes datos
    X = np.arange(len(precios)).reshape(-1, 1)
    y = np.array(precios)
    modelo.fit(X, y)

def predecir_precio_siguiente(precios):
    if len(precios) < 2:
        return precios[-1] if precios else 0
    X = np.array([[len(precios)]])
    prediccion = modelo.predict(X)
    return prediccion[0]


class Agente:
    def __init__(self, id, entorno, genotipo=None, bus=None):
        self.id = id
        self.entorno = entorno
        self.bus = bus
        self.mensajes = []
        self.historial = []
        self.genotipo = genotipo if genotipo else {
            "tipo": random.choice(["inversor", "especulador", "conservador"]),
            "agresividad": random.uniform(0.1, 1.0),
            "paciencia": random.uniform(0.1, 1.0),
        }
        os.makedirs("logs", exist_ok=True)
        self.log_file = f"logs/agente_{self.id}.json"

    def decidir(self, observacion):
        pred = predecir_estado(observacion)
        noticias = observacion.get("noticias", {})

        decision = "mantener"

        if self.genotipo["tipo"] == "inversor":
            if pred.get("tendencia") == "alcista" and noticias.get("positiva", False):
                decision = "comprar"
            elif pred.get("tendencia") == "bajista":
                decision = "vender"
        elif self.genotipo["tipo"] == "especulador":
            if random.random() < self.genotipo["agresividad"]:
                decision = random.choice(["comprar", "vender"])
        elif self.genotipo["tipo"] == "conservador":
            if pred.get("tendencia") == "bajista" and noticias.get("negativa", False):
                decision = "vender"

        self.historial.append({"observacion": observacion, "prediccion": pred, "accion": decision})
        self._guardar_log(observacion, pred, decision)
        return decision

    def _guardar_log(self, obs, pred, accion):
        entrada = {
            "observacion": obs,
            "prediccion": pred,
            "accion": accion,
            "genotipo": self.genotipo
        }
        with open(self.log_file, "a") as f:
            f.write(json.dumps(entrada) + "\n")
