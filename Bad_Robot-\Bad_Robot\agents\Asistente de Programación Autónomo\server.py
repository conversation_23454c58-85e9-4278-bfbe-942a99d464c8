from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import numpy as np
import subprocess
import re
from dqn_agent_coder import CoderDQNAgent

# --- Configuración ---
app = Flask(__name__)
CORS(app)
WORKSPACE_DIR = os.path.abspath("ai_workspace")
if not os.path.exists(WORKSPACE_DIR): os.makedirs(WORKSPACE_DIR)

# --- IA y Acciones ---
ACTIONS = { 0: "LEER_CODIGO", 1: "ESCRIBIR_CODIGO", 2: "EJECUTAR_Y_CORREGIR", 3: "CREAR_NUEVO_CODIGO" }
STATE_SIZE = 5 # cmd_hash, code_hash, stdout_hash, stderr_hash, has_error
ACTION_SIZE = len(ACTIONS)
agent = CoderDQNAgent(STATE_SIZE, ACTION_SIZE)

# Estado global simple para la demo
last_step_data = {}
last_action_result = {}

# --- Funciones de Sandbox y Sistema de Archivos ---
def secure_path(filename):
    if not filename: return None
    safe_filename = os.path.normpath(filename).lstrip('.\\/')
    full_path = os.path.join(WORKSPACE_DIR, safe_filename)
    if os.path.commonpath([full_path, WORKSPACE_DIR]) != WORKSPACE_DIR:
        raise PermissionError("Acceso denegado: intento de salir del workspace.")
    return full_path

def extract_filename(command):
    match = re.search(r"([a-zA-Z0-9_.-]+\.py)", command)
    return match.group(1) if match else "temp_script.py"

# --- "Motor Cognitivo" Simulado para Corregir/Crear Código ---
# En un sistema real, esto sería un LLM (como GPT). Aquí lo simulamos con reglas.
def pseudo_llm_fix_code(code, error_message):
    """Simula un LLM que intenta corregir código basado en un error."""
    new_code = f"# IA: Intento de corregir el error: {error_message.splitlines()[-1]}\n"
    if "ZeroDivisionError" in error_message:
        new_code += re.sub(r"(\w+\s*/\s*0)", r"(\1) # IA: Añadido check de división por cero", code, flags=re.IGNORECASE)
        new_code = re.sub(r"(\w+\s*/\s*\w+)", r"try:\n    \1\nexcept ZeroDivisionError:\n    print('Error: División por cero detectada.')", code)
    elif "is not defined" in error_message:
        var_name = re.search(r"name '(\w+)' is not defined", error_message).group(1)
        new_code += f"{var_name} = None # IA: Variable inicializada para evitar NameError\n" + code
    else:
        new_code += code + "\n# IA: No se encontró una corrección automática. Añadiendo un print de depuración."
        new_code += "\nprint('Debug: No se pudo aplicar una corrección automática.')"
    return new_code

def pseudo_llm_create_code(command):
    """Simula un LLM que crea código nuevo."""
    if "hola mundo" in command:
        return "print('Hola, Mundo!')"
    if "sumar" in command:
        return "a = 5\nb = 10\nprint(f'La suma es: {a+b}')"
    return "# IA: No entendí la petición para crear código.\npass"

# --- Lógica de Acciones ---
def execute_action(action_name, params):
    filename = params.get('filename')
    result = {'filename': filename, 'code_content': '', 'stdout': '', 'stderr': '', 'message': ''}
    
    try:
        if action_name == "LEER_CODIGO":
            path = secure_path(filename)
            with open(path, 'r') as f: result['code_content'] = f.read()
            result['message'] = f"Leído el archivo '{filename}'."
        
        elif action_name == "ESCRIBIR_CODIGO":
            path = secure_path(filename)
            with open(path, 'w') as f: f.write(params['code'])
            result['code_content'] = params['code']
            result['message'] = f"Modificado el archivo '{filename}'."

        elif action_name == "EJECUTAR_Y_CORREGIR":
            path = secure_path(filename)
            # 1. Ejecutar el código actual
            proc = subprocess.run(['python', path], capture_output=True, text=True, timeout=5)
            result['stdout'] = proc.stdout
            result['stderr'] = proc.stderr
            
            # 2. Si hay error, intentar corregirlo
            if proc.returncode != 0:
                result['message'] = f"Ejecución de '{filename}' falló. Intentando corregir."
                with open(path, 'r') as f: current_code = f.read()
                fixed_code = pseudo_llm_fix_code(current_code, proc.stderr)
                
                # Escribir el código corregido
                with open(path, 'w') as f: f.write(fixed_code)
                result['code_content'] = fixed_code
            else:
                result['message'] = f"Ejecución de '{filename}' exitosa."
                with open(path, 'r') as f: result['code_content'] = f.read()

        elif action_name == "CREAR_NUEVO_CODIGO":
            new_code = pseudo_llm_create_code(params['command'])
            path = secure_path(filename)
            with open(path, 'w') as f: f.write(new_code)
            result['code_content'] = new_code
            result['message'] = f"Creado nuevo script '{filename}'."

    except Exception as e:
        result['stderr'] = str(e)
        result['message'] = f"Error en el servidor al ejecutar la acción: {e}"
        
    return result

# --- Rutas del API ---
def get_next_action(command, last_result):
    state = agent.get_state_representation(command, last_result)
    last_error = last_result.get('stderr')
    action_idx, decision_type, reason = agent.act(state, command, last_error)
    action_name = ACTIONS[action_idx]
    
    # Preparar parámetros para la acción
    filename = extract_filename(command)
    params = {'filename': filename, 'command': command}
    
    return {
        'action_name': action_name,
        'action_params': params,
        'decision_type': decision_type,
        'reason': reason,
        'requires_confirmation': action_name == "EJECUTAR_Y_CORREGIR",
        'state_for_memory': state
    }

@app.route('/start_task', methods=['POST'])
def start_task():
    global last_step_data, last_action_result
    data = request.json
    command = data['command']
    
    # Estado inicial
    last_action_result = {}
    response_data = get_next_action(command, last_action_result)
    
    # Si no requiere confirmación, ejecutarla directamente
    if not response_data['requires_confirmation']:
        result = execute_action(response_data['action_name'], response_data['action_params'])
        response_data['result'] = result
        last_action_result = result
    
    last_step_data = {
        'state': response_data.pop('state_for_memory'),
        'action': list(ACTIONS.keys())[list(ACTIONS.values()).index(response_data['action_name'])]
    }
    return jsonify(response_data)


@app.route('/feedback_and_continue', methods=['POST'])
def feedback_and_continue():
    global last_step_data, last_action_result
    data = request.json
    reward = data['reward']
    command = data['command']
    confirmation_data = data.get('confirmation_data')

    # Si se está confirmando una acción, ejecutarla ahora
    if confirmation_data:
        action_to_run = confirmation_data['action_name']
        params_to_run = confirmation_data['action_params']
        result = execute_action(action_to_run, params_to_run)
        last_action_result = result
    
    # Obtener el siguiente estado y entrenar con la experiencia anterior
    next_state = agent.get_state_representation(command, last_action_result)
    done = False # La IA podría aprender a determinar esto en el futuro
    agent.remember(last_step_data['state'], last_step_data['action'], reward, next_state, done)
    agent.replay(16)

    # Decidir la próxima acción
    response_data = get_next_action(command, last_action_result)

    # Ejecutarla si no requiere confirmación
    if not response_data['requires_confirmation']:
        result = execute_action(response_data['action_name'], response_data['action_params'])
        response_data['result'] = result
        last_action_result = result
    
    last_step_data = {
        'state': response_data.pop('state_for_memory'),
        'action': list(ACTIONS.keys())[list(ACTIONS.values()).index(response_data['action_name'])]
    }
    return jsonify(response_data)

if __name__ == '__main__':
    print("*"*60)
    print("ADVERTENCIA DE SEGURIDAD EXTREMA")
    print(f"La IA operará en '{WORKSPACE_DIR}' y PUEDE EJECUTAR CÓDIGO.")
    print("Úselo bajo su propio riesgo y en un entorno controlado.")
    print("*"*60)
    app.run(port=5000)