# app.py - Versión mejorada y corregida
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO
import openai
import os
from dotenv import load_dotenv
import speech_recognition as sr
from io import BytesIO
import numpy as np
from PIL import Image
import base64
import cv2
import requests
import json
import threading
import time
from werkzeug.utils import secure_filename

# Importar módulos locales
from EmotionRecognizer import EmotionRecognizer
from voice_analysis import VoiceEmotionAnalyzer

# Cargar variables de entorno
load_dotenv()

# Configuración inicial de la aplicación
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'secret_key_default')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

# Configuración de OpenAI
openai.api_key = os.getenv('OPENAI_API_KEY')
if not openai.api_key:
    print("⚠️  Warning: OPENAI_API_KEY no encontrada en variables de entorno")

# Configuración de archivos permitidos
ALLOWED_EXTENSIONS = {'wav', 'mp3', 'ogg', 'webm', 'm4a'}
UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Inicializar componentes
emotion_recognizer = EmotionRecognizer()
voice_analyzer = VoiceEmotionAnalyzer()
voice_recognizer = sr.Recognizer()

# Configurar reconocedor de voz
voice_recognizer.energy_threshold = 300
voice_recognizer.dynamic_energy_threshold = True
voice_recognizer.pause_threshold = 0.8

def allowed_file(filename):
    """Verificar si el archivo tiene una extensión permitida"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def safe_openai_request(messages, max_retries=3):
    """Realizar solicitud a OpenAI con manejo de errores y reintentos"""
    for attempt in range(max_retries):
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message['content']
        except openai.error.RateLimitError:
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Backoff exponencial
                continue
            return "Error: Límite de rate excedido. Intenta más tarde."
        except openai.error.APIError as e:
            return f"Error de API de OpenAI: {str(e)}"
        except openai.error.InvalidRequestError as e:
            return f"Solicitud inválida: {str(e)}"
        except Exception as e:
            return f"Error inesperado: {str(e)}"

# Rutas principales
@app.route('/')
def index():
    """Página principal"""
    return render_template('index.html')

@app.route('/health')
def health_check():
    """Endpoint de salud para monitoreo"""
    return jsonify({
        'status': 'healthy',
        'openai_configured': bool(openai.api_key),
        'timestamp': time.time()
    })

# API para transcripción de audio
@app.route('/api/transcribe', methods=['POST'])
def transcribe_audio():
    """Transcribir audio a texto"""
    if 'audio' not in request.files:
        return jsonify({'error': 'No se proporcionó archivo de audio'}), 400
    
    audio_file = request.files['audio']
    
    if audio_file.filename == '':
        return jsonify({'error': 'Archivo sin nombre'}), 400
    
    if not allowed_file(audio_file.filename):
        return jsonify({'error': 'Tipo de archivo no permitido'}), 400
    
    try:
        # Guardar archivo temporalmente
        filename = secure_filename(audio_file.filename)
        temp_path = os.path.join(UPLOAD_FOLDER, f"temp_{int(time.time())}_{filename}")
        audio_file.save(temp_path)
        
        # Convertir a formato compatible si es necesario
        try:
            with sr.AudioFile(temp_path) as source:
                # Ajustar para ruido ambiente
                voice_recognizer.adjust_for_ambient_noise(source, duration=0.5)
                audio = voice_recognizer.record(source)
            
            # Intentar reconocimiento con Google
            text = voice_recognizer.recognize_google(audio, language='es-ES')
            
            # Limpiar archivo temporal
            os.remove(temp_path)
            
            return jsonify({
                'text': text,
                'confidence': 0.95  # Google no proporciona confianza
            })
            
        except sr.UnknownValueError:
            # Intentar con otros servicios si Google falla
            try:
                text = voice_recognizer.recognize_sphinx(audio, language='es-ES')
                os.remove(temp_path)
                return jsonify({
                    'text': text,
                    'confidence': 0.7
                })
            except:
                os.remove(temp_path)
                return jsonify({'error': 'No se pudo entender el audio'}), 400
                
    except sr.RequestError as e:
        return jsonify({'error': f"Error en el servicio de reconocimiento: {e}"}), 500
    except Exception as e:
        # Limpiar archivo si existe
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.remove(temp_path)
        return jsonify({'error': f"Error procesando audio: {str(e)}"}), 500

# API para análisis de emociones faciales
@app.route('/analyze_emotion', methods=['POST'])
def analyze_emotion():
    """Analizar emoción en imagen facial"""
    try:
        data = request.get_json()
        if not data or 'image' not in data:
            return jsonify({'error': 'No se proporcionaron datos de imagen'}), 400
        
        # Validar formato de imagen
        if not data['image'].startswith('data:image/'):
            return jsonify({'error': 'Formato de imagen inválido'}), 400
        
        # Decodificar imagen base64
        try:
            header, encoded = data['image'].split(",", 1)
            binary_data = base64.b64decode(encoded)
        except ValueError:
            return jsonify({'error': 'Error decodificando imagen base64'}), 400
        
        # Convertir a formato que pueda procesar OpenCV
        try:
            image_array = np.frombuffer(binary_data, dtype=np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if image is None:
                return jsonify({'error': 'No se pudo decodificar la imagen'}), 400
                
        except Exception as e:
            return jsonify({'error': f'Error procesando imagen: {str(e)}'}), 400
        
        # Analizar emoción
        result = emotion_recognizer.analyze_face(image)
        
        # Validar resultado
        if not result or 'emotion' not in result:
            return jsonify({
                'emotion': 'neutral',
                'confidence': 0.0,
                'emotion_icon': '😐',
                'error': 'No se pudo analizar la imagen'
            })
        
        return jsonify(result)
        
    except Exception as e:
        print(f"Error en análisis de emoción: {e}")
        return jsonify({
            'error': str(e),
            'emotion': 'neutral',
            'confidence': 0.0,
            'emotion_icon': '😐'
        }), 500

# API para análisis de emociones por voz
@app.route('/api/analyze_voice_emotion', methods=['POST'])
def analyze_voice_emotion():
    """Analizar emoción en archivo de audio"""
    if 'audio' not in request.files:
        return jsonify({'error': 'No se proporcionó archivo de audio'}), 400
    
    audio_file = request.files['audio']
    
    if not allowed_file(audio_file.filename):
        return jsonify({'error': 'Tipo de archivo no permitido'}), 400
    
    try:
        # Guardar archivo temporalmente
        filename = secure_filename(audio_file.filename)
        temp_path = os.path.join(UPLOAD_FOLDER, f"voice_{int(time.time())}_{filename}")
        audio_file.save(temp_path)
        
        # Analizar emoción vocal
        result = voice_analyzer.analyze_voice(temp_path)
        
        # Limpiar archivo temporal
        os.remove(temp_path)
        
        return jsonify(result)
        
    except Exception as e:
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.remove(temp_path)
        return jsonify({'error': f"Error analizando emoción vocal: {str(e)}"}), 500

# WebSocket para el chat
@socketio.on('connect')
def handle_connect():
    """Manejar conexión de cliente"""
    print(f"Cliente conectado: {request.sid}")
    socketio.emit('status', {'message': 'Conectado al servidor AGI'})

@socketio.on('disconnect')
def handle_disconnect():
    """Manejar desconexión de cliente"""
    print(f"Cliente desconectado: {request.sid}")

@socketio.on('message')
def handle_message(data):
    """Manejar mensajes de chat"""
    try:
        print(f"Mensaje recibido de {request.sid}: {data}")
        
        if not data or 'message' not in data:
            socketio.emit('error', {'text': 'Mensaje vacío'})
            return
        
        message = data['message'].strip()
        if not message:
            socketio.emit('error', {'text': 'Mensaje vacío'})
            return
        
        # Preparar contexto para OpenAI
        messages = [
            {
                "role": "system", 
                "content": "Eres un asistente AGI avanzado que combina análisis emocional y conversación natural. Responde de manera empática y útil."
            },
            {
                "role": "user", 
                "content": message
            }
        ]
        
        # Incluir contexto emocional si está disponible
        if 'emotion' in data:
            emotion_context = f"El usuario muestra emoción: {data['emotion']} con confianza {data.get('confidence', 0):.2f}"
            messages[0]["content"] += f" {emotion_context}"
        
        # Generar respuesta
        reply = safe_openai_request(messages)
        socketio.emit('response', {'text': reply})
        
    except Exception as e:
        error_msg = f"Error procesando mensaje: {str(e)}"
        print(error_msg)
        socketio.emit('error', {'text': error_msg})

@socketio.on('face_capture')
def handle_face_capture(data):
    """Manejar captura facial"""
    try:
        print(f"Captura facial recibida de {request.sid}")
        
        if not data or 'image' not in data:
            socketio.emit('error', {'text': 'No se recibieron datos de imagen'})
            return
        
        # Procesar imagen en hilo separado para no bloquear
        def process_image():
            try:
                # Decodificar y analizar imagen
                header, encoded = data['image'].split(",", 1)
                binary_data = base64.b64decode(encoded)
                image_array = np.frombuffer(binary_data, dtype=np.uint8)
                image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                
                result = emotion_recognizer.analyze_face(image)
                socketio.emit('face_processed', {
                    'status': 'success',
                    'emotion': result
                })
            except Exception as e:
                socketio.emit('error', {'text': f"Error procesando rostro: {str(e)}"})
        
        # Ejecutar en hilo separado
        threading.Thread(target=process_image, daemon=True).start()
        
    except Exception as e:
        socketio.emit('error', {'text': f"Error en captura facial: {str(e)}"})

# Manejo de errores globales
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint no encontrado'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Error interno del servidor'}), 500

@app.errorhandler(413)
def too_large(error):
    return jsonify({'error': 'Archivo demasiado grande'}), 413

# Limpieza de archivos temporales
def cleanup_temp_files():
    """Limpiar archivos temporales antiguos"""
    try:
        current_time = time.time()
        for filename in os.listdir(UPLOAD_FOLDER):
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            if os.path.isfile(filepath):
                # Eliminar archivos más antiguos de 1 hora
                if current_time - os.path.getmtime(filepath) > 3600:
                    os.remove(filepath)
                    print(f"Archivo temporal eliminado: {filename}")
    except Exception as e:
        print(f"Error limpiando archivos temporales: {e}")

# Programar limpieza cada hora
def schedule_cleanup():
    """Programar limpieza periódica"""
    cleanup_temp_files()
    threading.Timer(3600, schedule_cleanup).start()  # Cada hora

# Inicialización
if __name__ == '__main__':
    print("🚀 Iniciando servidor AGI...")
    print(f"📁 Directorio de uploads: {UPLOAD_FOLDER}")
    print(f"🤖 OpenAI configurado: {'✅' if openai.api_key else '❌'}")
    
    # Iniciar limpieza programada
    schedule_cleanup()
    
    # Ejecutar servidor
    try:
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=int(os.getenv('PORT', 5000)),
            debug=os.getenv('DEBUG', 'False').lower() == 'true',
            allow_unsafe_werkzeug=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Servidor detenido por el usuario")
    except Exception as e:
        print(f"❌ Error iniciando servidor: {e}")
