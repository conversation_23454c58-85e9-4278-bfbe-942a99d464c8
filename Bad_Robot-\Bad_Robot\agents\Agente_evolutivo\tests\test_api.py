import pytest
import json
from src.app import create_app
from src import task_service

# --- Fixture de Pytest ---
# Esto crea una instancia de nuestra app para cada test
@pytest.fixture
def client():
    # Usamos una base de datos en memoria para las pruebas
    task_service.DATABASE_URL = ":memory:"
    
    # Creamos la tabla antes de cada test
    conn = task_service.get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        completed BOOLEAN NOT NULL
    )
    ''')
    conn.commit()
    conn.close()

    app = create_app()
    with app.test_client() as client:
        yield client

# --- Pruebas ---

def test_get_tasks_empty(client):
    """Prueba que GET /tasks devuelve una lista vacía al inicio."""
    rv = client.get('/tasks')
    assert rv.status_code == 200
    assert rv.json == []

def test_create_task(client):
    """Prueba la creación de una tarea."""
    rv = client.post('/tasks', json={'title': 'Test a la API'})
    assert rv.status_code == 201
    assert rv.json['title'] == 'Test a la API'
    assert rv.json['completed'] is False
    assert 'id' in rv.json

def test_create_task_invalid(client):
    """Prueba que no se puede crear una tarea sin título."""
    rv = client.post('/tasks', json={'completed': True})
    assert rv.status_code == 400

def test_get_specific_task(client):
    """Prueba obtener una tarea específica por ID."""
    # Primero creamos una tarea
    rv_post = client.post('/tasks', json={'title': 'Mi Tarea'})
    task_id = rv_post.json['id']

    # Luego la obtenemos
    rv_get = client.get(f'/tasks/{task_id}')
    assert rv_get.status_code == 200
    assert rv_get.json['title'] == 'Mi Tarea'

def test_update_task(client):
    """Prueba actualizar una tarea."""
    rv_post = client.post('/tasks', json={'title': 'Tarea Original'})
    task_id = rv_post.json['id']

    rv_put = client.put(f'/tasks/{task_id}', json={'title': 'Tarea Actualizada', 'completed': True})
    assert rv_put.status_code == 200
    assert rv_put.json['title'] == 'Tarea Actualizada'
    assert rv_put.json['completed'] is True

def test_delete_task(client):
    """Prueba eliminar una tarea."""
    rv_post = client.post('/tasks', json={'title': 'Tarea a borrar'})
    task_id = rv_post.json['id']

    # La eliminamos
    rv_delete = client.delete(f'/tasks/{task_id}')
    assert rv_delete.status_code == 204

    # Verificamos que ya no existe
    rv_get = client.get(f'/tasks/{task_id}')
    assert rv_get.status_code == 404