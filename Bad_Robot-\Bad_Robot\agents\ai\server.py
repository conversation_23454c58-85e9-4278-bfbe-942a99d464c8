from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import numpy as np
from dqn_agent_files import FileDQNAgent
import re

# --- Configuración de Seguridad y Entorno ---
app = Flask(__name__)
CORS(app)
WORKSPACE_DIR = os.path.abspath("ai_workspace")
if not os.path.exists(WORKSPACE_DIR):
    os.makedirs(WORKSPACE_DIR)

# --- Configuración de la IA ---
# Las acciones que la IA puede realizar
ACTIONS = { 0: "LIST_FILES", 1: "READ_FILE", 2: "WRITE_FILE" }
STATE_SIZE = 4  # cmd_hash, files_hash, num_files, last_action_result
ACTION_SIZE = len(ACTIONS)
agent = FileDQNAgent(STATE_SIZE, ACTION_SIZE)

# Variable global para mantener el estado entre las llamadas del usuario
last_step_data = {}

# --- Funciones de Sandbox y Sistema de Archivos ---
def secure_path(filename):
    """Garantiza que la ruta del archivo esté dentro del WORKSPACE_DIR."""
    if filename is None: return None
    # Eliminar cualquier intento de subir de directorio (../, ..\, etc.)
    safe_filename = os.path.normpath(filename).lstrip('.\\/')
    full_path = os.path.join(WORKSPACE_DIR, safe_filename)
    
    # Comprobar que la ruta final sigue dentro del workspace
    if os.path.commonpath([full_path, WORKSPACE_DIR]) != WORKSPACE_DIR:
        raise PermissionError("Acceso denegado: intento de salir del workspace.")
    return full_path

def list_files():
    files = os.listdir(WORKSPACE_DIR)
    return "Contenido del directorio:\n" + "\n".join(files), True

def read_file(filename):
    try:
        path = secure_path(filename)
        with open(path, 'r', encoding='utf-8') as f:
            return f.read(), True
    except Exception as e:
        return f"Error al leer '{filename}': {e}", False

def write_file(filename, content=""):
    try:
        path = secure_path(filename)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        return f"Archivo '{filename}' escrito con éxito.", True
    except Exception as e:
        return f"Error al escribir '{filename}': {e}", False

def extract_params(command):
    """Extrae nombres de archivo y contenido del comando del usuario usando regex simples."""
    filename = None
    content = None
    # "lee archivo.txt", "crea reporte.txt"
    match = re.search(r"(?:lee|escribe en|crea|guarda en|de|del archivo|el archivo)\s+([a-zA-Z0-9_.-]+)", command)
    if match:
        filename = match.group(1)
    
    # "escribe 'hola mundo' en saludo.txt"
    match = re.search(r"con el contenido de ([a-zA-Z0-9_.-]+)", command)
    if match: # Si pide escribir con el contenido de otro archivo
        source_filename = match.group(1)
        content, _ = read_file(source_filename) # Leemos el contenido del origen
    else: # Si el contenido está en el propio comando
        match = re.search(r"(?:escribe|guarda)\s+['\"](.*?)['\"]", command)
        if match:
            content = match.group(1)
            
    return filename, content

# --- Rutas del API ---
@app.route('/start_task', methods=['POST'])
def start_task():
    global last_step_data
    data = request.json
    command = data['command']
    
    # Estado inicial
    files_in_dir = os.listdir(WORKSPACE_DIR)
    state = agent.get_state_representation(command, files_in_dir, True) # True: last action was successful
    
    # La IA elige una acción
    action_idx, decision_type, reason = agent.act(state, command)
    action_name = ACTIONS[action_idx]
    
    # Ejecutar la acción
    filename, content = extract_params(command)
    output, success = execute_action(action_name, filename, content)
    
    # Guardar datos para el paso de entrenamiento
    last_step_data = {
        'state': state,
        'action': action_idx,
        'command': command
    }
    
    return jsonify({
        'action_name': action_name,
        'action_params': filename,
        'decision_type': decision_type,
        'reason': reason,
        'workspace_status': { 'current_path': '/', 'output': output },
        'task_complete': False
    })

@app.route('/feedback_and_continue', methods=['POST'])
def feedback_and_continue():
    global last_step_data
    data = request.json
    reward = data['reward']
    command = data['command']
    
    # Estado siguiente
    files_in_dir = os.listdir(WORKSPACE_DIR)
    # Suponemos que la tarea no ha terminado (done=False)
    # En un sistema real, la IA podría decidir si la tarea está completa
    done = False 
    next_state = agent.get_state_representation(command, files_in_dir, reward > 0)

    # Entrenar con la experiencia completada
    if 'state' in last_step_data:
        agent.remember(last_step_data['state'], last_step_data['action'], reward, next_state, done)
        agent.replay(16) # Entrenar con un pequeño lote

    # Decidir y ejecutar la siguiente acción
    action_idx, decision_type, reason = agent.act(next_state, command)
    action_name = ACTIONS[action_idx]
    filename, content = extract_params(command)
    output, success = execute_action(action_name, filename, content)
    
    # Actualizar los datos para el próximo feedback
    last_step_data = {'state': next_state, 'action': action_idx, 'command': command}

    return jsonify({
        'action_name': action_name,
        'action_params': filename,
        'decision_type': decision_type,
        'reason': reason,
        'workspace_status': { 'current_path': '/', 'output': output },
        'task_complete': done 
    })

def execute_action(action_name, filename, content):
    if action_name == "LIST_FILES":
        return list_files()
    elif action_name == "READ_FILE":
        if not filename: return "Error: No se especificó qué archivo leer.", False
        return read_file(filename)
    elif action_name == "WRITE_FILE":
        if not filename: return "Error: No se especificó dónde escribir.", False
        return write_file(filename, content or f"Contenido generado por la IA para {filename}")
    return "Acción desconocida", False


if __name__ == '__main__':
    print("*"*50)
    print("ADVERTENCIA DE SEGURIDAD")
    print(f"La IA solo operará dentro de la carpeta: {WORKSPACE_DIR}")
    print("Nunca elimine las protecciones de 'secure_path'.")
    print("*"*50)
    print("\nServidor de IA Asistente iniciado en http://127.0.0.1:5000")
    print("Abre el archivo index.html en tu navegador para empezar.")
    app.run(port=5000)