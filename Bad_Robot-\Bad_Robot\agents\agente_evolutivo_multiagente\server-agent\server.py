import paho.mqtt.client as mqtt
import json
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import random
import os
import numpy as np
import pickle

class ServerAgent:
    def __init__(self):
        self.model_path = "server_agent/model.pkl"
        self.model = self.cargar_modelo() or LinearRegression()
        self.isolation_forest = self.cargar_modelo("model_anomaly.pkl") or IsolationForest(contamination=0.1)
        self.scaler = StandardScaler()
        self.X_train_temp = []
        self.y_train_temp = []

        self.X_train_anomaly = []

        self.client = mqtt.Client("server_agent")
        self.client.connect("localhost", 5000)
        self.client.on_message = self.on_message
        self.client.subscribe("topic/sensores")

    def cargar_modelo(self, filename):
        if os.path.exists(f"server_agent/{filename}"):
            with open(f"server_agent/{filename}", "rb") as f:
                return pickle.load(f)
        return None

    def guardar_modelo(self, model, filename):
        with open(f"server_agent/{filename}", "wb") as f:
            pickle.dump(self.model, f)

    def on_message(self, client, userdata, msg):
        if msg.topic == "topic/sensores":
            data = json.loads(msg.payload)
            print(f"[SERVIDOR] Datos recibidos: {data}")
            self.procesar_datos(data)

    def procesar_datos(self, data):
        sensores = data["datos"]
        temp = sensores["temperatura"]
        hora = int(sensores["hora"].split(" ")[1].split(":")[0])
        movimiento = 1 if sensores["movimiento"] else 0
        sonido = {"silencio": 0, "voz": 1, "alarma": 2}[sensores["sonido"]]

        # Entrenamiento regresión temperatura
        self.X_train_temp.append([[hora]])
        self.y_train_temp.append(temp)
        if len(self.X_train_temp) > 5:
            X = np.array([x[0] for x in self.X_train_temp])
            y = np.array(self.y_train_temp)
            self.model_temp.fit(X, y)
            self.guardar_modelo(self.model_temp, "model_temp.pkl")
            print("[MODELO] Modelo de temperatura actualizado")

            next_hour = (hora + 1) % 24
            pred_temp = self.model_temp.predict([[next_hour]])
            print(f"[PREDICCIÓN] Temp. esperada a las {next_hour}:00 -> {pred_temp[0]:.1f}°C")

            accion = "activar_calefaccion" if pred_temp < 18 else "esperar"
            self.client.publish("topic/acciones", json.dumps({"accion": accion}))

        # Entrenamiento detección de anomalías
        features = [[temp, movimiento, sonido]]
        scaled_features = self.scaler.fit_transform(features)
        self.X_train_anomaly.extend(scaled_features)
        if len(self.X_train_anomaly) > 10:
            X_anomaly = np.array(self.X_train_anomaly)
            self.isolation_forest.fit(X_anomaly)
            self.guardar_modelo(self.isolation_forest, "model_anomaly.pkl")

            score = self.isolation_forest.score_samples(X_anomaly[-1:])
            if score[0] < -0.7:
                print("[ALERTA] Se detectó una anomalía!")
                self.client.publish("topic/acciones", json.dumps({"accion": "alerta"}))

        # Ejecutar evolución genética
        self.evolucion_genetica(data)

    def evolucion_genetica(self, data):
        # Simulación de población de estrategias
        estrategias = [
            {"accion": "activar_calefaccion", "umbral": 18},
            {"accion": "esperar", "umbral": 20},
            {"accion": "alerta", "umbral": 999}
        ]

        # Evaluación básica de fitness
        temp = data["datos"]["temperatura"]
        mejor_estrategia = min(estrategias, key=lambda e: abs(e["umbral"] - temp))
        print(f"[EVOLUCIÓN] Estrategia seleccionada: {mejor_estrategia['accion']}")

        # Mutación aleatoria
        if random.random() < 0.2:
            idx = random.randint(0, len(estrategias)-1)
            estrategias[idx]["umbral"] += random.uniform(-1, 1)
            print(f"[MUTACIÓN] Umbral ajustado: {estrategias[idx]}")

    

        # Entrenamiento básico
        self.X_train.append([[hora]])
        self.y_train.append(temp)
        if len(self.X_train) > 5:
            X = np.array([x[0] for x in self.X_train])
            y = np.array(self.y_train)
            self.model.fit(X, y)
            self.guardar_modelo()
            print("[MODELO] Modelo actualizado")

            # Predicción para la próxima hora
            next_hour = (hora + 1) % 24
            pred_temp = self.model.predict([[next_hour]])
            print(f"[PREDICCIÓN] Temperatura esperada a las {next_hour}:00 -> {pred_temp[0]:.1f}°C")

            # Enviar acción condicional
            if pred_temp < 18:
                accion = {"accion": "activar_calefaccion"}
            else:
                accion = {"accion": "esperar"}

            self.client.publish("topic/acciones", json.dumps(accion))

    def iniciar(self):
        print("[SERVIDOR] Escuchando datos...")
        self.client.loop_forever()

if __name__ == "__main__":
    servidor = ServerAgent()
    servidor.iniciar()