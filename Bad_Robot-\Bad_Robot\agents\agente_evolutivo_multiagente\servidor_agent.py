import paho.mqtt.client as mqtt
import json
from sklearn.linear_model import LinearRegression
import numpy as np

class ServerAgent:
    def __init__(self):
        self.model = LinearRegression()
        self.X_train = []
        self.y_train = []
        self.client = mqtt.Client("server_agent")
        self.client.connect("broker_address", 1883)
        self.client.on_message = self.on_message
        self.client.subscribe("topic/sensores")

    def on_message(self, client, userdata, msg):
        if msg.topic == "topic/sensores":
            data = json.loads(msg.payload)
            print(f"[SERVIDOR] Datos recibidos: {data}")
            self.procesar_datos(data)

    def procesar_datos(self, data):
        sensores = data["datos"]
        temp = sensores["temperatura"]
        hora = int(sensores["hora"].split(" ")[1].split(":")[0])

        # Entrenamiento básico
        self.X_train.append([[hora]])
        self.y_train.append(temp)
        if len(self.X_train) > 5:
            X = np.array([x[0] for x in self.X_train])
            y = np.array(self.y_train)
            self.model.fit(X, y)
            print("[MODELO] Modelo actualizado")

            # Predicción para la próxima hora
            next_hour = (hora + 1) % 24
            pred_temp = self.model.predict([[next_hour]])
            print(f"[PREDICCIÓN] Temperatura esperada a las {next_hour}:00 -> {pred_temp[0]:.1f}°C")

            # Enviar acción condicional
            if pred_temp < 18:
                accion = {"accion": "activar_calefaccion"}
            else:
                accion = {"accion": "esperar"}

            self.client.publish("topic/acciones", json.dumps(accion))

    def iniciar(self):
        print("[SERVIDOR] Escuchando datos...")
        self.client.loop_forever()

if __name__ == "__main__":
    servidor = ServerAgent()
    servidor.iniciar()