import numpy as np
import random
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON>, <PERSON>, Tu<PERSON>, Optional, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from collections import deque
import uuid

@dataclass
class Message:
    """Mensaje para comunicación entre agentes"""
    sender_id: str
    receiver_id: str
    message_type: str  # 'request', 'response', 'broadcast', 'negotiation'
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1  # 1-5, siendo 5 la máxima prioridad
    requires_response: bool = False
    message_id: str = ""
    
    def __post_init__(self):
        if not self.message_id:
            self.message_id = str(uuid.uuid4())[:8]

@dataclass
class PredictionModel:
    """Modelo de predicción para estados futuros"""
    feature_weights: Dict[str, float]
    temporal_patterns: Dict[str, List[float]]
    accuracy_history: List[float]
    prediction_horizon: int = 5
    
    def __post_init__(self):
        if not self.accuracy_history:
            self.accuracy_history = [0.5]  # Comenzar con precisión neutral

@dataclass
class Prediction:
    """Resultado de una predicción"""
    predicted_state: Dict[str, Any]
    confidence: float
    time_horizon: int
    prediction_timestamp: datetime
    contributing_factors: List[str]
    uncertainty_bounds: Dict[str, Tuple[float, float]] = None
    
    def __post_init__(self):
        if self.uncertainty_bounds is None:
            self.uncertainty_bounds = {}

@dataclass
class PerformanceMetrics:
    """Métricas de rendimiento del agente"""
    success_rate: float = 0.0
    efficiency: float = 0.0
    adaptation_speed: float = 0.0
    resource_usage: float = 0.0
    learning_rate: float = 0.0
    
    def aggregate_score(self) -> float:
        """Calcula un puntaje agregado de rendimiento"""
        return (self.success_rate * 0.3 + 
                self.efficiency * 0.25 + 
                self.adaptation_speed * 0.2 + 
                (1 - self.resource_usage) * 0.15 + 
                self.learning_rate * 0.1)

@dataclass
class Strategy:
    """Estrategia del agente con parámetros evolutivos"""
    exploration_rate: float = 0.1
    learning_coefficient: float = 0.01
    risk_tolerance: float = 0.5
    cooperation_tendency: float = 0.5
    planning_horizon: int = 10
    
    def mutate(self, mutation_strength: float = 0.1):
        """Muta la estrategia con variaciones aleatorias"""
        self.exploration_rate = np.clip(
            self.exploration_rate + np.random.normal(0, mutation_strength), 0, 1)
        self.learning_coefficient = np.clip(
            self.learning_coefficient + np.random.normal(0, mutation_strength/10), 0.001, 0.1)
        self.risk_tolerance = np.clip(
            self.risk_tolerance + np.random.normal(0, mutation_strength), 0, 1)
        self.cooperation_tendency = np.clip(
            self.cooperation_tendency + np.random.normal(0, mutation_strength), 0, 1)
        self.planning_horizon = max(1, 
            self.planning_horizon + int(np.random.normal(0, mutation_strength * 10)))

class CommunicationModule:
    """Módulo de comunicación multi-agente"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.inbox: deque = deque(maxlen=100)
        self.outbox: deque = deque(maxlen=50)
        self.message_history: List[Message] = []
        self.known_agents: Dict[str, Dict[str, Any]] = {}
        self.reputation_scores: Dict[str, float] = {}
        self.communication_protocols: Dict[str, callable] = {
            'request': self._handle_request,
            'response': self._handle_response,
            'broadcast': self._handle_broadcast,
            'negotiation': self._handle_negotiation
        }
        self.network_topology: Dict[str, List[str]] = {}  # Conexiones directas
        self.message_routing_table: Dict[str, str] = {}  # Tabla de enrutamiento
    
    def send_message(self, receiver_id: str, message_type: str, content: Dict[str, Any], 
                    priority: int = 1, requires_response: bool = False) -> str:
        """Envía un mensaje a otro agente"""
        message = Message(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content,
            timestamp=datetime.now(),
            priority=priority,
            requires_response=requires_response
        )
        
        self.outbox.append(message)
        self.message_history.append(message)
        return message.message_id
    
    def receive_message(self, message: Message):
        """Recibe un mensaje de otro agente"""
        self.inbox.append(message)
        self._update_agent_knowledge(message.sender_id, message)
    
    def process_incoming_messages(self) -> List[Dict[str, Any]]:
        """Procesa mensajes entrantes y genera respuestas"""
        responses = []
        
        # Ordenar por prioridad
        messages = sorted(list(self.inbox), key=lambda x: x.priority, reverse=True)
        self.inbox.clear()
        
        for message in messages:
            if message.message_type in self.communication_protocols:
                response = self.communication_protocols[message.message_type](message)
                if response:
                    responses.append(response)
        
        return responses
    
    def _handle_request(self, message: Message) -> Optional[Dict[str, Any]]:
        """Maneja solicitudes de otros agentes"""
        request_type = message.content.get('request_type')
        
        if request_type == 'resource_sharing':
            return self._handle_resource_request(message)
        elif request_type == 'collaboration':
            return self._handle_collaboration_request(message)
        elif request_type == 'information_exchange':
            return self._handle_information_request(message)
        elif request_type == 'prediction_sharing':
            return self._handle_prediction_request(message)
        
        return None
    
    def _handle_response(self, message: Message) -> Optional[Dict[str, Any]]:
        """Maneja respuestas de otros agentes"""
        # Actualizar conocimiento basado en la respuesta
        response_data = message.content
        self._update_reputation(message.sender_id, response_data.get('quality', 0.5))
        
        return {
            'type': 'response_processed',
            'sender': message.sender_id,
            'data': response_data
        }
    
    def _handle_broadcast(self, message: Message) -> Optional[Dict[str, Any]]:
        """Maneja mensajes de difusión"""
        broadcast_type = message.content.get('broadcast_type')
        
        if broadcast_type == 'environment_update':
            return {
                'type': 'environment_intel',
                'data': message.content.get('environment_data', {}),
                'reliability': self.reputation_scores.get(message.sender_id, 0.5)
            }
        elif broadcast_type == 'threat_alert':
            return {
                'type': 'threat_warning',
                'threat_level': message.content.get('threat_level', 0),
                'location': message.content.get('location', 'unknown'),
                'predicted_impact': message.content.get('predicted_impact', 'unknown')
            }
        elif broadcast_type == 'prediction_update':
            return {
                'type': 'prediction_intel',
                'prediction_data': message.content.get('prediction_data', {}),
                'confidence': message.content.get('confidence', 0.5)
            }
        
        return None
    
    def _handle_negotiation(self, message: Message) -> Optional[Dict[str, Any]]:
        """Maneja negociaciones entre agentes"""
        proposal = message.content.get('proposal', {})
        offer_value = proposal.get('value', 0)
        required_effort = proposal.get('effort_required', 1)
        risk_level = proposal.get('risk_level', 0.5)
        
        # Evaluación avanzada de propuesta
        sender_reputation = self.reputation_scores.get(message.sender_id, 0.5)
        benefit_ratio = (offer_value * sender_reputation) / max(required_effort, 0.1)
        risk_penalty = risk_level * 0.3
        
        accept_probability = min(0.9, benefit_ratio * 0.4 - risk_penalty + 0.1)
        
        if random.random() < accept_probability:
            return {
                'type': 'negotiation_response',
                'decision': 'accept',
                'terms': proposal,
                'commitment_level': accept_probability
            }
        else:
            counter_value = offer_value * (1.2 + risk_level * 0.3)
            counter_effort = required_effort * (0.8 - risk_level * 0.2)
            
            return {
                'type': 'negotiation_response',
                'decision': 'counter',
                'counter_proposal': {
                    'value': counter_value,
                    'effort_required': max(0.1, counter_effort),
                    'risk_level': risk_level * 0.8
                }
            }
    
    def _handle_resource_request(self, message: Message) -> Dict[str, Any]:
        """Maneja solicitudes de recursos"""
        requested_amount = message.content.get('amount', 0)
        urgency = message.content.get('urgency', 1)
        resource_type = message.content.get('resource_type', 'generic')
        
        # Decisión basada en múltiples factores
        sender_reputation = self.reputation_scores.get(message.sender_id, 0.5)
        urgency_factor = min(urgency, 2.0) / 2.0
        scarcity_factor = 1.0 - message.content.get('scarcity_level', 0.5)
        
        willingness = (sender_reputation * 0.4 + 
                      urgency_factor * 0.3 + 
                      scarcity_factor * 0.3) * random.uniform(0.8, 1.2)
        
        if willingness > 0.6:
            granted_amount = min(requested_amount, requested_amount * willingness)
            return {
                'type': 'resource_response',
                'granted': True,
                'amount': granted_amount,
                'resource_type': resource_type,
                'conditions': {
                    'return_expected': willingness < 0.8,
                    'future_reciprocity': True
                }
            }
        else:
            return {
                'type': 'resource_response',
                'granted': False,
                'reason': 'insufficient_trust_or_resources',
                'alternative_offer': {
                    'amount': requested_amount * 0.3,
                    'conditions': 'high_priority_only'
                }
            }
    
    def _handle_collaboration_request(self, message: Message) -> Dict[str, Any]:
        """Maneja solicitudes de colaboración"""
        collaboration_type = message.content.get('collaboration_type')
        expected_benefit = message.content.get('expected_benefit', 0)
        duration = message.content.get('duration', 1)
        risk_level = message.content.get('risk_level', 0.5)
        
        # Evaluar beneficio potencial con factores temporales y de riesgo
        sender_reputation = self.reputation_scores.get(message.sender_id, 0.5)
        time_discount = 1.0 / (1.0 + duration * 0.1)  # Descuento temporal
        risk_adjustment = 1.0 - risk_level * 0.4
        
        collaboration_value = (expected_benefit * sender_reputation * 
                             time_discount * risk_adjustment)
        
        interested = collaboration_value > 0.3
        
        return {
            'type': 'collaboration_response',
            'interested': interested,
            'collaboration_value': collaboration_value,
            'proposed_terms': {
                'resource_contribution': min(0.4, collaboration_value * 0.8),
                'expected_share': collaboration_value * 0.6,
                'risk_sharing': min(0.5, risk_level),
                'duration_limit': max(1, duration * 0.8)
            } if interested else None
        }
    
    def _handle_information_request(self, message: Message) -> Dict[str, Any]:
        """Maneja solicitudes de información"""
        info_type = message.content.get('info_type')
        specificity = message.content.get('specificity', 'general')
        
        # Simular intercambio de información inteligente
        shared_info = {}
        reliability_score = self.reputation_scores.get(self.agent_id, 0.7)
        
        if info_type == 'environment_state':
            shared_info = {
                'recent_observations': self._generate_environment_intel(),
                'trend_analysis': self._generate_trend_analysis(),
                'threat_assessment': self._generate_threat_assessment()
            }
        elif info_type == 'strategy_insights':
            shared_info = {
                'performance_patterns': self._generate_performance_patterns(),
                'successful_strategies': self._generate_strategy_recommendations(),
                'risk_factors': self._generate_risk_factors()
            }
        elif info_type == 'prediction_data':
            shared_info = {
                'future_predictions': self._generate_prediction_data(),
                'confidence_intervals': self._generate_confidence_intervals(),
                'uncertainty_factors': self._generate_uncertainty_factors()
            }
        
        return {
            'type': 'information_response',
            'information': shared_info,
            'reliability': reliability_score,
            'information_quality': min(1.0, reliability_score + random.uniform(-0.1, 0.1)),
            'timestamp': datetime.now().isoformat()
        }
    
    def _handle_prediction_request(self, message: Message) -> Dict[str, Any]:
        """Maneja solicitudes de predicciones compartidas"""
        prediction_type = message.content.get('prediction_type', 'general')
        time_horizon = message.content.get('time_horizon', 5)
        
        prediction_data = {
            'prediction_type': prediction_type,
            'time_horizon': time_horizon,
            'predicted_values': self._generate_shared_predictions(prediction_type, time_horizon),
            'confidence_level': random.uniform(0.6, 0.9),
            'contributing_factors': self._generate_prediction_factors(prediction_type),
            'uncertainty_range': self._generate_uncertainty_range()
        }
        
        return {
            'type': 'prediction_response',
            'prediction_data': prediction_data,
            'model_accuracy': random.uniform(0.7, 0.95),
            'data_freshness': 'recent'
        }
    
    def _generate_environment_intel(self) -> Dict[str, Any]:
        """Genera inteligencia sobre el entorno"""
        return {
            'resource_trends': ['increasing', 'stable', 'decreasing'][random.randint(0, 2)],
            'competition_level': random.uniform(0.3, 0.8),
            'opportunities_detected': random.randint(1, 4),
            'environmental_stability': random.uniform(0.4, 0.9)
        }
    
    def _generate_trend_analysis(self) -> Dict[str, Any]:
        """Genera análisis de tendencias"""
        return {
            'short_term_trend': random.choice(['positive', 'neutral', 'negative']),
            'long_term_outlook': random.choice(['optimistic', 'cautious', 'pessimistic']),
            'volatility_index': random.uniform(0.2, 0.7),
            'predictability_score': random.uniform(0.5, 0.9)
        }
    
    def _generate_threat_assessment(self) -> Dict[str, Any]:
        """Genera evaluación de amenazas"""
        return {
            'immediate_threats': random.randint(0, 3),
            'threat_severity': random.uniform(0.1, 0.8),
            'threat_types': random.sample(['resource_scarcity', 'competition', 'environmental', 'systemic'], 
                                        random.randint(1, 3)),
            'mitigation_urgency': random.uniform(0.3, 0.9)
        }
    
    def _generate_performance_patterns(self) -> Dict[str, Any]:
        """Genera patrones de rendimiento"""
        return {
            'success_patterns': {
                'exploration_rate': random.uniform(0.1, 0.3),
                'cooperation_level': random.uniform(0.4, 0.8),
                'risk_tolerance': random.uniform(0.3, 0.7)
            },
            'failure_patterns': {
                'over_exploration': random.uniform(0.4, 0.8),
                'resource_waste': random.uniform(0.2, 0.6),
                'poor_timing': random.uniform(0.1, 0.4)
            }
        }
    
    def _generate_strategy_recommendations(self) -> List[str]:
        """Genera recomendaciones estratégicas"""
        recommendations = [
            'increase_exploration_in_stable_periods',
            'enhance_cooperation_during_resource_abundance',
            'reduce_risk_tolerance_during_high_volatility',
            'optimize_resource_allocation_timing',
            'strengthen_prediction_accuracy'
        ]
        return random.sample(recommendations, random.randint(2, 4))
    
    def _generate_risk_factors(self) -> List[str]:
        """Genera factores de riesgo"""
        risk_factors = [
            'environmental_instability',
            'increased_competition',
            'resource_depletion',
            'prediction_uncertainty',
            'communication_breakdown'
        ]
        return random.sample(risk_factors, random.randint(1, 3))
    
    def _generate_prediction_data(self) -> Dict[str, Any]:
        """Genera datos de predicción"""
        return {
            'resource_forecast': {
                'next_5_cycles': [random.randint(40, 90) for _ in range(5)],
                'trend_direction': random.choice(['increasing', 'stable', 'decreasing']),
                'confidence': random.uniform(0.6, 0.9)
            },
            'threat_forecast': {
                'probability_increase': random.uniform(0.1, 0.6),
                'expected_intensity': random.uniform(0.2, 0.8),
                'time_to_peak': random.randint(3, 10)
            }
        }
    
    def _generate_confidence_intervals(self) -> Dict[str, Tuple[float, float]]:
        """Genera intervalos de confianza"""
        return {
            'resource_level': (random.uniform(30, 50), random.uniform(70, 90)),
            'success_rate': (random.uniform(0.4, 0.6), random.uniform(0.7, 0.9)),
            'efficiency': (random.uniform(0.3, 0.5), random.uniform(0.6, 0.8))
        }
    
    def _generate_uncertainty_factors(self) -> List[str]:
        """Genera factores de incertidumbre"""
        factors = [
            'external_market_volatility',
            'competitor_strategy_changes',
            'technological_disruption',
            'regulatory_changes',
            'environmental_fluctuations'
        ]
        return random.sample(factors, random.randint(2, 4))
    
    def _generate_shared_predictions(self, prediction_type: str, time_horizon: int) -> Dict[str, Any]:
        """Genera predicciones para compartir"""
        base_predictions = {}
        
        for i in range(1, time_horizon + 1):
            cycle_key = f"cycle_{i}"
            base_predictions[cycle_key] = {
                'resource_level': max(0, 75 - i * 5 + random.randint(-15, 15)),
                'threat_level': min(1.0, 0.3 + i * 0.05 + random.uniform(-0.1, 0.1)),
                'opportunity_score': random.uniform(0.2, 0.8),
                'stability_index': max(0.1, 0.8 - i * 0.02 + random.uniform(-0.1, 0.1))
            }
        
        return base_predictions
    
    def _generate_prediction_factors(self, prediction_type: str) -> List[str]:
        """Genera factores que contribuyen a las predicciones"""
        factor_sets = {
            'environment': ['resource_trends', 'competition_dynamics', 'external_factors'],
            'performance': ['strategy_effectiveness', 'learning_rate', 'adaptation_speed'],
            'general': ['historical_patterns', 'current_state', 'trend_analysis']
        }
        
        factors = factor_sets.get(prediction_type, factor_sets['general'])
        return random.sample(factors, len(factors))
    
    def _generate_uncertainty_range(self) -> Dict[str, float]:
        """Genera rangos de incertidumbre"""
        return {
            'prediction_variance': random.uniform(0.1, 0.3),
            'confidence_interval_width': random.uniform(0.2, 0.4),
            'model_uncertainty': random.uniform(0.05, 0.2)
        }
    
    def _update_agent_knowledge(self, agent_id: str, message: Message):
        """Actualiza conocimiento sobre otros agentes"""
        if agent_id not in self.known_agents:
            self.known_agents[agent_id] = {
                'first_contact': datetime.now(),
                'message_count': 0,
                'last_interaction': datetime.now(),
                'interaction_types': set(),
                'communication_pattern': {},
                'reliability_history': []
            }
        
        agent_info = self.known_agents[agent_id]
        agent_info['message_count'] += 1
        agent_info['last_interaction'] = datetime.now()
        agent_info['interaction_types'].add(message.message_type)
        
        # Actualizar patrón de comunicación
        hour = message.timestamp.hour
        if hour not in agent_info['communication_pattern']:
            agent_info['communication_pattern'][hour] = 0
        agent_info['communication_pattern'][hour] += 1
    
    def _update_reputation(self, agent_id: str, quality_score: float):
        """Actualiza puntuación de reputación de un agente"""
        current_score = self.reputation_scores.get(agent_id, 0.5)
        
        # Media ponderada con mayor peso a interacciones recientes
        learning_rate = 0.2
        new_score = current_score * (1 - learning_rate) + quality_score * learning_rate
        self.reputation_scores[agent_id] = np.clip(new_score, 0.0, 1.0)
        
        # Actualizar historial de confiabilidad
        if agent_id in self.known_agents:
            self.known_agents[agent_id]['reliability_history'].append({
                'timestamp': datetime.now(),
                'quality_score': quality_score,
                'reputation_score': new_score
            })
    
    def broadcast_to_network(self, message_type: str, content: Dict[str, Any], network: List[str]):
        """Envía mensaje de difusión a múltiples agentes"""
        for agent_id in network:
            if agent_id != self.agent_id:
                self.send_message(agent_id, 'broadcast', {
                    'broadcast_type': message_type,
                    **content
                }, priority=2)
    
    def initiate_collaboration(self, target_agents: List[str], collaboration_type: str, 
                             expected_benefit: float, duration: int = 5) -> List[str]:
        """Inicia proceso de colaboración con múltiples agentes"""
        collaboration_id = str(uuid.uuid4())[:8]
        responses = []
        
        for agent_id in target_agents:
            message_id = self.send_message(
                agent_id, 
                'request',
                {
                    'request_type': 'collaboration',
                    'collaboration_type': collaboration_type,
                    'collaboration_id': collaboration_id,
                    'expected_benefit': expected_benefit,
                    'duration': duration,
                    'risk_level': random.uniform(0.2, 0.6),
                    'initiator_reputation': self.reputation_scores.get(self.agent_id, 0.7)
                },
                priority=3,
                requires_response=True
            )
            responses.append(message_id)
        
        return responses
    
    def share_prediction_with_network(self, prediction_data: Dict[str, Any], network: List[str]):
        """Comparte predicciones con la red de agentes"""
        self.broadcast_to_network(
            'prediction_update',
            {
                'prediction_data': prediction_data,
                'confidence': prediction_data.get('confidence', 0.7),
                'timestamp': datetime.now().isoformat(),
                'model_version': '1.0'
            },
            network
        )
    
    def request_prediction_consensus(self, prediction_query: Dict[str, Any], network: List[str]) -> List[str]:
        """Solicita consenso de predicción de múltiples agentes"""
        request_id = str(uuid.uuid4())[:8]
        responses = []
        
        for agent_id in network:
            if agent_id != self.agent_id:
                message_id = self.send_message(
                    agent_id,
                    'request',
                    {
                        'request_type': 'prediction_sharing',
                        'request_id': request_id,
                        'prediction_type': prediction_query.get('type', 'general'),
                        'time_horizon': prediction_query.get('time_horizon', 5),
                        'specific_variables': prediction_query.get('variables', [])
                    },
                    priority=3,
                    requires_response=True
                )
                responses.append(message_id)
        
        return responses
    
    def get_communication_summary(self) -> Dict[str, Any]:
        """Obtiene resumen de actividad de comunicación"""
        recent_messages = [m for m in self.message_history if 
                          (datetime.now() - m.timestamp).seconds < 300]
        
        message_types = {}
        for message in recent_messages:
            msg_type = message.message_type
            if msg_type not in message_types:
                message_types[msg_type] = 0
            message_types[msg_type] += 1
        
        return {
            'total_messages_sent': len([m for m in self.message_history if m.sender_id == self.agent_id]),
            'total_messages_received': len([m for m in self.message_history if m.receiver_id == self.agent_id]),
            'known_agents_count': len(self.known_agents),
            'average_reputation': np.mean(list(self.reputation_scores.values())) if self.reputation_scores else 0.5,
            'recent_interactions': len(recent_messages),
            'message_type_distribution': message_types,
            'most_trusted_agents': sorted(self.reputation_scores.items(), key=lambda x: x[1], reverse=True)[:3],
            'communication_efficiency': self._calculate_communication_efficiency()
        }
    
    def _calculate_communication_efficiency(self) -> float:
        """Calcula eficiencia de comunicación"""
        if not self.message_history:
            return 0.5
        
        # Calcular ratio de respuestas recibidas vs solicitudes enviadas
        requests_sent = len([m for m in self.message_history if 
                           m.sender_id == self.agent_id and m.requires_response])
        responses_received = len([m for m in self.message_history if 
                                m.receiver_id == self.agent_id and m.message_type == 'response'])
        
        if requests_sent == 0:
            return 0.7
        
        response_rate = min(1.0, responses_received / requests_sent)
        
        # Factor de calidad basado en reputaciones
        avg_reputation = np.mean(list(self.reputation_scores.values())) if self.reputation_scores else 0.5
        
        return (response_rate * 0.6 + avg_reputation * 0.4)

class PredictionEngine:
    """Motor de predicción avanzado para estados futuros del entorno y comportamiento"""
    
    def __init__(self, prediction_horizon: int = 10):
        self.prediction_horizon = prediction_horizon
        self.models: Dict[str, PredictionModel] = {}
        self.historical_states: deque = deque(maxlen=100)
        self.prediction_cache: Dict[str, Prediction] = {}
        self.feature_importance: Dict[str, float] = {}
        self.ensemble_predictions: List[Prediction] = []
        self.external_predictions: Dict[str, List[Dict[str, Any]]] = {}  # Predicciones de otros agentes
        
    def add_observation(self, state: Dict[str, Any], timestamp: datetime = None):
        """Añade una observación al historial"""
        if timestamp is None:
            timestamp = datetime.now()
        
        observation = {
            'state': state,
            'timestamp': timestamp,
            'features': self._extract_predictive_features(state)
        }
        
        self.historical_states.append(observation)
        self._update_models()
        self._update_feature_importance()
    
    def add_external_prediction(self, agent_id: str, prediction_data: Dict[str, Any]):
        """Añade predicción de otro agente para consenso"""
        if agent_id not in self.external_predictions:
            self.external_predictions[agent