<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>IA Agente de Investigación</title>
    <style>
        body { font-family: sans-serif; display: flex; height: 100vh; margin: 0; background: #f4f4f9; }
        .panel { padding: 20px; overflow-y: auto; box-sizing: border-box; }
        #left-panel { flex: 1; background: #fff; border-right: 1px solid #ddd; display: flex; flex-direction: column; }
        #right-panel { flex: 2; display: grid; grid-template-rows: 1fr 1fr; grid-template-columns: 1fr 1fr; gap: 20px; }
        h1, h2 { color: #333; }
        textarea { width: 95%; border: 1px solid #ccc; padding: 10px; margin-bottom: 10px; border-radius: 5px; }
        button { background: #007bff; color: #fff; border: none; padding: 10px 15px; cursor: pointer; margin: 5px; border-radius: 5px; }
        .reward-btn { background: #28a745; }
        .penalty-btn { background: #dc3545; }
        #ai-log { background: #e9ecef; flex-grow: 1; padding: 10px; border-radius: 5px; white-space: pre-wrap; font-size: 0.9em; }
        .results-box { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        #image-results img { max-width: 100%; border-radius: 5px; }
        #feedback-controls { display: none; }
    </style>
</head>
<body>
    <div id="left-panel" class="panel">
        <h1>Control de la IA</h1>
        <h2>1. Tema de Investigación</h2>
        <textarea id="user-command" rows="3" placeholder="Ej: 'Investiga sobre los gatos y busca una foto'"></textarea>
        <button id="send-command-btn">Iniciar Investigación</button>

        <h2>2. Log de la IA</h2>
        <div id="ai-log">Esperando instrucciones...</div>

        <h2>3. Feedback</h2>
        <div id="feedback-controls">
            <p>¿Fue un paso útil?</p>
            <button id="reward-btn" class="reward-btn">Sí, útil (+10)</button>
            <button id="penalty-btn" class="penalty-btn">No, inútil (-10)</button>
        </div>
    </div>

    <div id="right-panel" class="panel">
        <div id="text-results" class="results-box">
            <h2>Resultados de Texto</h2>
            <div id="text-content"></div>
        </div>
        <div id="image-results" class="results-box">
            <h2>Resultados de Imagen</h2>
            <div id="image-content"></div>
        </div>
        <div id="analysis-results" class="results-box" style="grid-column: span 2;">
            <h2>Análisis y Síntesis de Patrones</h2>
            <div id="analysis-content"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>