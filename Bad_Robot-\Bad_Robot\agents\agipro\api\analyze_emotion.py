@app.route('/analyze_emotion', methods=['POST'])
def analyze_emotion():
    try:
        data = request.get_json()
        if not data or 'image' not in data:
            return jsonify({
                'status': 'error',
                'error': 'No se proporcionaron datos de imagen'
            }), 400
        
        # Extraer la parte base64 de la imagen (eliminar el prefijo)
        header, encoded = data['image'].split(",", 1)
        
        # Decodificar la imagen base64
        binary_data = base64.b64decode(encoded)
        
        # Convertir a formato OpenCV
        image = np.frombuffer(binary_data, dtype=np.uint8)
        image = cv2.imdecode(image, cv2.IMREAD_COLOR)
        
        # Verificar que la imagen se cargó correctamente
        if image is None:
            return jsonify({
                'status': 'error',
                'error': 'Formato de imagen no válido'
            }), 400
        
        # Procesar la imagen para detectar emociones
        result = emotion_recognizer.analyze_face(image)
        
        return jsonify({
            'status': 'success',
            'emotion': result['emotion'],
            'confidence': result['confidence'],
            'emotion_icon': result['emotion_icon']
        })
        
    except ValueError as e:
        return jsonify({
            'status': 'error',
            'error': 'Formato base64 no válido'
        }), 400
        
    except Exception as e:
        print(f"Error en análisis de emoción: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': f"Error interno: {str(e)}",
            'emotion': 'neutral',
            'confidence': 0,
            'emotion_icon': '😐'
        }), 500