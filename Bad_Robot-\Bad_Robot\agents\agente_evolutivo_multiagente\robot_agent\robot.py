import paho.mqtt.client as mqtt
import random
import time
import json
import os

class RobotAgent:
    def __init__(self, client_id):
        self.client = mqtt.Client(client_id)
        self.client.connect("localhost", 1883)
        self.id = client_id

    def percibir(self):
        return {
            "temperatura": round(random.uniform(15.0, 30.0), 1),
            "movimiento": random.choice([True, False]),
            "sonido": random.choice(["silencio", "voz", "alarma"]),
            "hora": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    def enviar_datos(self):
        datos = self.percibir()
        payload = json.dumps({
            "agente": self.id,
            "tipo": "sensor",
            "datos": datos
        })
        self.client.publish("topic/sensores", payload)
        print(f"[{self.id}] Enviado: {payload}")

    def recibir_accion(self, client, userdata, msg):
        if msg.topic == "topic/acciones":
            accion = json.loads(msg.payload)
            print(f"[{self.id}] Recibida acción: {accion}")
            self.ejecutar_accion(accion)

    def ejecutar_accion(self, accion):
        tipo = accion.get("accion")
        if tipo == "activar_calefaccion":
            print("[ACCION] Calefacción activada.")
        elif tipo == "alerta":
            print("[ACCION] Alarma activada.")

    def iniciar(self):
        self.client.on_message = self.recibir_accion
        self.client.subscribe("topic/acciones")
        self.client.loop_start()
        while True:
            self.enviar_datos()
            time.sleep(5)

if __name__ == "__main__":
    robot = RobotAgent("robot_001")
    robot.iniciar()