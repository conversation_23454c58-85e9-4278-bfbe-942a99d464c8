**Sistema de Gestión de Datos, Conocimiento y Modelos para "Autonomatic"**

**Objetivo:** Implementar y mantener un sistema robusto y eficiente para el almacenamiento, recuperación y utilización de datos, conocimiento estructurado y modelos de machine learning.

**1. Gestión de Datos y Almacenamiento Vectorial:**
    *   **Vector Store:** Utiliza `SupabaseVectorStore` o `ChromaDB` (según configuración `VECTOR_STORE`) para:
        *   Almacenar embeddings de los resultados de las tareas y documentos relevantes.
        *   Realizar búsquedas de similitud (`similarity_search`) para el `context_agent`.
    *   **Base de Datos Estructurada (`Supabase`):**
        *   Utiliza tablas (`ai_system`, `documents`, `memories`, `data`) para almacenar:
            *   Configuración del sistema y estado.
            *   Metadatos de documentos y tareas.
            *   Variables de memoria a largo plazo.
            *   Resultados estructurados de tareas.
        *   Implementa funciones SQL (si usas Supabase) para consultas complejas o triggers.
    *   **Manejo de Archivos Locales:**
        *   Utiliza `os`, `shutil`, `glob`, `Path` para leer, escribir, listar y organizar archivos en el sistema de archivos local.
        *   Procesa diferentes tipos de archivo (CSV con `pandas`, PDF con `PyPDF2`, DOCX con `python-docx`).
        *   Implementa lógica para eliminar duplicados y organizar la información descargada o generada.

**2. Grafos de Conocimiento y Memoria Procedural:**
    *   **Neo4j (u otro motor de grafos):**
        *   Representa conocimiento procedural (secuencias de acciones, planes, dependencias complejas) como grafos.
        *   Almacena relaciones entre entidades, conceptos y tareas.
        *   Utiliza Cypher (o el lenguaje de consulta apropiado) para extraer patrones y secuencias de la memoria procedural.
    *   **Generación Dinámica:** Desarrolla la capacidad de aumentar dinámicamente los grafos de conocimiento a medida que se aprende nueva información o se completan tareas.

**3. Gestión de Modelos LLM y ML:**
    *   **Soporte Multimodelo:**
        *   Asegura la carga y el uso correcto de modelos locales (`LlamaCpp`, `mistral.gguf`, `gemma-2-2b-it-GGUF`, etc.) desde `MODEL_DIRS`.
        *   Gestiona las llamadas a APIs externas (`OpenAI`, `Anthropic`).
        *   Implementa la lógica de `MODEL_PRIORITY` para seleccionar el modelo apropiado.
        *   Configura dinámicamente parámetros (`temperature`, `max_tokens`, `threads`, `context` según `MODEL_SETTINGS`).
    *   **Fine-Tuning y Reentrenamiento (Avanzado):**
        *   **MoE (Mix of Experts):** Si se implementan múltiples modelos especializados (ej. para predicciones financieras), establece un flujo de trabajo para reentrenar la red de gating del MoE con nuevos datos de mercado.
        *   **RL Policy Refresh:** Para políticas de RL (ej. en simulación de portafolio o toma de decisiones), actualiza periódicamente las políticas con logs de interacción del usuario o resultados de simulación.
        *   **Actualización de Modelos de Sentimiento:** Reentrena modelos de análisis de sentimiento con noticias recientes.
        *   Establece `cron jobs` (o `schedule` en Python) para estas tareas de actualización/fine-tuning.
    *   **Modelos de Clasificación/Traducción/Generación Específicos:**
        *   Entrena o integra modelos para tareas específicas si es necesario (ej. clasificación de datos, traducción automática).

**4. Acceso y Procesamiento de Información Externa:**
    *   **Búsqueda Web:** Utiliza `requests`, `httpx`, `BeautifulSoup` para scraping o `DuckDuckGo`/`Google Custom Search API` para búsquedas estructuradas.
    *   **APIs de Noticias:** Integra `NewsAPI` para obtener y procesar noticias relevantes, generando resúmenes con NLP.
    *   **Datos de Mercado en Tiempo Real (Si aplica el dominio financiero):**
        *   Implementa la ingesta de precios de acciones, índices, forex, crypto, commodities.
        *   Obtén indicadores económicos (PIB, inflación, etc.).
        *   Utiliza `WebSockets` para recibir datos en tiempo real y actualizar dashboards o interfaces.

**5. Automatización del Flujo de Información:**
    *   Define flujos de trabajo para cómo la información descargada/procesada se almacena, se indexa y se utiliza para tareas futuras o recomendaciones.
    *   Ejemplo: Descargar -> Organizar (eliminar duplicados) -> Procesar (extraer info, generar embeddings) -> Almacenar en BD/Vector Store -> Usar para respuestas/análisis.