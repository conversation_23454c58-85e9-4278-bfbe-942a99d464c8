import sqlite3

# Conectarse a la base de datos (la creará si no existe)
connection = sqlite3.connect('tasks.db')

# Crear un cursor para ejecutar comandos
cursor = connection.cursor()

# Ejecutar el comando para crear la tabla 'tasks'
# Usamos "IF NOT EXISTS" para evitar errores si el script se ejecuta más de una vez.
cursor.execute('''
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    completed BOOLEAN NOT NULL CHECK (completed IN (0, 1))
)
''')

# Guardar los cambios y cerrar la conexión
connection.commit()
connection.close()

print("Base de datos 'tasks.db' inicializada correctamente.")