├── index.html (Interfaz principal)
├── agi_assistant/
│   ├── main.py (Núcleo principal)
│   ├── voice_interface.py (Voz completa)
│   ├── code_analysis.py (Análisis profundo de código)
│   ├── code_management.py (Refactorización y optimización)
│   ├── dependency_manager.py (Gestión inteligente de dependencias)
│   ├── git_integration.py (Control de versiones)
│   ├── multi_ai_integration.py (Conexión con todos los modelos de IA)
│   ├── file_manager.py (Gestión de archivos y directorios)
│   └── huggingface_integration.py (Modelos especializados)
├── prompts/
│   ├── coding_prompts.md
│   ├── git_prompts.md
│   └── error_handling.md
└── requirements.txt