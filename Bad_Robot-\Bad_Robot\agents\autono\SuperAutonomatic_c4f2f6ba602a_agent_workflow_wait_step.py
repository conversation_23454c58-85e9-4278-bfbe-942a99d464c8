"""agent_workflow_wait_step

Revision ID: c4f2f6ba602a
Revises: 40affbf3022b
Create Date: 2023-09-04 05:34:10.195248

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c4f2f6ba602a'
down_revision = '40affbf3022b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agent_workflow_step_waits',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('unique_id', sa.String(), nullable=True),
    sa.Column('delay', sa.Integer(), nullable=True),
    sa.Column('wait_begin_time', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('agent_workflow_step_waits')
    # ### end Alembic commands ###
