<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGI+ Assistant</title>
    <!-- Importamos la librería del cliente de Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; margin: 0; display: flex; height: 100vh; background-color: #f0f2f5; }
        .sidebar { width: 350px; background-color: #fff; padding: 20px; border-right: 1px solid #ddd; display: flex; flex-direction: column; box-shadow: 2px 0 5px rgba(0,0,0,0.05); }
        .main-content { flex-grow: 1; display: flex; flex-direction: column; padding: 20px; }
        h1, h2 { color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; }
        #video-container { position: relative; width: 100%; max-width: 320px; margin: 0 auto; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        #video { width: 100%; display: block; }
        #status-overlay { position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.6); color: white; padding: 5px 10px; border-radius: 5px; font-size: 14px; }
        .info-box { background: #e9ecef; border-radius: 8px; padding: 15px; margin-top: 20px; }
        .info-box p { margin: 5px 0; font-size: 16px; }
        .info-box span { font-weight: bold; color: #007bff; }
        #chat-window { flex-grow: 1; border: 1px solid #ccc; background-color: #fff; padding: 15px; overflow-y: auto; margin-bottom: 15px; border-radius: 8px; }
        .chat-message { margin-bottom: 12px; padding: 8px 12px; border-radius: 18px; max-width: 80%; }
        .user-message { background-color: #007bff; color: white; align-self: flex-end; margin-left: auto; }
        .assistant-message { background-color: #e9ecef; color: #333; align-self: flex-start; }
        .error-message { background-color: #ffdddd; color: #d8000c; border: 1px solid #d8000c; }
        #input-area { display: flex; gap: 10px; }
        #message-input { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 20px; }
        #send-button, #record-button { padding: 10px 20px; border: none; border-radius: 20px; color: white; cursor: pointer; font-weight: bold; }
        #send-button { background-color: #28a745; }
        #record-button.recording { background-color: #dc3545; }
        #record-button { background-color: #17a2b8; }
    </style>
</head>
<body>
    <!-- Columna lateral para Video y Estado -->
    <div class="sidebar">
        <h1>AGI+ Dashboard</h1>
        
        <div id="video-container">
            <video id="video" autoplay playsinline muted></video>
            <canvas id="canvas" style="display:none;"></canvas>
            <div id="status-overlay">Conectando...</div>
        </div>

        <div class="info-box">
            <h2>Contexto Detectado</h2>
            <p>Persona: <span id="person-name">Desconocido</span></p>
            <p>Emoción Facial: <span id="facial-emotion">Neutral 😐</span></p>
            <p>Emoción Vocal: <span id="vocal-emotion">Neutral 🎤</span></p>
        </div>
    </div>

    <!-- Contenido principal para el Chat -->
    <div class="main-content">
        <div id="chat-window">
            <!-- Los mensajes del chat se agregarán aquí -->
        </div>
        <div id="input-area">
            <input type="text" id="message-input" placeholder="Escribe un mensaje o usa el botón de voz...">
            <button id="send-button">Enviar</button>
            <button id="record-button">Grabar Voz</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- 1. INICIALIZACIÓN Y REFERENCIAS A ELEMENTOS ---
            const socket = io();

            // Elementos de la UI
            const video = document.getElementById('video');
            const canvas = document.getElementById('canvas');
            const statusOverlay = document.getElementById('status-overlay');
            const personNameEl = document.getElementById('person-name');
            const facialEmotionEl = document.getElementById('facial-emotion');
            const vocalEmotionEl = document.getElementById('vocal-emotion');
            const chatWindow = document.getElementById('chat-window');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const recordButton = document.getElementById('record-button');

            // --- 2. ESTADO GLOBAL ---
            // Guardamos el contexto actual para enviarlo con cada mensaje
            let currentFacialData = { emotion: 'neutral', confidence: 0.0, emotion_icon: '😐', person: 'Desconocido' };
            let currentVocalEmotion = { emotion: 'neutral', confidence: 0.0 };
            
            // Para la grabación de voz
            let mediaRecorder;
            let audioChunks = [];
            let isRecording = false;

            // --- 3. CONFIGURACIÓN DE SOCKET.IO ---
            socket.on('connect', () => {
                console.log('Conectado al servidor AGI+');
                statusOverlay.textContent = '✅ Conectado';
            });

            socket.on('status', (data) => {
                console.log('Status del servidor:', data.message);
            });
            
            socket.on('assistant_response', (data) => {
                addMessageToChat('Assistant', data.text, 'assistant-message');
                playTextAsSpeech(data.text);
            });

            socket.on('error', (data) => {
                console.error('Error del servidor:', data.text);
                addMessageToChat('Error', data.text, 'error-message');
            });

            // --- 4. CONFIGURACIÓN DE CÁMARA Y ANÁLISIS FACIAL ---
            async function setupCamera() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
                    video.srcObject = stream;
                    
                    // Iniciar análisis facial periódico
                    setInterval(analyzeFace, 2000); // Analiza la cara cada 2 segundos

                    // Preparar el grabador de voz con el stream de audio
                    prepareMediaRecorder(stream);

                } catch (err) {
                    console.error("Error al acceder a la cámara/micrófono:", err);
                    statusOverlay.textContent = '❌ Error de Cámara/Mic';
                }
            }

            function prepareMediaRecorder(stream) {
                mediaRecorder = new MediaRecorder(stream);

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    audioChunks = []; // Limpiar para la próxima grabación
                    
                    // Mostrar que se está procesando
                    recordButton.textContent = 'Procesando...';
                    recordButton.disabled = true;

                    // Enviar audio a los dos endpoints en paralelo
                    const formData = new FormData();
                    formData.append('audio', audioBlob, 'recording.webm');
                    
                    try {
                        const [transcribeResponse, voiceEmotionResponse] = await Promise.all([
                            fetch('/api/transcribe', { method: 'POST', body: formData }),
                            fetch('/api/analyze_voice_emotion', { method: 'POST', body: new FormData(formData) }) // Clonar FormData
                        ]);

                        const transcriptData = await transcribeResponse.json();
                        const voiceEmotionData = await voiceEmotionResponse.json();
                        
                        if (transcriptData.error) throw new Error(`Transcripción: ${transcriptData.error}`);

                        // Actualizar estado de emoción vocal
                        if (!voiceEmotionData.error) {
                            currentVocalEmotion = voiceEmotionData;
                            vocalEmotionEl.textContent = `${voiceEmotionData.emotion.charAt(0).toUpperCase() + voiceEmotionData.emotion.slice(1)} 🎤`;
                        }
                        
                        // Enviar el mensaje transcrito con todo el contexto
                        sendMessage(transcriptData.text);

                    } catch (error) {
                        console.error("Error procesando audio:", error);
                        addMessageToChat('Error', 'No se pudo procesar el audio.', 'error-message');
                    } finally {
                        recordButton.textContent = 'Grabar Voz';
                        recordButton.disabled = false;
                    }
                };
            }

            async function analyzeFace() {
                if (video.readyState < 2) return; // Si el video no está listo

                // Dibujar el frame actual en el canvas oculto
                const context = canvas.getContext('2d');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Obtener la imagen como base64
                const imageDataUrl = canvas.toDataURL('image/jpeg');

                try {
                    const response = await fetch('/api/analyze_face', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ image: imageDataUrl }),
                    });
                    const data = await response.json();
                    
                    if (data.error) {
                        console.warn("Error en análisis facial:", data.error);
                        return;
                    }
                    
                    // Actualizar el estado global y la UI
                    currentFacialData = data;
                    personNameEl.textContent = data.person;
                    facialEmotionEl.textContent = `${data.emotion.charAt(0).toUpperCase() + data.emotion.slice(1)} ${data.emotion_icon}`;
                    
                } catch (err) {
                    console.error("Error enviando imagen para análisis:", err);
                }
            }

            // --- 5. FUNCIONES DE INTERACCIÓN ---
            function sendMessage(text) {
                const messageText = (typeof text === 'string' ? text : messageInput.value).trim();
                if (!messageText) return;

                addMessageToChat('Tú', messageText, 'user-message');
                
                // Construir el payload con el estado actual
                const payload = {
                    message: messageText,
                    facial_emotion: { emotion: currentFacialData.emotion, confidence: currentFacialData.confidence },
                    vocal_emotion: currentVocalEmotion,
                    person: currentFacialData.person
                };

                // Enviar al servidor
                socket.emit('user_message', payload);

                if (typeof text !== 'string') {
                    messageInput.value = '';
                }
            }

            async function playTextAsSpeech(text) {
                try {
                    const response = await fetch('/api/tts', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text })
                    });
                    if (!response.ok) {
                        throw new Error(`El servidor TTS respondió con estado ${response.status}`);
                    }
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    const audio = new Audio(audioUrl);
                    audio.play();
                } catch (err) {
                    console.error("Error en Text-to-Speech:", err);
                }
            }

            function toggleRecording() {
                if (isRecording) {
                    // Detener grabación
                    mediaRecorder.stop();
                    recordButton.textContent = 'Grabar Voz';
                    recordButton.classList.remove('recording');
                    isRecording = false;
                } else {
                    // Iniciar grabación
                    audioChunks = [];
                    mediaRecorder.start();
                    recordButton.textContent = 'Detener';
                    recordButton.classList.add('recording');
                    isRecording = true;
                }
            }
            
            function addMessageToChat(sender, message, className) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('chat-message', className);
                messageElement.innerHTML = `<strong>${sender}:</strong> ${message}`;
                chatWindow.appendChild(messageElement);
                chatWindow.scrollTop = chatWindow.scrollHeight; // Auto-scroll
            }

            // --- 6. ASIGNACIÓN DE EVENTOS ---
            sendButton.addEventListener('click', () => sendMessage());
            messageInput.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            });
            recordButton.addEventListener('click', toggleRecording);

            // Iniciar todo
            setupCamera();
        });
    </script>
</body>
</html>