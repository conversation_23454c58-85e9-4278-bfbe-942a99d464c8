from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import json
import threading
import time
from datetime import datetime
import asyncio
import logging

# Import your enhanced agent (assuming it's in the same directory)
try:
    from advanced_agent import EnhancedAutonomousEvolutiveAgent
except ImportError:
    print("Warning: Could not import EnhancedAutonomousEvolutiveAgent. Using mock agent.")
    from unittest.mock import MagicMock
    EnhancedAutonomousEvolutiveAgent = MagicMock

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global agent instance
agent = None
system_stats_cache = {}
last_stats_update = 0

def initialize_agent():
    """Initialize the enhanced autonomous agent"""
    global agent
    try:
        agent = EnhancedAutonomousEvolutiveAgent("WEB_AGENT_001")
        logger.info("Enhanced Autonomous Agent initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize agent: {e}")
        return False

def update_system_stats():
    """Update system statistics in background"""
    global system_stats_cache, last_stats_update
    
    while True:
        try:
            if agent and hasattr(agent, 'maintenance'):
                stats = agent.maintenance.self_diagnosis_and_repair()
                network_status = agent.maintenance.check_internet_connection()
                
                system_stats_cache = {
                    **stats,
                    'network': network_status,
                    'timestamp': datetime.now().isoformat(),
                    'agent_status': 'active'
                }
                last_stats_update = time.time()
            
        except Exception as e:
            logger.error(f"Error updating system stats: {e}")
            system_stats_cache = {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'agent_status': 'error'
            }
        
        time.sleep(5)  # Update every 5 seconds

# Routes
@app.route('/')
def index():
    """Serve the frontend interface"""
    # Read the HTML content from the artifact
    # In production, you would serve this as a static file
    return render_template_string(open('index.html', 'r', encoding='utf-8').read())

@app.route('/api/message', methods=['POST'])
def handle_message():
    """Handle incoming messages from the frontend"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        input_type = data.get('input_type', 'text')
        
        if not message.strip():
            return jsonify({
                'error': 'Empty message',
                'text': 'Por favor, envía un mensaje válido.',
                'speak': False
            }), 400
        
        logger.info(f"Received message: {message} (type: {input_type})")
        
        # Process message with the agent
        if agent:
            response = agent.process_user_input(message, input_type)
            
            # Enhance response with additional data
            enhanced_response = {
                **response,
                'timestamp': datetime.now().isoformat(),
                'agent_id': agent.agent_id if hasattr(agent, 'agent_id') else 'WEB_AGENT_001',
                'tasks': get_current_tasks(),
                'system_stats': system_stats_cache
            }
            
            logger.info(f"Agent response: {response.get('text', '')[:100]}...")
            return jsonify(enhanced_response)
        else:
            return jsonify({
                'text': 'Agente no disponible. Reintentando inicialización...',
                'speak': False,
                'error': 'Agent not initialized'
            }), 503
            
    except Exception as e:
        logger.error(f"Error handling message: {e}")
        return jsonify({
            'text': f'Error interno del servidor: {str(e)}',
            'speak': False,
            'error': str(e)
        }), 500

@app.route('/api/system_stats', methods=['GET'])
def get_system_stats():
    """Get current system statistics"""
    try:
        if time.time() - last_stats_update > 30:  # If stats are older than 30 seconds
            if agent and hasattr(agent, 'maintenance'):
                stats = agent.maintenance.self_diagnosis_and_repair()
                network_status = agent.maintenance.check_internet_connection()
                
                return jsonify({
                    **stats,
                    'network': network_status,
                    'timestamp': datetime.now().isoformat(),
                    'agent_status': 'active'
                })
        
        return jsonify(system_stats_cache)
        
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat(),
            'agent_status': 'error'
        }), 500

@app.route('/api/tasks', methods=['GET'])
def get_tasks():
    """Get current tasks"""
    try:
        tasks = get_current_tasks()
        return jsonify({
            'tasks': tasks,
            'total': len(tasks),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Error getting tasks: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tasks', methods=['POST'])
def create_task():
    """Create a new task"""
    try:
        data = request.get_json()
        task_description = data.get('description', '')
        priority = data.get('priority', 1)
        
        if not task_description.strip():
            return jsonify({'error': 'Task description is required'}), 400
        
        if agent and hasattr(agent, 'communication_module'):
            task_id = agent.communication_module.create_task(task_description, priority)
            
            return jsonify({
                'task_id': task_id,
                'description': task_description,
                'priority': priority,
                'status': 'created',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Agent communication module not available'}), 503
            
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/repair', methods=['POST'])
def repair_system():
    """Trigger system repair"""
    try:
        if agent and hasattr(agent, 'maintenance'):
            # Run system repair in background
            def run_repair():
                result = agent.maintenance.check_and_repair_system()
                logger.info(f"System repair completed: {result}")
            
            threading.Thread(target=run_repair, daemon=True).start()
            
            return jsonify({
                'status': 'started',
                'message': 'Reparación del sistema iniciada en segundo plano',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Agent maintenance module not available'}), 503
            
    except Exception as e:
        logger.error(f"Error starting system repair: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/network/check', methods=['POST'])
def check_network():
    """Check network connection"""
    try:
        if agent and hasattr(agent, 'maintenance'):
            result = agent.maintenance.check_internet_connection()
            
            return jsonify({
                'result': result,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Agent maintenance module not available'}), 503
            
    except Exception as e:
        logger.error(f"Error checking network: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/history', methods=['GET'])
def get_chat_history():
    """Get chat history"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        if agent and hasattr(agent, 'get_chat_history'):
            history = agent.get_chat_history(limit)
            
            return jsonify({
                'history': history,
                'total': len(history),
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'history': [], 'total': 0}), 200
            
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/clear', methods=['POST'])
def clear_chat():
    """Clear chat history"""
    try:
        if agent and hasattr(agent, 'chat_history'):
            agent.chat_history.clear()
            
            return jsonify({
                'status': 'cleared',
                'message': 'Historial de chat limpiado',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'status': 'no_history'}), 200
            
    except Exception as e:
        logger.error(f"Error clearing chat: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/status', methods=['GET'])
def get_agent_status():
    """Get agent status and information"""
    try:
        if agent:
            status = {
                'agent_id': getattr(agent, 'agent_id', 'Unknown'),
                'status': 'active',
                'capabilities': [
                    'speech_recognition',
                    'system_maintenance',
                    'task_management',
                    'communication',
                    'multimodal_interface'
                ],
                'memory_items': len(getattr(agent, 'memory', {})),
                'cycle_count': getattr(agent, 'cycle_count', 0),
                'timestamp': datetime.now().isoformat()
            }
            
            return jsonify(status)
        else:
            return jsonify({
                'status': 'inactive',
                'error': 'Agent not initialized',
                'timestamp': datetime.now().isoformat()
            }), 503
            
    except Exception as e:
        logger.error(f"Error getting agent status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/execute', methods=['POST'])
def execute_command():
    """Execute a system command through the agent"""
    try:
        data = request.get_json()
        command = data.get('command', '')
        
        if not command.strip():
            return jsonify({'error': 'Command is required'}), 400
        
        if agent and hasattr(agent, 'execute_command'):
            result = agent.execute_command(command)
            
            return jsonify({
                'command': command,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Agent command execution not available'}), 503
            
    except Exception as e:
        logger.error(f"Error executing command: {e}")
        return jsonify({'error': str(e)}), 500

def get_current_tasks():
    """Helper function to get current tasks"""
    try:
        if agent and hasattr(agent, 'current_tasks'):
            return agent.current_tasks
        elif agent and hasattr(agent, 'communication_module') and hasattr(agent.communication_module, 'task_queue'):
            return agent.communication_module.task_queue
        else:
            return []
    except Exception as e:
        logger.error(f"Error getting current tasks: {e}")
        return []

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

def main():
    """Main function to start the Flask server"""
    print("🚀 Starting Enhanced Agent Backend Server...")
    
    # Initialize agent
    if initialize_agent():
        print("✅ Agent initialized successfully")
    else:
        print("❌ Agent initialization failed - server will run in limited mode")
    
    # Start background stats updater
    stats_thread = threading.Thread(target=update_system_stats, daemon=True)
    stats_thread.start()
    print("📊 System stats monitor started")
    
    # Start Flask server
    print("🌐 Starting Flask server on http://localhost:5000")
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )

if __name__ == '__main__':
    main()