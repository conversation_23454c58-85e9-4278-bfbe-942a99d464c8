# setup.py - Complete setup script for Enhanced Agent Interface

import os
import subprocess
import sys
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3.8, 0):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def install_requirements():
    """Install required Python packages"""
    requirements = [
        'flask>=2.3.0',
        'flask-cors>=4.0.0',
        'numpy>=1.21.0',
        'psutil>=5.9.0',
        'speechrecognition>=3.10.0',
        'pyttsx3>=2.90',
        'requests>=2.28.0'
    ]
    
    print("📦 Installing Python requirements...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ Installed {req}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {req}: {e}")
            return False
    
    return True

def create_project_structure():
    """Create the project directory structure"""
    print("📁 Creating project structure...")
    
    directories = [
        'static',
        'templates',
        'logs',
        'data'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def create_config_file():
    """Create configuration file"""
    config_content = """# config.py - Configuration for Enhanced Agent Interface

import os

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'enhanced-agent-secret-key-2024'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # Agent configuration
    AGENT_ID = os.environ.get('AGENT_ID', 'WEB_AGENT_001')
    
    # System monitoring
    STATS_UPDATE_INTERVAL = int(os.environ.get('STATS_UPDATE_INTERVAL', '5'))
    
    # Speech recognition
    SPEECH_LANGUAGE = os.environ.get('SPEECH_LANGUAGE', 'es-ES')
    
    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'logs/agent.log')
    
    # Server configuration
    HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
    PORT = int(os.environ.get('FLASK_PORT', '5000'))

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
"""
    
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Created config.py")

def create_requirements_file():
    """Create requirements.txt file"""
    requirements_content = """flask>=2.3.0
flask-cors>=4.0.0
numpy>=1.21.0
psutil>=5.9.0
speechrecognition>=3.10.0
pyttsx3>=2.90
requests>=2.28.0
python-dotenv>=1.0.0
"""
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print("✅ Created requirements.txt")

def create_run_script():
    """Create run script"""
    run_content = """#!/usr/bin/env python3
# run.py - Main application runner

import os
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    # Import the Flask backend
    from flask_backend import main
    
    if __name__ == '__main__':
        print("🚀 Starting Enhanced Agent Interface...")
        print("📍 Access the interface at: http://localhost:5000")
        print("🔧 API Documentation available at: http://localhost:5000/api/")
        print("⏹️  Press Ctrl+C to stop the server")
        print("-" * 50)
        
        main()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all required files are in place:")
    print("- advanced_agent.py (your original agent file)")
    print("- flask_backend.py (Flask backend)")
    print("- index.html (frontend interface)")
    sys.exit(1)
except KeyboardInterrupt:
    print("\\n🛑 Server stopped by user")
    sys.exit(0)
except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)
"""
    
    with open('run.py', 'w', encoding='utf-8') as f:
        f.write(run_content)
    
    # Make it executable on Unix systems
    if os.name != 'nt':
        os.chmod('run.py', 0o755)
    
    print("✅ Created run.py")

def create_html_file():
    """Create the HTML file from the frontend artifact"""
    html_content = """<!-- Save the frontend HTML content here -->
<!-- Copy the complete HTML from the frontend artifact and paste it here -->
<!-- This file should be named index.html -->
"""
    
    if not os.path.exists('index.html'):
        with open('index.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("✅ Created index.html template (you need to copy the frontend content)")
    else:
        print("✅ index.html already exists")

def create_systemd_service():
    """Create systemd service file for Linux"""
    if os