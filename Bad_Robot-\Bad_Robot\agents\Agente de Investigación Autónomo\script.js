const userCommandEl = document.getElementById('user-command');
const sendCommandBtn = document.getElementById('send-command-btn');
const aiLogEl = document.getElementById('ai-log');
const feedbackControlsEl = document.getElementById('feedback-controls');
const rewardBtn = document.getElementById('reward-btn');
const penaltyBtn = document.getElementById('penalty-btn');
const textContentEl = document.getElementById('text-content');
const imageContentEl = document.getElementById('image-content');
const analysisContentEl = document.getElementById('analysis-content');

const API_URL = 'http://127.0.0.1:5000';
let currentTask = '';

sendCommandBtn.addEventListener('click', startTask);
rewardBtn.addEventListener('click', () => provideFeedback(10));
penaltyBtn.addEventListener('click', () => provideFeedback(-10));

function clearDashboard() {
    aiLogEl.innerHTML = 'Esperando instrucciones...';
    textContentEl.innerHTML = '';
    imageContentEl.innerHTML = '';
    analysisContentEl.innerHTML = '';
}

async function startTask() {
    currentTask = userCommandEl.value;
    if (!currentTask) return alert("Por favor, introduce un tema.");
    
    clearDashboard();
    logMessage("🤖", `Nueva investigación: "${currentTask}"`);
    sendCommandBtn.disabled = true;
    feedbackControlsEl.style.display = 'none';

    const response = await fetch(`${API_URL}/start_task`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command: currentTask })
    });
    handleAIResponse(await response.json());
}

async function provideFeedback(reward) {
    logMessage("👨‍🏫", `Feedback enviado: ${reward > 0 ? 'Positivo' : 'Negativo'} (${reward})`);
    feedbackControlsEl.style.display = 'none';

    const response = await fetch(`${API_URL}/feedback_and_continue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reward: reward, command: currentTask })
    });
    handleAIResponse(await response.json());
}

function handleAIResponse(data) {
    sendCommandBtn.disabled = false;
    if (data.error) {
        logMessage("🔥", `Error: ${data.error}`);
        return;
    }

    logMessage("💡", `Decisión: ${data.decision_type}. Razón: ${data.reason}`);
    logMessage("⚡️", `Ejecutando Acción: ${data.action_name}`);
    if (data.result.message) {
        logMessage("📄", `Resultado: ${data.result.message}`);
    }

    updateUI(data.result);

    if (data.task_complete) {
        logMessage("✅", "Investigación finalizada.");
        feedbackControlsEl.style.display = 'none';
    } else {
        feedbackControlsEl.style.display = 'block';
    }
}

function updateUI(result) {
    if (result.text_summary) {
        textContentEl.innerHTML = `<p>${result.text_summary}</p>`;
    }
    if (result.image_path) {
        // Añadir un timestamp para evitar el caché del navegador
        imageContentEl.innerHTML = `<img src="${API_URL}/${result.image_path}?t=${new Date().getTime()}" alt="Imagen encontrada por la IA">`;
    }
    if (result.synthesis) {
        analysisContentEl.innerHTML = `<p>${result.synthesis}</p>`;
    }
}

function logMessage(icon, text) {
    aiLogEl.innerHTML += `<div class="log-entry"><strong>${icon}</strong> ${text}</div>`;
    aiLogEl.scrollTop = aiLogEl.scrollHeight;
}