import os
import datetime

# Nombre conceptual de nuestra "AGI" simulada
AGI_NAME = "AOAD_Demo"

def log_message(message):
    """Función simple para registrar mensajes."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {AGI_NAME}: {message}")

def crear_archivo_con_codigo(nombre_archivo, codigo_fuente, lenguaje="desconocido"):
    """
    Crea un archivo con el código fuente proporcionado.
    """
    try:
        with open(nombre_archivo, "w", encoding="utf-8") as f:
            f.write(f"# Archivo generado por {AGI_NAME} en {datetime.datetime.now()}\n")
            f.write(f"# Lenguaje inferido/solicitado: {lenguaje}\n\n")
            f.write(codigo_fuente)
        log_message(f"Archivo '{nombre_archivo}' creado exitosamente.")
        return True
    except IOError as e:
        log_message(f"Error al crear el archivo '{nombre_archivo}': {e}")
        return False

def procesar_peticion_usuario(peticion):
    """
    Simula el análisis de la petición del usuario y decide si generar código.
    Esta es la parte que en una AGI real sería extremadamente compleja.
    """
    peticion_lower = peticion.lower()
    codigo_generado = None
    nombre_archivo_sugerido = None
    lenguaje_sugerido = "python" # Default

    # --- Lógica de "comprensión" muy básica ---

    if "crea una funcion python" in peticion_lower and "sumar dos numeros" in peticion_lower:
        nombre_archivo_sugerido = "funcion_suma.py"
        codigo_generado = """
def sumar(a, b):
    \"\"\"Esta función suma dos números.\"\"\"
    return a + b

# Ejemplo de uso:
# resultado = sumar(5, 3)
# print(f"La suma es: {resultado}")
"""
    elif "script bash" in peticion_lower and "mostrar fecha" in peticion_lower:
        nombre_archivo_sugerido = "mostrar_fecha.sh"
        lenguaje_sugerido = "bash"
        codigo_generado = """
#!/bin/bash
echo "La fecha y hora actual es:"
date
"""
    elif "crea un html" in peticion_lower and "hola mundo" in peticion_lower:
        nombre_archivo_sugerido = "hola.html"
        lenguaje_sugerido = "html"
        codigo_generado = """
<!DOCTYPE html>
<html>
<head>
    <title>Hola Mundo</title>
</head>
<body>
    <h1>Hola Mundo desde {AGI_NAME}!</h1>
    <p>Este archivo fue generado automáticamente.</p>
</body>
</html>
""".format(AGI_NAME=AGI_NAME) # Usamos format para insertar el nombre de la AGI

    elif "necesito un archivo de texto" in peticion_lower and "lista de compras" in peticion_lower:
        nombre_archivo_sugerido = "lista_compras.txt"
        lenguaje_sugerido = "texto plano"
        # Supongamos que la AGI puede extraer items de la petición (aquí lo hardcodeamos)
        items = ["Manzanas", "Leche", "Pan", "Huevos"] # Esto podría ser extraído de la petición
        codigo_generado = "Lista de Compras:\n"
        for item in items:
            codigo_generado += f"- {item}\n"

    # --- Fin de la lógica de comprensión ---

    if codigo_generado and nombre_archivo_sugerido:
        log_message(f"Detectada necesidad de crear código para: {nombre_archivo_sugerido}")
        confirmacion = input(f"{AGI_NAME}: Entendido. ¿Quieres que cree el archivo '{nombre_archivo_sugerido}'? (s/n): ").lower()
        if confirmacion == 's':
            crear_archivo_con_codigo(nombre_archivo_sugerido, codigo_generado, lenguaje_sugerido)
        else:
            log_message("Creación de archivo cancelada por el usuario.")
    elif peticion_lower not in ["salir", "adios", "exit"]:
        log_message("No he detectado una necesidad clara de generar código para tu petición, o no tengo la capacidad para ello.")
        log_message("Puedes probar: 'crea una funcion python para sumar dos numeros' o 'script bash para mostrar fecha'.")


def iniciar_chat_agi():
    """
    Inicia el bucle principal de interacción con el usuario.
    """
    log_message("Iniciando AGI Observadora y Autocodificadora Dinámica (Simulación).")
    log_message("Escribe tu petición o 'salir' para terminar.")

    while True:
        try:
            peticion_usuario = input("Usuario: ")
            if peticion_usuario.lower() in ["salir", "adios", "exit"]:
                log_message("Terminando simulación.")
                break
            procesar_peticion_usuario(peticion_usuario)
        except KeyboardInterrupt:
            log_message("\nInterrupción por teclado. Terminando simulación.")
            break
        except Exception as e:
            log_message(f"Ha ocurrido un error inesperado: {e}")
            # En una AGI real, este error podría llevar a un intento de autocorrección o diagnóstico.


# --- Punto de entrada principal ---
if __name__ == "__main__":
    iniciar_chat_agi()