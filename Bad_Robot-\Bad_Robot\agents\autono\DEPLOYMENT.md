# Deployment Checklist

## Security Requirements
- [ ] Set `FLASK_SECRET_KEY` environment variable
- [ ] Enable HTTPS (set `ENABLE_HTTPS=true`)
- [ ] Configure production WSGI server (Gunicorn/uWSGI)
- [ ] Set up firewall rules
- [ ] Configure automated backups
- [ ] Set up monitoring/alerting

## Environment Variables
```bash
export FLASK_SECRET_KEY=your-secure-key-here
export ENABLE_HTTPS=true
export PORT=8000
```

## Recommended Stack
- Web Server: Nginx (reverse proxy)
- App Server: Gunicorn with 4 workers
- Process Manager: systemd or supervisor
- Database: PostgreSQL (if needed)
- Cache: Redis (if needed)

## Deployment Steps
1. Install dependencies:
```bash
pip install gunicorn
```

2. Create systemd service file:
```ini
[Unit]
Description=Assistente Web Service
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/Assist/assistente
Environment="FLASK_SECRET_KEY=your-secret-key"
Environment="ENABLE_HTTPS=true"
ExecStart=/usr/bin/gunicorn --workers 4 --bind 0.0.0.0:8000 interaction_module:app
Restart=always

[Install]
WantedBy=multi-user.target
```

3. Configure Nginx:
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Security Best Practices
- Regularly rotate secrets
- Keep dependencies updated
- Monitor for suspicious activity
- Implement rate limiting
- Use a WAF (Web Application Firewall)
