import numpy as np
from PIL import Image, ImageDraw
import cv2
import requests
from io import BytesIO
import random
from typing import Di<PERSON>, <PERSON><PERSON>, Optional
# Reemplaza en emotions.py
import mediapipe as mp
import sqlite3
from datetime import datetime
import librosa
import soundfile as sf
import pydub
from pydub import AudioSegment
import os
import time
import threading

class EmotionRecognizer:
    def __init__(self):
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5)
        
        self.emotion_model = self._load_advanced_model()

    def _load_advanced_model(self):
        # Modelo simple de ejemplo (reemplazar con un modelo real entrenado)
        return {
            "happy": {"lip_stretch": (0.3, 0.9), "eye_squint": (0.1, 0.5)},
            "sad": {"lip_depressor": (0.4, 1.0), "brow_lower": (0.6, 1.0)},
            "angry": {"brow_tighten": (0.7, 1.0), "jaw_clench": (0.5, 1.0)},
            "surprised": {"eye_widen": (0.8, 1.0), "jaw_drop": (0.3, 0.9)}
        }

    def analyze_face(self, image_path: str) -> Dict[str, float]:
        try:
            if image_path.startswith('http'):
                response = requests.get(image_path)
                img = cv2.imdecode(np.frombuffer(response.content, np.uint8), cv2.IMREAD_COLOR)
            else:
                img = cv2.imread(image_path)
            
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(img_rgb)
            
            if not results.multi_face_landmarks:
                return {"neutral": 1.0}
            
            landmarks = results.multi_face_landmarks[0].landmark
            features = self._extract_facial_features(landmarks)
            
            return self._predict_emotion(features)
            
        except Exception as e:
            print(f"Error en análisis facial: {e}")
            return {"neutral": 1.0}

    def _extract_facial_features(self, landmarks):
        # Extrae 468 puntos clave faciales y calcula métricas
        return {
            "lip_stretch": self._calculate_distance(landmarks[61], landmarks[291]),
            "eye_squint": self._calculate_eye_closure(landmarks[145], landmarks[159]),
            "brow_lower": self._calculate_brow_drop(landmarks[65], landmarks[158]),
            "jaw_drop": self._calculate_distance(landmarks[17], landmarks[0])
        }
    
class EmotionRecognizer:
    def __init__(self):
        # Modelo básico de emociones (puedes reemplazar con un modelo ML más avanzado)
        self.emotion_model = {
            "happy": {"features": ["smile", "eyes_wide"], "threshold": 0.7},
            "sad": {"features": ["frown", "eyes_narrow"], "threshold": 0.6},
            "angry": {"features": ["brows_furrowed", "mouth_tight"], "threshold": 0.65},
            "surprised": {"features": ["eyes_wide_open", "mouth_open"], "threshold": 0.55}
        }
        
        # Configuración de respuestas emocionales
        self.emotional_responses = {
            "happy": [
                "¡Me alegra verte tan contento!",
                "Tu felicidad es contagiosa :)",
                "Parece que estás teniendo un gran día."
            ],
            "sad": [
                "Lamento que no estés teniendo un buen día...",
                "¿Quieres hablar sobre lo que te preocupa?",
                "Estoy aquí para ayudarte."
            ],
            "angry": [
                "Pareces molesto. ¿Quieres que te ayude con algo?",
                "Respira profundo, estoy aquí para ayudar.",
                "Intentaré ser más cuidadoso con mis palabras."
            ],
            "surprised": [
                "¡Vaya! Pareces sorprendido.",
                "¿Te he sorprendido?",
                "No esperaba esa reacción."
            ],
            "neutral": [
                "¿En qué puedo ayudarte hoy?",
                "Hola, ¿cómo estás?",
                "Estoy listo para ayudarte."
            ]
        }
        
        # Estado emocional del usuario
        self.user_emotion_history = []
        self.current_emotion = "neutral"
        
        # Configuración de expresión emocional del AGI
        self.agi_emotional_state = "neutral"
    
    def analyze_face(self, image_path: str) -> Dict[str, float]:
        """
        Analiza una imagen facial y devuelve las probabilidades de emociones
        
        Args:
            image_path: Ruta a la imagen o URL
            
        Returns:
            Dict: Diccionario con las probabilidades de cada emoción
        """
        # En una implementación real, aquí usarías un modelo de ML
        # Esta es una simulación básica
        try:
            if image_path.startswith('http'):
                response = requests.get(image_path)
                img = Image.open(BytesIO(response.content))
            else:
                img = Image.open(image_path)
                
            # Convertir a escala de grises para procesamiento básico
            img_gray = img.convert('L')
            width, height = img.size
            
            # Simular detección de características (en realidad usarías OpenCV/Dlib)
            features = {
                "smile": random.uniform(0, 1),
                "eyes_wide": random.uniform(0, 1),
                "frown": random.uniform(0, 1),
                "eyes_narrow": random.uniform(0, 1),
                "brows_furrowed": random.uniform(0, 1),
                "mouth_tight": random.uniform(0, 1),
                "eyes_wide_open": random.uniform(0, 1),
                "mouth_open": random.uniform(0, 1)
            }
            
            # Calcular probabilidades de emociones
            emotion_probs = {}
            for emotion, config in self.emotion_model.items():
                feature_scores = [features[feat] for feat in config["features"]]
                avg_score = sum(feature_scores) / len(feature_scores)
                emotion_probs[emotion] = avg_score if avg_score > config["threshold"] else 0
            
            # Normalizar probabilidades
            total = sum(emotion_probs.values())
            if total > 0:
                for emotion in emotion_probs:
                    emotion_probs[emotion] /= total
            else:
                emotion_probs["neutral"] = 1.0
            
            # Actualizar historial de emociones
            self.user_emotion_history.append(emotion_probs)
            if len(self.user_emotion_history) > 10:  # Mantener solo las últimas 10
                self.user_emotion_history.pop(0)
            
            # Determinar emoción actual
            self.current_emotion = max(emotion_probs.items(), key=lambda x: x[1])[0]
            
            return emotion_probs
            
        except Exception as e:
            print(f"Error al analizar imagen: {e}")
            return {"neutral": 1.0}
    
    def get_emotional_response(self, emotion: Optional[str] = None) -> str:
        """
        Obtiene una respuesta adecuada al estado emocional
        
        Args:
            emotion: Si no se proporciona, usa la emoción actual
            
        Returns:
            str: Respuesta emocional adecuada
        """
        target_emotion = emotion or self.current_emotion
        responses = self.emotional_responses.get(target_emotion, self.emotional_responses["neutral"])
        return random.choice(responses)
    
    def set_agi_emotion(self, emotion: str):
        """
        Establece el estado emocional del AGI para influir en sus respuestas
        
        Args:
            emotion: Emoción a establecer (happy, sad, angry, surprised, neutral)
        """
        valid_emotions = list(self.emotional_responses.keys())
        if emotion in valid_emotions:
            self.agi_emotional_state = emotion
        else:
            print(f"Emoción no válida. Usando 'neutral'. Válidas: {valid_emotions}")
            self.agi_emotional_state = "neutral"
    
    def generate_emotional_text(self, text: str) -> str:
        """
        Modifica un texto para reflejar el estado emocional del AGI
        
        Args:
            text: Texto original
            
        Returns:
            str: Texto con marcadores emocionales
        """
        if self.agi_emotional_state == "happy":
            return f"{text} :)"
        elif self.agi_emotional_state == "sad":
            return f"{text} :("
        elif self.agi_emotional_state == "angry":
            return f"{text} >:("
        elif self.agi_emotional_state == "surprised":
            return f"{text} :O"
        else:
            return text
    
    def detect_emotion_from_text(self, text: str) -> str:
        """
        Intenta detectar la emoción en un texto
        
        Args:
            text: Texto a analizar
            
        Returns:
            str: Emoción detectada
        """
        text = text.lower()
        positive_words = ["feliz", "contento", "genial", "maravilloso", "fantástico"]
        negative_words = ["triste", "deprimido", "molesto", "enfadado", "frustrado"]
        angry_words = ["odio", "asco", "rabia", "furioso", "cabreado"]
        surprise_words = ["sorpresa", "increíble", "asombroso", "wow", "oh"]
        
        if any(word in text for word in angry_words):
            return "angry"
        elif any(word in text for word in surprise_words):
            return "surprised"
        elif any(word in text for word in positive_words):
            return "happy"
        elif any(word in text for word in negative_words):
            return "sad"
        else:
            return "neutral"
    
    def get_emotion_face_icon(self, emotion: Optional[str] = None) -> str:
        """
        Devuelve un emoji correspondiente a la emoción
        
        Args:
            emotion: Si no se proporciona, usa la emoción actual
            
        Returns:
            str: Emoji representando la emoción
        """
        target_emotion = emotion or self.current_emotion
        icons = {
            "happy": "😊",
            "sad": "😢",
            "angry": "😠",
            "surprised": "😲",
            "neutral": "😐"
        }
        return icons.get(target_emotion, "🤖")  # Robot por defecto si no se reconoce

    def draw_emotional_face(self, emotion: Optional[str] = None, size: Tuple[int, int] = (200, 200)) -> Image:
        """
        Dibuja una cara simple que representa la emoción
        
        Args:
            emotion: Emoción a representar (si no se proporciona, usa la actual)
            size: Tamaño de la imagen resultante
            
        Returns:
            Image: Objeto PIL.Image con la cara dibujada
        """
        target_emotion = emotion or self.current_emotion
        img = Image.new('RGB', size, (255, 255, 255))
        draw = ImageDraw.Draw(img)
        
        # Cara
        face_width, face_height = size[0] * 0.8, size[1] * 0.8
        face_x, face_y = (size[0] - face_width) / 2, (size[1] - face_height) / 2
        draw.ellipse([face_x, face_y, face_x + face_width, face_y + face_height], 
                    fill=(255, 255, 0), outline=(0, 0, 0))
        
        # Ojos
        eye_width, eye_height = face_width * 0.15, face_height * 0.1
        left_eye_x, right_eye_x = face_x + face_width * 0.3, face_x + face_width * 0.7
        eyes_y = face_y + face_height * 0.35
        
        if target_emotion == "happy":
            # Ojos sonrientes
            draw.ellipse([left_eye_x, eyes_y, left_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            draw.ellipse([right_eye_x, eyes_y, right_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            # Boca sonriente
            mouth_width, mouth_height = face_width * 0.4, face_height * 0.2
            mouth_x, mouth_y = face_x + face_width * 0.3, face_y + face_height * 0.6
            draw.arc([mouth_x, mouth_y, mouth_x + mouth_width, mouth_y + mouth_height], 
                    start=0, end=180, fill=(0, 0, 0), width=3)
            
        elif target_emotion == "sad":
            # Ojos tristes
            draw.ellipse([left_eye_x, eyes_y, left_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            draw.ellipse([right_eye_x, eyes_y, right_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            # Boca triste
            mouth_width, mouth_height = face_width * 0.4, face_height * 0.2
            mouth_x, mouth_y = face_x + face_width * 0.3, face_y + face_height * 0.6
            draw.arc([mouth_x, mouth_y - mouth_height, mouth_x + mouth_width, mouth_y], 
                    start=180, end=360, fill=(0, 0, 0), width=3)
            
        elif target_emotion == "angry":
            # Cejas enojadas
            brow_width, brow_height = face_width * 0.2, face_height * 0.05
            draw.rectangle([left_eye_x - brow_width * 0.2, eyes_y - brow_height * 2, 
                          left_eye_x + brow_width, eyes_y - brow_height], 
                          fill=(0, 0, 0))
            draw.rectangle([right_eye_x - brow_width * 0.2, eyes_y - brow_height * 2, 
                          right_eye_x + brow_width, eyes_y - brow_height], 
                          fill=(0, 0, 0))
            # Ojos
            draw.ellipse([left_eye_x, eyes_y, left_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(255, 0, 0))
            draw.ellipse([right_eye_x, eyes_y, right_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(255, 0, 0))
            # Boca enojada
            mouth_width, mouth_height = face_width * 0.4, face_height * 0.1
            mouth_x, mouth_y = face_x + face_width * 0.3, face_y + face_height * 0.65
            draw.rectangle([mouth_x, mouth_y, mouth_x + mouth_width, mouth_y + mouth_height], 
                          fill=(0, 0, 0))
            
        elif target_emotion == "surprised":
            # Ojos muy abiertos
            eye_width, eye_height = face_width * 0.2, face_height * 0.2
            draw.ellipse([left_eye_x, eyes_y, left_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            draw.ellipse([right_eye_x, eyes_y, right_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            # Boca abierta
            mouth_width, mouth_height = face_width * 0.3, face_height * 0.3
            mouth_x, mouth_y = face_x + face_width * 0.35, face_y + face_height * 0.6
            draw.ellipse([mouth_x, mouth_y, mouth_x + mouth_width, mouth_y + mouth_height], 
                        fill=(0, 0, 0))
            
        else:  # neutral
            # Ojos neutrales
            draw.ellipse([left_eye_x, eyes_y, left_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            draw.ellipse([right_eye_x, eyes_y, right_eye_x + eye_width, eyes_y + eye_height], 
                        fill=(0, 0, 0))
            # Boca neutral
            mouth_width, mouth_height = face_width * 0.4, face_height * 0.05
            mouth_x, mouth_y = face_x + face_width * 0.3, face_y + face_height * 0.65
            draw.rectangle([mouth_x, mouth_y, mouth_x + mouth_width, mouth_y + mouth_height], 
                          fill=(0, 0, 0))
        
        return img
# Añade a emotions.py


class EmotionRecognizer:
    def __init__(self):
        self.voice_model = self._load_voice_model()
    
    def _load_voice_model(self):
        # Ejemplo simplificado (usar un modelo preentrenado en producción)
        return {
            "happy": {"pitch_range": (200, 400), "speed": (0.15, 0.25)},
            "sad": {"pitch_range": (50, 150), "speed": (0.05, 0.15)},
            "angry": {"pitch_range": (150, 300), "energy": (0.7, 1.0)}
        }

    def analyze_voice(self, audio_path: str) -> Dict[str, float]:
        try:
            y, sr = librosa.load(audio_path, sr=None)
            
            # Extraer características del audio
            pitch = librosa.yin(y, fmin=50, fmax=500)
            pitch_mean = np.nanmean(pitch)
            speed = len(y) / sr  # Duración en segundos
            
            # Calcular probabilidades (ejemplo simplificado)
            if pitch_mean > 300:
                return {"happy": 0.8, "surprised": 0.2}
            elif pitch_mean < 100:
                return {"sad": 0.9, "neutral": 0.1}
            # ... (lógica completa para todas las emociones)
            
        except Exception as e:
            print(f"Error en análisis de voz: {e}")
            return {"neutral": 1.0} 
# Añade a emotions.py


class EmotionRecognizer:
    def __init__(self, db_path="emotion_history.db"):
        self.conn = sqlite3.connect(db_path)
        self._init_db()
    
    def _init_db(self):
        cursor = self.conn.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS emotion_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            emotion TEXT,
            confidence REAL,
            source TEXT,  # 'face', 'voice', 'text'
            timestamp DATETIME
        )
        """)
        self.conn.commit()

    def save_emotion(self, user_id: str, emotion: str, confidence: float, source: str):
        cursor = self.conn.cursor()
        cursor.execute("""
        INSERT INTO emotion_history (user_id, emotion, confidence, source, timestamp)
        VALUES (?, ?, ?, ?, ?)
        """, (user_id, emotion, confidence, source, datetime.now()))
        self.conn.commit()

    def get_emotional_trend(self, user_id: str, days: int = 7) -> Dict[str, float]:
        cursor = self.conn.cursor()
        cursor.execute("""
        SELECT emotion, AVG(confidence) 
        FROM emotion_history 
        WHERE user_id = ? AND timestamp > datetime('now', ?)
        GROUP BY emotion
        """, (user_id, f"-{days} days"))
        
        return {row[0]: row[1] for row in cursor.fetchall()}           