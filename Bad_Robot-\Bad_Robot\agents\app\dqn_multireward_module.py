# agi_system/dqn_multireward_module.py
import random
from collections import defaultdict

class DQNMultiRewardModule:
    def __init__(self, actions, learning_rate=0.1, discount_factor=0.9,
                 exploration_rate=1.0, exploration_decay=0.995, min_exploration_rate=0.01,
                 reward_weights=None):
        self.actions = actions  # Lista de posibles IDs de acción (e.g., 0, 1, 2)
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = exploration_rate
        self.epsilon_decay = exploration_decay
        self.min_epsilon = min_exploration_rate
        self.q_table = defaultdict(lambda: defaultdict(float))  # state_repr -> action_id -> q_value

        if reward_weights is None:
            self.reward_weights = {"completion": 1.0, "efficiency": 0.1, "info_gain": 0.5, "error": -1.0}
        else:
            self.reward_weights = reward_weights
        print(f"[DQN] Módulo DQN Multi-Recompensa inicializado con {len(actions)} acciones y pesos: {self.reward_weights}")

    def choose_action(self, state_representation, available_action_ids):
        """Elige una acción usando epsilon-greedy sobre los IDs de acción disponibles."""
        if not available_action_ids:
            print("[DQN] Advertencia: No hay IDs de acción disponibles.")
            return None # O una acción por defecto como 'idle'

        if random.uniform(0, 1) < self.epsilon:
            action_id = random.choice(available_action_ids)
            # print(f"[DQN] Acción elegida por exploración: {action_id}")
        else:
            q_values_for_state = self.q_table[state_representation]
            # Filtrar Q-values solo para acciones disponibles
            valid_q_values = {act_id: q_values_for_state.get(act_id, 0.0) for act_id in available_action_ids}
            if not valid_q_values:
                action_id = random.choice(available_action_ids) # Si no hay Q-values, explorar
                # print(f"[DQN] Acción elegida al azar (estado nuevo sin Q-values): {action_id}")
            else:
                action_id = max(valid_q_values, key=valid_q_values.get)
                # print(f"[DQN] Acción elegida por explotación: {action_id}")
        return action_id

    def learn(self, state_repr, action_id, reward_components, next_state_repr, next_available_action_ids):
        """Actualiza la Q-table basado en la experiencia."""
        total_reward = self._calculate_total_reward(reward_components)

        if not next_available_action_ids: # Estado terminal o sin acciones
            max_q_next_state = 0.0
        else:
            q_values_for_next_state = self.q_table[next_state_repr]
            valid_q_values_next = {act_id: q_values_for_next_state.get(act_id, 0.0) for act_id in next_available_action_ids}
            if not valid_q_values_next:
                 max_q_next_state = 0.0
            else:
                max_q_next_state = max(valid_q_values_next.values())

        current_q = self.q_table[state_repr][action_id]
        new_q = current_q + self.lr * (total_reward + self.gamma * max_q_next_state - current_q)
        self.q_table[state_repr][action_id] = new_q
        # print(f"[DQN] Q-Table actualizada para ({state_repr}, {action_id}): {current_q:.2f} -> {new_q:.2f} (Recompensa: {total_reward:.2f})")

    def _calculate_total_reward(self, reward_components):
        """Calcula la recompensa total ponderada."""
        total_reward = 0
        for r_name, r_value in reward_components.items():
            total_reward += r_value * self.reward_weights.get(r_name, 0)
        return total_reward

    def decay_exploration_rate(self):
        self.epsilon = max(self.min_epsilon, self.epsilon * self.epsilon_decay)