### agentes/genetico.py - Operaciones evolutivas simples para los agentes

import random

def mutar_genotipo(genotipo, tasa=0.1):
    nuevo = genotipo.copy()
    for key in nuevo:
        if random.random() < tasa:
            nuevo[key] += random.uniform(-0.1, 0.1)
            nuevo[key] = max(0, min(nuevo[key], 1))  # mantener en [0,1]
    return nuevo

def cruzar_genotipos(g1, g2):
    hijo = {}
    for key in g1:
        hijo[key] = random.choice([g1[key], g2[key]])
    return hijo

def seleccionar_mejores(agentes, recompensas, top_k=2):
    evaluados = [(ag, recompensas.get(ag.id, 0)) for ag in agentes]
    evaluados.sort(key=lambda x: x[1], reverse=True)
    return [x[0] for x in evaluados[:top_k]]
