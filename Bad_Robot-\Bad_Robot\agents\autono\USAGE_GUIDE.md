# AGI Assistant Usage Guide

## Basic Commands

1. **Start the assistant**:
```bash
python main.py
```

2. **Voice commands**:
- Speak naturally after hearing the startup beep
- Example commands:
  - "Search for AI news"
  - "Open browser to github.com"
  - "What's my CPU usage?"

## API Endpoints

The system provides these REST endpoints:
- `GET /status` - System status
- `POST /command` - Submit text commands
- `GET /search?q=query` - Web search

## Development Commands

```bash
# Run tests
pytest tests/

# Generate documentation
mkdocs serve

# Format code
black .
```

## Safety Controls

1. Emergency stop:
```python
# Press Ctrl+C in the terminal running main.py
```

2. Sandbox inspection:
```bash
docker ps  # View running containers
docker logs [container_id]
```

## Troubleshooting

1. Microphone not working:
- Check system permissions
- Verify pyaudio installation

2. Browser issues:
- Run `playwright install`
- Check firewall settings
