from flask import Flask, render_template
from flask_socketio import SocketIO
import openai

app = Flask(__name__)
socketio = SocketIO(app)

@app.route('/')
def home():
    return render_template('index.html')

@socketio.on('message')
def handle_message(data):
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "system", "content": "Eres un asistente AGI"},
                 {"role": "user", "content": data['message']}]
    )
    socketio.emit('response', {'text': response.choices[0].message['content']})

if __name__ == '__main__':
    socketio.run(app, debug=True)