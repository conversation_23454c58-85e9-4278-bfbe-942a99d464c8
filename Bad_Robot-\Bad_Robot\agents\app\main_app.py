El # main.py - Sistema AGI Unificado Completo Optimizado
import os
import json
import base64
import logging
import threading
import time
import uuid
import random
import socket
import ast
import shutil
from datetime import datetime
from collections import deque
from threading import Thread, Event
import tensorflow as tf
from collections import deque
import numpy as np
import pygame
import pyaudio
import audioop
import cv2
import requests
import torch
import torch.nn as nn
import torch.optim as optim
import zeroconf
from flask import Flask, jsonify, request, render_template_string
from flask_socketio import SocketIO
from flask_socketio import emit
from werkzeug.utils import secure_filename
from flask_sqlalchemy import SQLAlchemy
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM, TrainingArguments
from datasets import load_dataset
from trl import SFTTrainer
from peft import LoraConfig
from llama_cpp import Llama
# Load model directly
from transformers import AutoModel
model = AutoModel.from_pretrained("state-spaces/mamba2-370m")
from model_finder_api import model_api
app.register_blueprint(model_api)
import json
from flask import jsonify, Blueprint
from flask_socketio import emit
# Load model directly
from transformers import AutoModel
model = AutoModel.from_pretrained("state-spaces/mamba-370m")
from flask import jsonify, Blueprint
from flask_socketio import emit
# Change these imports
# from model_finder_api import model_api # This was the Blueprint
from model_finder_api import model_api, register_socketio_events, ModelFinder # Import the registration function and class
# Use the stubs
from Autonoagent_Unificado import AGIAPI
from Superautonomatics import GameConnector, DQNAgent as SuperautoDQNAgent # Rename to avoid clash with internal DQNAgent

from flask import Flask, request, jsonify
from flask_socketio import SocketIO, emit
import os, base64, time, io
from gtts import gTTS
import speech_recognition as sr
from pydub import AudioSegment
import tempfile
import requests
import asyncio
import websockets
import shutil # For copying files
# Ejemplo de integración con DQNMultiRewardModule
    from dqn_multireward_module import DQNMultiRewardModule
dqn_module = DQNMultiRewardModule(actions=[0, 1, 2])
    action = dqn_module.choose_action(state_representation=prompt, available_action_ids=[0, 1, 2])
    
    emit('agi_response', {'content': f"Acción recomendada: {action}. {respuesta}"})

# ... (Flask app setup) ...
# app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")  # Permite conexiones desde cualquier origen (para desarrollo)
# ...
# app.register_blueprint(model_api) # This should still work with the blueprint from model_finder_api.py

# Initialize SocketIO events from model_finder_api
# This needs to be done AFTER socketio is initialized on app
# register_socketio_events(socketio, app) # Do this inside AGICore or after AGICore init.

# ... Inside AGICore __init__ ...

# Conectar al DQN para capturar imagen
def obtener_imagen():
    res = requests.get("http://localhost:7000/vision")
    print(res.json())

# Capturar voz desde micrófono
def escuchar_voz():
    res = requests.get("http://localhost:7000/voice")
    print("Texto detectado:", res.json())

# WebSocket de chat con el agente
async def conversar():
    async with websockets.connect("ws://localhost:7000/chat") as ws:
        await ws.send("¿Cuál es tu propósito?")
        resp = await ws.recv()
        print("Respuesta del agente:", resp)




# app.py
from flask import Flask, request
from flask_socketio import SocketIO
import base64
import os
from Superautonomatics import GameConnector, DQNAgent

dqn = DQNAgent()
dqn.load()
game = GameConnector(dqn)

state = [0.5, 0.1, 0.0, 1.0]
reward = 0.8
done = False
action = game.step(state, reward, done)
print(f"Acción del AGI: {action}")

from Autonoagent_Unificado import AGIAPI

agi = AGIAPI()

state = [0.5, 0.1, 0.0, 1.0]
reward = 0.8
done = False
action = agi.process_game_step(state, reward, done)
print(f"Acción elegida: {action}")


app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@socketio.on('audio_upload')
def handle_audio(data):
    try:
        file_path = os.path.join(UPLOAD_FOLDER, f'audio_{int(time.time())}.wav')
        with open(file_path, 'wb') as f:
            f.write(base64.b64decode(data['data']))
    except Exception as e:
        print(f"Error procesando audio: {str(e)}")

@socketio.on('file_upload')
def handle_file(data):
    try:
        file_path = os.path.join(UPLOAD_FOLDER, data['name'])
        with open(file_path, 'wb') as f:
            f.write(base64.b64decode(data['data']))
        emit('upload_success', {'filename': data['name']})
    except Exception as e:
        emit('upload_error', {'filename': data['name']})
        print(f"Error subiendo archivo: {str(e)}")

if __name__ == '__main__':
    socketio.run(app, port=5000)
# Configuración inicial
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///players.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.urandom(24)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['AUDIO_FOLDER'] = 'audio'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['AUDIO_FOLDER'], exist_ok=True)
from model_finder_api import model_api
app = Flask(__name__)
app.config['SECRET_KEY'] = 'supersecretkey'
socketio = SocketIO(app, cors_allowed_origins="*"


app.register_blueprint(model_api)
GET /api/models
fetch('/api/models')
db = SQLAlchemy(app)
socketio = SocketIO(app, cors_allowed_origins="*")
logging.basicConfig(level=logging.INFO)


user_response_modes = {}
recognizer = sr.Recognizer()
<script>
@socketio.on('connect')
def on_connect():
    print("Cliente conectado")
    emit('debug', {'msg': 'Conectado al servidor AGI'})

@socketio.on('set_response_mode')
def set_response_mode(data):
    sid = request.sid
    mode = data.get('mode', 'text')
    user_response_modes[sid] = mode
    print(f"[Modo respuesta actualizado] {sid} => {mode}")

@socketio.on('prompt_to_model')
def handle_prompt(data):
    prompt = data.get('prompt', '')
    sid = request.sid
    print(f"Prompt recibido: {prompt}")

    respuesta = f"Respuesta AGI para: {prompt}"
    emit('agi_response', {'content': respuesta})

    modo = user_response_modes.get(sid, 'text')
    if modo in ['voice', 'both']:
        tts = gTTS(respuesta, lang='es')
        buf = io.BytesIO()
        tts.write_to_fp(buf)
        audio_data = base64.b64encode(buf.getvalue()).decode('utf-8')
        emit('audio_response', {'audio_base64': audio_data})

@socketio.on('audio_stream_chunk')
def handle_audio_chunk(data):
    print("Chunk de audio recibido (procesando STT real)")
    audio_base64 = data.get('audio_base64')
    audio_bytes = base64.b64decode(audio_base64)

    with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as temp_audio:
        temp_audio.write(audio_bytes)
        temp_audio.flush()

        # Convertir webm a wav para SpeechRecognition
        sound = AudioSegment.from_file(temp_audio.name, format="webm")
        wav_path = temp_audio.name.replace('.webm', '.wav')
        sound.export(wav_path, format="wav")

        with sr.AudioFile(wav_path) as source:
            audio = recognizer.record(source)
            try:
                text = recognizer.recognize_google(audio, language='es-ES')
                print(f"Texto reconocido: {text}")
                emit('agi_response', {'content': text})
            except sr.UnknownValueError:
                emit('agi_response', {'content': 'No se entendió el audio.'})
            except sr.RequestError as e:
                emit('agi_response', {'content': f'Error STT: {str(e)}'})

@socketio.on('disconnect')
def on_disconnect():
    sid = request.sid
    print(f"Cliente desconectado: {sid}")
    if sid in user_response_modes:
        del user_response_modes[sid]

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000)

# En main_app.py
@socketio.on('process_message')
def handle_agi_message(data):
    # ... (lógica existente)
    
    # Respuesta estructurada
    structured_response = {
        'text': "He procesado tu solicitud",
        'dqn_action': str(dqn_action),
        'symbolic': str(symbolic_result),
        'timestamp': datetime.now().isoformat()
    }
    
    emit('agi_structured_response', structured_response)

# ==================== Modelos de Base de Datos ====================
class Player(db.Model):
    id = db.Column(db.String(36), primary_key=True)
    username = db.Column(db.String(50), unique=True)
    rating = db.Column(db.Float, default=1000.0)
    ai_level = db.Column(db.Integer, default=1)
    avatar_data = db.Column(db.Text)
    achievements = db.Column(db.Text)
    match_history = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class GameSession(db.Model):
    id = db.Column(db.String(36), primary_key=True)
    players = db.Column(db.Text)
    winner = db.Column(db.String(36))
    strategy_used = db.Column(db.String(20))
    duration = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Achievement(db.Model):
    id = db.Column(db.String(36), primary_key=True)
    name = db.Column(db.String(100))
    description = db.Column(db.Text)
    criteria = db.Column(db.Text)

# ==================== Componentes del Juego ====================
class GameConfig:
    WIDTH, HEIGHT = 800, 600
    PLAYER_SPEED = 5
    MAX_PLAYERS = 4
    REWARD_TYPES = ["velocidad", "tamaño", "puntos_extra", "habilidad_especial"]
// ================ CONFIGURACIÓN AVANZADA ================ //
class GameConfig {
    static MODES = {
        SURVIVAL: {
            description: "Sobrevive el máximo tiempo posible",
            enemySpawnRate: 0.03,
            rewardSpawnRate: 0.02,
            duration: 120 // segundos
        },
        CAPTURE_THE_FLAG: {
            description: "Captura la bandera 5 veces",
            flagsRequired: 5,
            rewardSpawnRate: 0.01,
            flagPosition: null
        },
        POINT_RACE: {
            description: "Alcanza 1000 puntos primero",
            targetPoints: 1000,
            pointMultiplier: 1.5
        }
    };

    static REWARD_TYPES = [
        { type: "invisibilidad", duration: 5, color: "#4a4a4a" },
        { type: "teletransportacion", uses: 3, color: "#9b59b6" },
        { type: "atraccion_puntos", duration: 10, color: "#f1c40f" },
        { type: "escudo_temporal", duration: 8, color: "#3498db" },
        { type: "duplicador_puntos", duration: 15, color: "#2ecc71" }
    ];
}
// ================ SISTEMA DE NIVELES IA ================ //
class AIController {
    constructor() {
        this.level = 1;
        this.xp = 0;
        this.unlockedMechanics = new Set(['basic_rewards']);
        this.learningRate = 0.1;
        this.rewardKnowledge = new Map();
    }

    gainXP(amount) {
        this.xp += amount;
        while (this.xp >= this.getNextLevelXP()) {
            this.levelUp();
        }
    }

    getNextLevelXP() {
        return 1000 * Math.pow(1.5, this.level - 1);
    }

    levelUp() {
        this.level++;
        this.unlockNewMechanic();
        this.learningRate *= 0.95;
    }

    unlockNewMechanic() {
        const newMechanics = [
            'combo_rewards',
            'dynamic_difficulty',
            'elemental_effects',
            'team_strategies',
            'meta_rewards'
        ];
        if (this.level <= newMechanics.length) {
            this.unlockedMechanics.add(newMechanics[this.level - 2]);
        }
    }

    analyzePlayerBehavior(actions) {
        // Aprendizaje por refuerzo para personalizaciones
        actions.forEach(({type, timestamp}) => {
            const knowledge = this.rewardKnowledge.get(type) || {count: 0, lastUsed: 0};
            knowledge.count++;
            knowledge.lastUsed = timestamp;
            this.rewardKnowledge.set(type, knowledge);
        });
    }

    getPopularCustomizations() {
        return [...this.rewardKnowledge.entries()]
            .sort((a, b) => b[1].count - a[1].count)
            .slice(0, 3);
    }
}

// ================ PERSONALIZACIÓN DE AVATARES ================ //
class AvatarSystem {
    constructor() {
        this.availableItems = {
            hats: ['baseball_cap', 'wizard_hat', 'crown'],
            colors: ['red', 'blue', 'gold'],
            effects: ['glow', 'sparks', 'aura']
        };
        this.playerInventory = new Set();
        this.popularityTracker = new Map();
    }

    unlockItem(player, itemType, itemName) {
        if (player.points >= this.getItemCost(itemType)) {
            player.points -= this.getItemCost(itemType);
            this.playerInventory.add(`${itemType}_${itemName}`);
            this.trackPopularity(itemType, itemName);
        }
    }

    getItemCost(itemType) {
        const baseCosts = { hats: 500, colors: 300, effects: 700 };
        return baseCosts[itemType];
    }

    trackPopularity(itemType, itemName) {
        const key = `${itemType}_${itemName}`;
        this.popularityTracker.set(key, (this.popularityTracker.get(key) || 0) + 1);
    }

    applyAvatarCustomization(player) {
        const avatar = document.getElementById('player-avatar');
        avatar.className = '';
        
        this.playerInventory.forEach(item => {
            const [type, name] = item.split('_');
            avatar.classList.add(`${type}-${name}`);
        });
    }
}

// ================ SISTEMA DE EVENTOS ESPECIALES ================ //
class EventManager {
    constructor() {
        this.activeEvents = [];
        this.eventHistory = [];
        this.playerBehaviorMetrics = {
            scoreRate: 0,
            itemUsage: new Map(),
            riskActions: 0
        };
    }

    checkForEvents() {
        const now = Date.now();
        
        // Evento: Doble puntos
        if (this.playerBehaviorMetrics.scoreRate > 50 && 
            !this.activeEvents.includes('double_points')) {
            this.triggerEvent('double_points', 30);
        }

        // Evento: Todos contra todos
        if (this.playerBehaviorMetrics.riskActions > 10) {
            this.triggerEvent('free_for_all', 45);
        }
    }

    triggerEvent(type, duration) {
        this.activeEvents.push(type);
        this.eventHistory.push({type, timestamp: Date.now()});
        
        setTimeout(() => {
            this.activeEvents = this.activeEvents.filter(e => e !== type);
        }, duration * 1000);
    }

    updateBehaviorMetrics(gameState) {
        // Actualizar métricas cada segundo
        this.playerBehaviorMetrics.scoreRate = gameState.score / (gameState.timeElapsed / 60);
        gameState.usedItems.forEach(item => {
            this.playerBehaviorMetrics.itemUsage.set(item, 
                (this.playerBehaviorMetrics.itemUsage.get(item) || 0) + 1);
        });
    }
}

// ================ SISTEMA MULTIAGENTE ================ //
class MultiAgentSystem {
    constructor() {
        this.agents = [
            new StrategyAgent('balanced'),
            new StrategyAgent('aggressive'),
            new StrategyAgent('defensive')
        ];
        this.currentAgentIndex = 0;
        this.voteHistory = [];
    }

    rotateAgent() {
        this.currentAgentIndex = (this.currentAgentIndex + 1) % this.agents.length;
    }

    recordVote(playerId, agentIndex) {
        this.voteHistory.push({playerId, agentIndex, timestamp: Date.now()});
        this.agents[agentIndex].popularityScore++;
    }

    getCurrentStrategy() {
        return this.agents[this.currentAgentIndex].currentStrategy();
    }
}

class StrategyAgent {
    constructor(strategyType) {
        this.strategyType = strategyType;
        this.popularityScore = 0;
        this.learningModel = new ReinforcementModel();
    }

    currentStrategy() {
        switch(this.strategyType) {
            case 'balanced':
                return { rewardFrequency: 0.05, riskFactor: 0.5 };
            case 'aggressive':
                return { rewardFrequency: 0.1, riskFactor: 0.8 };
            case 'defensive':
                return { rewardFrequency: 0.03, riskFactor: 0.2 };
        }
    }
}

// ================ INTEGRACIÓN EN EL JUEGO ================ //
class EnhancedDQNGame extends DQNGame {
    constructor(canvas) {
        super(canvas);
        this.aiController = new AIController();
        this.avatarSystem = new AvatarSystem();
        this.eventManager = new EventManager();
        this.multiAgentSystem = new MultiAgentSystem();
        this.playerMetadata = new Map();
    }

    applyGameModifiers() {
        // Aplicar efectos de nivel de IA
        if (this.aiController.unlockedMechanics.has('combo_rewards')) {
            this.enableComboRewards();
        }

        // Aplicar estrategia del agente actual
        const strategy = this.multiAgentSystem.getCurrentStrategy();
        this.rewardSpawnRate = strategy.rewardFrequency;
    }

    handleSpecialEvents() {
        if (this.eventManager.activeEvents.includes('double_points')) {
            this.player.scoreMultiplier *= 2;
        }
    }

    updatePlayerMetadata(playerId, actions) {
        const metadata = this.playerMetadata.get(playerId) || { totalPoints: 0, preferences: {} };
        metadata.totalPoints += this.score;
        metadata.lastPlayed = Date.now();
        this.playerMetadata.set(playerId, metadata);
        this.aiController.analyzePlayerBehavior(actions);
    }
}

// ================ INTERFAZ DE USUARIO MEJORADA ================ //
class EnhancedGameUI extends GameUI {
    constructor(game) {
        super(game);
        this.initVotingSystem();
        this.initAvatarCustomization();
    }

    initVotingSystem() {
        document.getElementById('vote-agent').addEventListener('click', () => {
            const agentIndex = document.getElementById('agent-select').value;
            this.game.multiAgentSystem.recordVote('player1', agentIndex);
            this.updateAgentDisplay();
        });
    }

    initAvatarCustomization() {
        document.getElementById('avatar-shop').addEventListener('click', (e) => {
            if (e.target.classList.contains('buy-item')) {
                const itemType = e.target.dataset.itemType;
                const itemName = e.target.dataset.itemName;
                this.game.avatarSystem.unlockItem(this.game.player, itemType, itemName);
                this.updateAvatarShop();
            }
        });
    }

    updateAgentDisplay() {
        document.getElementById('current-agent').textContent = 
            this.game.multiAgentSystem.agents[
                this.game.multiAgentSystem.currentAgentIndex
            ].strategyType;
    }

    updateAvatarShop() {
        const shop = document.getElementById('avatar-items');
        shop.innerHTML = '';
        
        this.game.avatarSystem.availableItems.forEach((items, type) => {
            items.forEach(item => {
                const button = document.createElement('button');
                button.className = 'buy-item';
                button.dataset.itemType = type;
                button.dataset.itemName = item;
                button.textContent = `${item} (${this.game.avatarSystem.getItemCost(type)} pts)`;
                shop.appendChild(button);
            });
        });
    }
}

// ================ NÚCLEO DEL JUEGO ================ //
class DQNGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.activeMode = null;
        this.activeRewards = [];
        this.player = {
            x: canvas.width/2, 
            y: canvas.height-30,
            size: 20,
            shield: false,
            invisible: false,
            scoreMultiplier: 1
        };
        this.enemies = [];
        this.rewards = [];
        this.flags = [];
        this.score = 0;
        this.timeLeft = 0;
        this.isGameRunning = false;
    }

    initGame(mode) {
        this.activeMode = mode;
        this.resetGameState();
        
        switch(mode) {
            case 'SURVIVAL':
                this.timeLeft = GameConfig.MODES.SURVIVAL.duration;
                break;
                
            case 'CAPTURE_THE_FLAG':
                this.spawnFlag();
                break;
        }
    }

    spawnFlag() {
        this.flags.push({
            x: Math.random() * this.canvas.width,
            y: 30,
            size: 25,
            collected: false
        });
    }

    applyReward(reward) {
        switch(reward.type) {
            case 'invisibilidad':
                this.player.invisible = true;
                setTimeout(() => this.player.invisible = false, reward.duration * 1000);
                break;
                
            case 'teletransportacion':
                this.player.x = Math.random() * this.canvas.width;
                break;
                
            case 'atraccion_puntos':
                this.attractPoints(true);
                setTimeout(() => this.attractPoints(false), reward.duration * 1000);
                break;
                
            case 'escudo_temporal':
                this.player.shield = true;
                setTimeout(() => this.player.shield = false, reward.duration * 1000);
                break;
                
            case 'duplicador_puntos':
                const originalMultiplier = this.player.scoreMultiplier;
                this.player.scoreMultiplier *= 2;
                setTimeout(() => this.player.scoreMultiplier = originalMultiplier, reward.duration * 1000);
                break;
        }
    }

    attractPoints(activate) {
        if(activate) {
            this.rewards.forEach(reward => {
                const dx = this.player.x - reward.x;
                const dy = this.player.y - reward.y;
                const distance = Math.sqrt(dx*dx + dy*dy);
                
                if(distance < 300) {
                    reward.x += dx * 0.05;
                    reward.y += dy * 0.05;
                }
            });
        }
    }

    checkGameEnd() {
        switch(this.activeMode) {
            case 'SURVIVAL':
                return this.timeLeft <= 0;
                
            case 'CAPTURE_THE_FLAG':
                return this.score >= GameConfig.MODES.CAPTURE_THE_FLAG.flagsRequired;
                
            case 'POINT_RACE':
                return this.score >= GameConfig.MODES.POINT_RACE.targetPoints;
        }
        return false;
    }

    update(deltaTime) {
        if(!this.isGameRunning) return;

        // Lógica específica del modo
        switch(this.activeMode) {
            case 'SURVIVAL':
                this.timeLeft -= deltaTime;
                break;
        }

        // Actualizar elementos del juego
        this.updateEnemies();
        this.updateRewards();
        this.checkCollisions();
        
        if(this.checkGameEnd()) {
            this.endGame();
        }
    }

    // ... (métodos restantes de actualización y renderizado)
}

// Ejemplo de uso de recompensas
game.applyReward({
    type: 'duplicador_puntos',
    duration: 15
});

// Cambiar modo de juego
game.initGame('CAPTURE_THE_FLAG');


// ================ INTERFAZ DE USUARIO ================ //
class GameUI {
    constructor(game) {
        this.game = game;
        this.modeSelector = document.getElementById('game-mode');
        this.scoreDisplay = document.getElementById('score');
        this.timeDisplay = document.getElementById('time');
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.getElementById('start-game').addEventListener('click', () => {
            const selectedMode = this.modeSelector.value;
            this.game.initGame(selectedMode);
            this.game.isGameRunning = true;
            this.updateUI();
        });
    }

    updateUI() {
        this.scoreDisplay.textContent = this.game.score;
        
        if(this.game.activeMode === 'SURVIVAL') {
            this.timeDisplay.textContent = Math.ceil(this.game.timeLeft);
        }
    }
}

// ================ INICIALIZACIÓN ================ //
const canvas = document.getElementById('game-canvas');
const game = new DQNGame(canvas);
const gameUI = new GameUI(game);

// Modificar HTML para incluir selector de modos
/*
<div class="game-section">
    <h2>DQN Learning Game</h2>
    <select id="game-mode">
        <option value="SURVIVAL">Supervivencia</option>
        <option value="CAPTURE_THE_FLAG">Captura la Bandera</option>
        <option value="POINT_RACE">Carrera de Puntos</option>
    </select>
    <canvas id="game-canvas" width="400" height="600"></canvas>
    <div class="game-stats">
        <p>Puntuación: <span id="score">0</span></p>
        <p id="time-display">Tiempo: <span id="time">0</span></p>
    </div>
    <button id="start-game">Iniciar Juego</button>
</div>
*/

class Player:
    def __init__(self, id, x, y):
        self.id = id
        self.x = x
        self.y = y
        self.score = 0
        self.rewards = []
        self.color = (random.randint(50, 255), random.randint(50, 255), random.randint(50, 255))
        self.radius = 15
        self.speed = GameConfig.PLAYER_SPEED
    
    def apply_reward(self, reward_type):
        if reward_type == "velocidad":
            self.speed += 1
        elif reward_type == "tamaño":
            self.radius += 2
        elif reward_type == "puntos_extra":
            self.score += 10
        elif reward_type == "habilidad_especial":
            pass
        self.rewards.append(reward_type)
    
    def move(self, dx, dy):
        self.x = max(self.radius, min(GameConfig.WIDTH - self.radius, self.x + dx * self.speed))
        self.y = max(self.radius, min(GameConfig.HEIGHT - self.radius, self.y + dy * self.speed))

# ==================== Componentes de IA ====================
class DQNAgent:
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.95
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.model = self._build_model()
    
    def _build_model(self):
        model = nn.Sequential(
            nn.Linear(self.state_size, 24),
            nn.ReLU(),
            nn.Linear(24, 24),
            nn.ReLU(),
            nn.Linear(24, self.action_size)
        )
        return model
     

     def _build_model(self):
        model = tf.keras.Sequential()
        model.add(tf.keras.layers.Dense(24, input_dim=self.state_size,       activation='relu'))
        model.add(tf.keras.layers.Dense(24, activation='relu'))
        model.add(tf.keras.layers.Dense(self.action_size,        activation='linear'))
        model.compile(loss='mse', optimizer=tf.keras.optimizers.Adam       (lr=self.learning_rate))
        return model
    
    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state):
        if np.random.rand() <= self.epsilon:
            return np.random.choice(self.action_size)
        state_tensor = torch.FloatTensor(state)
        with torch.no_grad():
            act_values = self.model(state_tensor)
        return torch.argmax(act_values).item()
    
    def replay(self, batch_size=32):
        if len(self.memory) < batch_size:
            return
        minibatch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state, done in minibatch:
            target = reward
            if not done:
                next_state_tensor = torch.FloatTensor(next_state)
                target = reward + self.gamma * torch.max(self.model(next_state_tensor)).item()
            state_tensor = torch.FloatTensor(state)
            target_f = self.model(state_tensor)
            target_f[action] = target
            self.model.train()
            optimizer = optim.Adam(self.model.parameters())
            optimizer.zero_grad()
            loss = nn.MSELoss()(self.model(state_tensor), target_f)
            loss.backward()
            optimizer.step()
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay


def replay(self, batch_size):
        minibatch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state, done in minibatch:
            target = reward
            if not done:
                target = reward + self.gamma * np.amax(self.model.predict(next_state)[0])
            target_f = self.model.predict(state)
            target_f[0][action] = target
            self.model.fit(state, target_f, epochs=1, verbose=0)
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

   


class MambaLanguageBlock(nn.Module):
    def __init__(self, d_model=512, n_layers=4):
        super().__init__()
        self.layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model * 2),
                nn.ReLU(),
                nn.Linear(d_model * 2, d_model)
            ) for _ in range(n_layers)
        ])
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x, context=None):
        for layer in self.layers:
            x = layer(x)
        return self.norm(x)


# Entidad Jugador
class Player:
    def __init__(self, id, x, y):
        self.id = id
        self.x = x
        self.y = y
        self.score = 0
        self.rewards = []
        self.color = (random.randint(50, 255), random.randint(50, 255), random.randint(50, 255))
        self.radius = 15
        self.speed = GameConfig.PLAYER_SPEED


    def apply_reward(self, reward_type):
        if reward_type == "velocidad":
            self.speed += 1
        elif reward_type == "tamaño":
            self.radius += 2
        elif reward_type == "puntos_extra":
            self.score += 10
        elif reward_type == "habilidad_especial":
            # Habilidad única temporal
            pass
        self.rewards.append(reward_type)
    

   def move(self, dx, dy):
        self.x = max(self.radius, min(GameConfig.WIDTH - self.radius, self.x + dx * self.speed))
        self.y = max(self.radius, min(GameConfig.HEIGHT - self.radius, self.y + dy * self.speed))


   # Servidor del Juego
class GameServer:
    def __init__(self):
        self.players = {}
        self.rewards = []
        self.dqn_agent = DQNAgent(len(GameConfig.REWARD_TYPES), len(GameConfig.REWARD_TYPES))
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.bind(('localhost', 5555))
        self.server_socket.listen(5)
        print("Servidor iniciado. Esperando conexiones...")
GameConfig.REWARD_TYPES.extend([
    "invisibilidad",
    "teletransportacion",
    "atraccion_puntos",
    "escudo_temporal",
    "duplicador_puntos"
])

# Hilo para aceptar conexiones
        self.accept_thread = threading.Thread(target=self.accept_connections)
        self.accept_thread.start()
        
        # Hilo para actualizar el juego
        self.game_thread = threading.Thread(target=self.run_game)
        self.game_thread.start()

 def accept_connections(self):
        while True:
            client_socket, addr = self.server_socket.accept()
            player_id = len(self.players) + 1
            if player_id > GameConfig.MAX_PLAYERS:
                client_socket.send(json.dumps({"error": "Límite de jugadores alcanzado"}).encode())
                client_socket.close()
                continue
            
            new_player = Player(player_id, 
                               random.randint(50, GameConfig.WIDTH-50), 
                               random.randint(50, GameConfig.HEIGHT-50))
self.players[player_id] = {"player": new_player, "socket": client_socket}
            
            # Enviar confirmación al cliente
            client_socket.send(json.dumps({
                "player_id": player_id,
                "color": new_player.color,
                "position": [new_player.x, new_player.y]
            }).encode())
            
 # Hilo para manejar al jugador
            threading.Thread(target=self.handle_player, args=(player_id,)).start()
    
    def handle_player(self, player_id):
        player_data = self.players[player_id]
        socket = player_data["socket"]
        player = player_data["player"]
        
        while True:
            try:
                data = socket.recv(1024).decode()
                if not data:
                    break
                
                command = json.loads(data)
                if command["action"] == "move":
                    player.move(command["dx"], command["dy"])
         elif command["action"] == "collect":
                    # Lógica para recolectar recompensas
                    pass
                
                # Enviar estado actualizado del juego
                self.send_game_state()
            
            except Exception as e:
                print(f"Error con jugador {player_id}: {e}")
                break
          # Eliminar jugador desconectado
        del self.players[player_id]
        socket.close()
        print(f"Jugador {player_id} desconectado")
    
    def send_game_state(self):
        game_state = {
            "players": [],
            "rewards": self.rewards
        }
        
        for pid, data in self.players.items():
            player = data["player"]
            game_state["players"].append({
                "id": player.id,
                "x": player.x,
                "y": player.y,
                "score": player.score,
                "color": player.color,
                "radius": player.radius
            })

   for player_data in self.players.values():
            try:
                player_data["socket"].send(json.dumps(game_state).encode())
            except:
                continue
    
    def generate_reward(self):
        if random.random() < 0.05 and len(self.rewards) < 5:  # 5% de chance por frame, máximo 5 recompensas
            reward_type = random.choice(GameConfig.REWARD_TYPES)
            self.rewards.append({
                "x": random.randint(20, GameConfig.WIDTH-20),

"y": random.randint(20, GameConfig.HEIGHT-20),
                "type": reward_type,
                "id": len(self.rewards) + 1
            })
    
    def run_game(self):
        clock = pygame.time.Clock()
        while True:
            self.generate_reward()
            
            # Entrenar DQN periódicamente
            if len(self.dqn_agent.memory) > 32:
                self.dqn_agent.replay(32)
            
            # Actualizar estado del juego para DQN
            if self.players:
                self.update_dqn_state()
            
            clock.tick(30)
 def update_dqn_state(self):
        # Estado actual: distribución de puntajes y recompensas activas
        scores = [p["player"].score for p in self.players.values()]
        avg_score = sum(scores) / len(scores) if scores else 0
        score_diff = max(scores) - min(scores) if len(scores) > 1 else 0
        
        state = np.array([
            avg_score,
            score_diff,
            len(self.rewards),
            sum(1 for r in self.rewards if r["type"] == "velocidad")
        ])
   
        # El agente DQN elige una acción (tipo de recompensa a enfatizar)
        action = self.dqn_agent.act(state.reshape(1, -1))
        chosen_reward = GameConfig.REWARD_TYPES[action]
        
        # Aumentar probabilidad de la recompensa elegida
        for _ in range(3):  # Generar 3 de la recompensa elegida
            if random.random() < 0.8:  # 80% de chance
                self.rewards.append({
                    "x": random.randint(20, GameConfig.WIDTH-20),
                    "y": random.randint(20, GameConfig.HEIGHT-20),

 "type": chosen_reward,
                    "id": len(self.rewards) + 1
                })
        
        # Guardar en memoria para aprendizaje
        next_state = np.array([
            avg_score + 1,  # Estimación simple
            score_diff + 0.5,
            len(self.rewards),
            sum(1 for r in self.rewards if r["type"] == "velocidad")
        ])
        
        # Recompensa para DQN: balance en el juego (diferencia de puntajes más pequeña es mejor)
        reward = 1 / (1 + score_diff)
        self.dqn_agent.remember(state, action, reward, next_state, False)
# Cliente del Juego (simplificado)
class GameClient:
    def __init__(self):
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.player_id = None
        self.game_state = None
        
    def connect(self):
        self.socket.connect(('localhost', 5555))
        response = json.loads(self.socket.recv(1024).decode())
        self.player_id = response["player_id"]
        print(f"Conectado como jugador {self.player_id}")
        
  # Hilo para recibir actualizaciones del juego
        threading.Thread(target=self.receive_updates).start()
        
        # Bucle principal del cliente
        self.run()
    
    def receive_updates(self):
        while True:
            try:
                data = self.socket.recv(4096).decode()
                if not data:
                    break
                self.game_state = json.loads(data)
            except:
                break
 def run(self):
        pygame.init()
        screen = pygame.display.set_mode((GameConfig.WIDTH, GameConfig.HEIGHT))
        pygame.display.set_caption(f"Arena Cognitiva - Jugador {self.player_id}")
        clock = pygame.time.Clock()
        
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
              # Manejo de entrada
            keys = pygame.key.get_pressed()
            dx, dy = 0, 0
            if keys[pygame.K_LEFT]: dx = -1
            if keys[pygame.K_RIGHT]: dx = 1
            if keys[pygame.K_UP]: dy = -1
            if keys[pygame.K_DOWN]: dy = 1
            
            if dx != 0 or dy != 0:
                self.socket.send(json.dumps({
                    "action": "move",
                    "dx": dx,
                    "dy": dy
                }).encode())
            
            # Dibujar
            screen.fill((0, 0, 0))

     if self.game_state:
                # Dibujar jugadores
                for player in self.game_state["players"]:
                    pygame.draw.circle(screen, player["color"], 
                                      (player["x"], player["y"]), player["radius"])
                    font = pygame.font.SysFont(None, 24)
                    text = font.render(f"P{player['id']}: {player['score']}", True, (255, 255, 255))
                    screen.blit(text, (player["x"] - 20, player["y"] - 30))
          
 # Dibujar recompensas
                for reward in self.game_state["rewards"]:
                    color = (0, 255, 0) if reward["type"] == "velocidad" else \
                            (255, 0, 0) if reward["type"] == "tamaño" else \
                            (0, 0, 255) if reward["type"] == "puntos_extra" else \
                            (255, 255, 0)
                    pygame.draw.circle(screen, color, (reward["x"], reward["y"]), 10)
            
            pygame.display.flip()
            clock.tick(60)
        
        pygame.quit()
        self.socket.close()

      # Iniciar servidor o cliente
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        GameServer()
    else:
        GameClient().connect()










# ==================== Subsistemas Principales ====================
class GGUFModelManager:
    def __init__(self, model_dir="models"):
        self.model_dir = model_dir
        self.loaded_models = {}
        
    def list_models(self):
        return [f for f in os.listdir(self.model_dir) if f.endswith('.gguf')]
    


class ModelFinder:
    def __init__(self, folder='models', allowed_extensions=None):
        self.folder = folder
        self.allowed_extensions = allowed_extensions or ['.gguf', '.bin', '.mamba', '.pth', '.pt']

    def find_models(self):
        """Busca modelos en la carpeta especificada y devuelve una lista de diccionarios."""
        found_models = []

        if not os.path.exists(self.folder):
            print(f"[ModelFinder] Carpeta no encontrada: {self.folder}")
            return []

        for root, _, files in os.walk(self.folder):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in self.allowed_extensions:
                    full_path = os.path.join(root, file)
                    found_models.append({
                        'name': os.path.splitext(file)[0],
                        'path': os.path.abspath(full_path),
                        'extension': ext
                    })

        print(f"[ModelFinder] Modelos encontrados: {len(found_models)}")
        return found_models

# Blueprint de Flask para registrar el endpoint
model_api = Blueprint('model_api', __name__)

@model_api.route('/api/models', methods=['GET'])
def get_models():
    finder = ModelFinder()
    models = finder.find_models()
    return jsonify(models)

# Carga dinámica del modelo seleccionado
def load_llama_cpp_model(path):
    # Ejemplo: integración con llama-cpp-python
    from llama_cpp import Llama
    print(f"[LLAMA_CPP] Cargando modelo desde {path}")
    return Llama(model_path=path, n_ctx=2048)

def load_mamba_model(path):
    import torch
    print(f"[MAMBA] Cargando modelo PyTorch/Mamba desde {path}")
    model = torch.load(path, map_location=torch.device('cpu'))
    return model

@model_api.record_once
def setup_socketio(state):
    socketio = state.app.extensions['socketio']

    @socketio.on('load_model')
    def handle_load_model(data):
        model_path = data.get('path')
        ext = os.path.splitext(model_path)[1].lower()
        model = None

        try:
            if ext == '.gguf' or ext == '.bin':
                model = load_llama_cpp_model(model_path)
            elif ext == '.mamba' or ext == '.pth' or ext == '.pt':
                model = load_mamba_model(model_path)
            else:
                emit('debug', {'msg': f'Extensión no compatible: {ext}'})
                return

            # Aquí puedes guardar el modelo en cache global, si es necesario
            state.app.config['CURRENT_MODEL'] = model
            emit('debug', {'msg': f'Modelo cargado: {os.path.basename(model_path)}'})

        except Exception as e:
            print(f"[ERROR] Fallo al cargar modelo: {e}")
            emit('debug', {'msg': f'Error al cargar modelo: {str(e)}'})

# Para registrar en tu app Flask principal:
# from model_finder_api import model_api
# app.register_blueprint(model_api)


@socketio.on('load_model')
def load_selected_model(data):
    model_path = data.get('path')
    print(f"Cargando modelo: {model_path}")
    # Aquí iría tu lógica de carga de modelo
    emit('debug', {'msg': f'Modelo cargado: {os.path.basename(model_path)}'})

    def load_model(self, model_name):
        if model_name not in self.loaded_models:
            model_path = os.path.join(self.model_dir, model_name)
            self.loaded_models[model_name] = Llama(
                model_path=model_path,
                n_ctx=2048,
                n_threads=4
            )
        return self.loaded_models[model_name]

class AGINetwork:
    def __init__(self):
        self.zeroconf = zeroconf.Zeroconf()
        self.peers = {}
        self.external_services = {
            'huggingface': 'https://api-inference.huggingface.co',
            'openai': 'https://api.openai.com',
            'local_directory': './agi_instances/'
        }
        
    def register_service(self, port):
        service_info = zeroconf.ServiceInfo(
            "_agi._tcp.local.",
            f"AGIService_{socket.gethostname()}._agi._tcp.local.",
            addresses=[socket.inet_aton(self.get_local_ip())],
            port=port,
            properties={
                'version': '3.0',
                'capabilities': 'vision,dqn,llm',
                'hostname': socket.gethostname()
            }
        )
        self.zeroconf.register_service(service_info)
    
    def get_local_ip(self):
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            s.connect(('**************', 1))
            IP = s.getsockname()[0]
        except Exception:
            IP = '127.0.0.1'
        finally:
            s.close()
        return IP
    
    def discover_peers(self):
        browser = zeroconf.ServiceBrowser(
            self.zeroconf, 
            "_agi._tcp.local.",
            handlers=[self._on_service_state_change]
        )
        self.scan_directory_instances()
        self.connect_external_services()
    
    def scan_directory_instances(self):
        agi_dir = self.external_services['local_directory']
        if os.path.exists(agi_dir):
            for instance in os.listdir(agi_dir):
                if instance.endswith('.agi'):
                    self.peers[f'dir_{instance}'] = {
                        'type': 'directory',
                        'path': os.path.join(agi_dir, instance)
                    }
    
    def connect_external_services(self):
        for service, url in self.external_services.items():
            if service != 'local_directory':
                try:
                    response = requests.get(url, timeout=2)
                    if response.status_code < 400:
                        logging.info(f"Conexión exitosa con {service}")
                except requests.RequestException:
                    logging.warning(f"No se pudo conectar a {service}")
    
    def _on_service_state_change(self, zeroconf, service_type, name, state_change):
        if state_change is zeroconf.ServiceStateChange.Added:
            info = self.zeroconf.get_service_info(service_type, name)
            if info.properties.get(b'type') == b'AGI':
                self.peers[name.decode()] = {
                    'address': socket.inet_ntoa(info.addresses[0]),
                    'port': info.port,
                    'capabilities': info.properties.get(b'capabilities', b'').decode()
                }

class AGICameraManager:
    def __init__(self):
        self.dispositivos = []
        self.detector = pipeline("object-detection")
        self.camera_thread = None
        self.running = False
        self.current_frame = None

    def start_camera_stream(self, idx=0):
        self.running = True
        cap = cv2.VideoCapture(idx)
        
        while self.running:
            ret, frame = cap.read()
            if not ret:
                break
            
            self.current_frame = frame.copy()
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            detections = self.detector(rgb_frame)
            
            height, width = frame.shape[:2]
            normalized_detections = []
            
            for obj in detections:
                box = obj['box']
                normalized = {
                    'label': obj['label'],
                    'score': obj['score'],
                    'x': box['xmin'] / width,
                    'y': box['ymin'] / height,
                    'width': (box['xmax'] - box['xmin']) / width,
                    'height': (box['ymax'] - box['ymin']) / height
                }
                normalized_detections.append(normalized)
            
            socketio.emit('object_detection', {'objects': normalized_detections})
            
        cap.release()

    def buscar_camaras(self, max_id=5):
        for i in range(max_id):
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                self.dispositivos.append(i)
                cap.release()
        return self.dispositivos

    def iniciar_streaming(self, idx=0):
        if self.camera_thread is None or not self.camera_thread.is_alive():
            self.camera_thread = Thread(target=self.start_camera_stream, args=(idx,))
            self.camera_thread.start()

    def detener_streaming(self):
        self.running = False

class AudioStreamer:
    def __init__(self, sample_rate=44100, chunk_size=1024, channels=1):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        self.audio = pyaudio.PyAudio()
        self.stream_out = None
        self.stream_in = None
        self.is_streaming = Event()
        self.volume = 1.0
        self.echo_cancellation = True
        self.noise_suppression = True
        
    def start_stream(self, output_device_index=None, input_device_index=None):
        if self.is_streaming.is_set():
            return False
            
        try:
            self.stream_out = self.audio.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                output=True,
                output_device_index=output_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            self.stream_in = self.audio.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=input_device_index,
                frames_per_buffer=self.chunk_size
            )
            
            self.is_streaming.set()
            Thread(target=self._stream_loop, daemon=True).start()
            return True
        except Exception as e:
            logging.error(f"Error al iniciar stream de audio: {str(e)}")
            return False
    
    def stop_stream(self):
        self.is_streaming.clear()
        if self.stream_out:
            self.stream_out.stop_stream()
            self.stream_out.close()
        if self.stream_in:
            self.stream_in.stop_stream()
            self.stream_in.close()
    
    def _stream_loop(self):
        while self.is_streaming.is_set():
            try:
                data = self.stream_in.read(self.chunk_size, exception_on_overflow=False)
                processed_data = self._process_audio(data)
                self.stream_out.write(processed_data)
                
                if socketio:
                    socketio.emit('audio_stream', {
                        'data': base64.b64encode(processed_data).decode('utf-8'),
                        'timestamp': time.time()
                    })
                    
            except Exception as e:
                logging.error(f"Error en stream de audio: {str(e)}")
                time.sleep(0.1)
    
    def _process_audio(self, data):
        audio_array = np.frombuffer(data, dtype=np.int16)
        
        if self.volume != 1.0:
            audio_array = (audio_array * self.volume).astype(np.int16)
        
        if self.echo_cancellation:
            audio_array = audioop.lin2lin(audio_array, 2, 2)
            audio_array = audioop.tomono(audio_array, 2, 0.9, 0.1)
        
        if self.noise_suppression:
            max_val = np.max(np.abs(audio_array))
            if max_val > 0:
                threshold = max_val * 0.2
                audio_array[np.abs(audio_array) < threshold] = 0
        
        return audio_array.tobytes()
    
    def list_devices(self):
        devices = []
        for i in range(self.audio.get_device_count()):
            dev_info = self.audio.get_device_info_by_index(i)
            devices.append({
                'index': i,
                'name': dev_info['name'],
                'max_input_channels': dev_info['maxInputChannels'],
                'max_output_channels': dev_info['maxOutputChannels'],
                'default_sample_rate': dev_info['defaultSampleRate']
            })
        return devices
    
    def set_volume(self, volume):
        self.volume = max(0.0, min(1.0, float(volume)))


    

class ModelFinder:
    def __init__(self, folder='models', allowed_extensions=None):
        self.folder = folder
        self.allowed_extensions = allowed_extensions or ['.gguf', '.bin', '.mamba', '.pth', '.pt']
    
    def find_models(self):
        """Busca modelos en la carpeta especificada y devuelve una lista de diccionarios."""
        found_models = []
        
        if not os.path.exists(self.folder):
            print(f"[ModelFinder] Carpeta no encontrada: {self.folder}")
            return []
        
        for root, _, files in os.walk(self.folder):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in self.allowed_extensions:
                    full_path = os.path.join(root, file)
                    found_models.append({
                        'name': os.path.splitext(file)[0],
                        'path': os.path.abspath(full_path),
                        'extension': ext
                    })
        
        print(f"[ModelFinder] Modelos encontrados: {len(found_models)}")
        return found_models

    def to_json(self):
        """Devuelve la lista de modelos en formato JSON."""
        import json
        return json.dumps(self.find_models(), indent=2)



# ==================== Sistemas del Juego ====================
class MatchmakingSystem:
    def __init__(self):
        self.queue = []
        self.lock = threading.Lock()
        self.strategy_weights = {
            'balanced': 1.0,
            'aggressive': 1.2,
            'defensive': 0.8
        }
        
    def add_to_queue(self, player_id, strategy='balanced'):
        with self.lock:
            player = Player.query.get(player_id)
            self.queue.append({
                'player_id': player_id,
                'rating': player.rating,
                'strategy': strategy,
                'timestamp': time.time()
            })
            self.check_matches()
    
    def calculate_match_score(self, p1, p2):
        time_factor = 1 - min(1, (time.time() - p1['timestamp']) / 300)
        rating_diff = abs(p1['rating'] - p2['rating'])
        strategy_compat = self.strategy_weights[p1['strategy']] * self.strategy_weights[p2['strategy']]
        return (1000 / (rating_diff + 100)) * time_factor * strategy_compat
    
    def check_matches(self):
        with self.lock:
            while len(self.queue) >= 2:
                best_match = None
                max_score = 0
                
                for i in range(len(self.queue)):
                    for j in range(i+1, len(self.queue)):
                        score = self.calculate_match_score(self.queue[i], self.queue[j])
                        if score > max_score:
                            max_score = score
                            best_match = (i, j)
                
                if best_match and max_score > 50:
                    p1 = self.queue.pop(best_match[1])
                    p2 = self.queue.pop(best_match[0])
                    self.create_game_session(p1, p2)
    
    def create_game_session(self, p1, p2):
        session_id = str(uuid.uuid4())
        new_session = GameSession(
            id=session_id,
            players=json.dumps([p1['player_id'], p2['player_id']]),
            strategy_used=f"{p1['strategy']}-vs-{p2['strategy']}"
        )
        db.session.add(new_session)
        db.session.commit()
        
        socketio.emit('match_found', {
            'session_id': session_id,
            'players': [p1['player_id'], p2['player_id']],
            'strategy_matchup': new_session.strategy_used
        }, room=[p1['player_id'], p2['player_id']])

class VisualEffectEngine:
    def __init__(self):
        self.effect_templates = {
            'glow': {'css_class': 'glow-effect', 'animation': 'glow 2s infinite'},
            'aura': {'css_class': 'aura-effect', 'animation': 'aura-pulse 3s infinite'},
            'particles': {'css_class': 'particle-system', 'animation': 'emit-particles 5s linear infinite'}
        }
    
    def apply_effect(self, player_id, effect_name):
        player = Player.query.get(player_id)
        avatar_data = json.loads(player.avatar_data or '{}')
        
        if effect_name in self.effect_templates:
            effect = self.effect_templates[effect_name]
            avatar_data['effects'].append(effect)
            player.avatar_data = json.dumps(avatar_data)
            db.session.commit()
            
            socketio.emit('avatar_update', {
                'player_id': player_id,
                'effect': effect
            }, room=player_id)

class AchievementSystem:
    def __init__(self):
        self.achievement_triggers = {
            'ai_level_up': self.check_level_up,
            'score_milestone': self.check_score_milestone,
            'reward_collector': self.check_rewards_collected
        }
    
    def check_achievements(self, player_id, trigger_type, **kwargs):
        player = Player.query.get(player_id)
        unlocked = json.loads(player.achievements or '[]')
        
        if trigger_type in self.achievement_triggers:
            new_achievements = self.achievement_triggers[trigger_type](player, **kwargs)
            for achievement in new_achievements:
                if achievement not in unlocked:
                    unlocked.append(achievement)
                    self.notify_achievement(player_id, achievement)
            
            player.achievements = json.dumps(unlocked)
            db.session.commit()
    
    def check_level_up(self, player, **kwargs):
        achievements = []
        if player.ai_level >= 5:
            achievements.append('Novice AI Trainer')
        if player.ai_level >= 10:
            achievements.append('Master of Algorithms')
        return achievements
    
    def notify_achievement(self, player_id, achievement_name):
        socketio.emit('achievement_unlocked', {
            'player_id': player_id,
            'achievement': achievement_name,
            'timestamp': datetime.utcnow().isoformat()
        }, room=player_id)

# ==================== Agente Neuro-Simbólico ====================
class NeuroSymbolicGameAgent:
    def __init__(self, player_id, initial_state_size=128, action_space_size=20):
        self.player_id = player_id
        self.symbolic_knowledge = SymbolicKnowledgeBase()
        self.neural_network = CognitiveNeuralNetwork()
        self.mamba_block = MambaLanguageBlock()
        self.working_memory = WorkingMemory()
        self.long_term_memory = LongTermMemory()
        self.decision_engine = DecisionEngine()
        self.communication_system = AdvancedCommunicator(
            agent_id=f"game_agent_{player_id}",
            local_directory="./agi_instances/"
        )
        self._setup_communication_handlers()
        Thread(target=self._handle_incoming_messages, daemon=True).start()
    
    def process_input(self, game_state):
        visual_data = self.process_visual(game_state['visual'])
        textual_data = self.process_text(game_state['text'])
        
        symbolic_rep = self.extract_symbolic_features(visual_data, textual_data)
        neural_rep = self.neural_network(visual_data, textual_data)
        
        fused_rep = self.fuse_representations(symbolic_rep, neural_rep)
        temporal_context = self.mamba_block(fused_rep, self.working_memory.get_context())
        action = self.decision_engine(temporal_context)
        
        self.update_memory(fused_rep, action, game_state['reward'])
        return action
    
    def _setup_communication_handlers(self):
        self.communication_handlers = {
            "resource_request": self._handle_resource_request,
            "strategy_coordination": self._handle_strategy_coordination,
            "threat_alert": self._handle_threat_alert
        }
    
    def communicate(self, message: str, context: dict) -> str:
        target = self._select_communication_target(message, context)
        structured_msg = {
            "type": context.get("message_type", "query"),
            "content": message,
            "context": context,
            "priority": context.get("priority", 3)
        }
        
        if context.get("require_response", False):
            response = self.communication_system.send_message(
                target=target,
                message=structured_msg,
                require_response=True
            )
            return response.get("content") if response else None
        else:
            self.communication_system.send_message(target, structured_msg)
            return None
    
    def _select_communication_target(self, message: str, context: dict) -> str:
        if context.get("message_type") == "strategy_coordination":
            capable_agents = [
                agent_id for agent_id, info in self.communication_system.discovered_agents.items()
                if "decision" in info["capabilities"]
            ]
            if capable_agents:
                return random.choice(capable_agents)
        
        if len(message.split()) > 10 and "?" in message:
            return "openai"
        
        return "local"
    
    def _handle_incoming_messages(self):
        while True:
            if not self.communication_system.message_queue.empty():
                message = self.communication_system.message_queue.get()
                self._process_message(message)
    
    def _process_message(self, message: dict):
        handler = self.communication_handlers.get(message.get("type"))
        if handler:
            response = handler(message)
            if response and "sender" in message:
                self.communication_system.send_message(
                    target=message["sender"],
                    message=response
                )
    
    def _handle_resource_request(self, message: dict) -> Optional[dict]:
        resource = message.get("content", {}).get("resource")
        if resource in self.available_resources:
            return {
                "type": "resource_response",
                "content": {
                    "resource": resource,
                    "amount": self.available_resources[resource] * 0.5,
                    "location": self.current_position
                },
                "in_response_to": message["message_id"]
            }
        return None

# ==================== Componentes de Soporte ====================
class AdvancedCommunicator:
    def __init__(self, agent_id, local_directory):
        self.agent_id = agent_id
        self.local_directory = local_directory
        self.discovered_agents = {}
        self.message_queue = queue.Queue()
        self.zeroconf = zeroconf.Zeroconf()
        self.register_service(5000)
        self.discover_peers()
    
    def send_message(self, target, message, require_response=False):
        if target.startswith('dir_'):
            return self._send_directory_message(target, message)
        elif target in ['openai', 'huggingface']:
            return self._send_external_message(target, message)
        else:
            return self._send_peer_message(target, message, require_response)
    
    def broadcast(self, message, min_capabilities=None):
        for peer_id, peer_info in self.discovered_agents.items():
            if min_capabilities:
                if all(cap in peer_info.get('capabilities', []) for cap in min_capabilities):
                    self.send_message(peer_id, message)
            else:
                self.send_message(peer_id, message)

class RepairAgent(FileSystemEventHandler):
    def __init__(self):
        self.observer = Observer()
        self.last_trigger = 0
    
    def on_modified(self, event):
        if not event.is_directory and self.is_code_file(event.src_path):
            if time.time() - self.last_trigger > 1:
                self.repair_file(event.src_path)
                self.last_trigger = time.time()
    
    def repair_file(self, file_path):
        try:
            self.lint_and_fix(file_path)
            self.fix_spelling(file_path)
            socketio.emit('system_update', {'message': f'Archivo reparado: {file_path}'})
        except Exception as e:
            logging.error(f"Error en reparación: {str(e)}")
    
    @staticmethod
    def is_code_file(path):
        valid_ext = ['.py', '.js', '.html', '.css', '.txt']
        return os.path.splitext(path)[1].lower() in valid_ext

class AutoExpansorAGI:
    def __init__(self):
        self.sources = {
            "github": "https://api.github.com/search/code?q=AGI+auto+expand",
            "huggingface": "https://huggingface.co/api/models?search=agi"
        }
    
    def ejecutar_autoexpansion(self):
        try:
            mejoras = requests.get(self.sources["github"]).json()
            if mejoras:
                socketio.emit('system_update', {'message': 'Nuevas mejoras disponibles'})
        except Exception as e:
            logging.error(f"Error en autoexpansión: {str(e)}")

import os
import json
from flask import jsonify, Blueprint
from flask_socketio import emit

class ModelFinder:
    def __init__(self, folder='models', allowed_extensions=None):
        self.folder = folder
        self.allowed_extensions = allowed_extensions or ['.gguf', '.bin', '.mamba', '.pth', '.pt']

    def find_models(self):
        """Busca modelos en la carpeta especificada y devuelve una lista de diccionarios."""
        found_models = []

        if not os.path.exists(self.folder):
            print(f"[ModelFinder] Carpeta no encontrada: {self.folder}")
            return []

        for root, _, files in os.walk(self.folder):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in self.allowed_extensions:
                    full_path = os.path.join(root, file)
                    found_models.append({
                        'name': os.path.splitext(file)[0],
                        'path': os.path.abspath(full_path),
                        'extension': ext
                    })

        print(f"[ModelFinder] Modelos encontrados: {len(found_models)}")
        return found_models

# Blueprint de Flask para registrar el endpoint
model_api = Blueprint('model_api', __name__)

@model_api.route('/api/models', methods=['GET'])
def get_models():
    finder = ModelFinder()
    models = finder.find_models()
    return jsonify(models)

# Carga dinámica del modelo seleccionado
def load_llama_cpp_model(path):
    # Ejemplo: integración con llama-cpp-python
    from llama_cpp import Llama
    print(f"[LLAMA_CPP] Cargando modelo desde {path}")
    return Llama(model_path=path, n_ctx=2048)

def load_mamba_model(path):
    import torch
    print(f"[MAMBA] Cargando modelo PyTorch/Mamba desde {path}")
    model = torch.load(path, map_location=torch.device('cpu'))
    return model

@model_api.record_once
def setup_socketio(state):
    socketio = state.app.extensions['socketio']

    @socketio.on('load_model')
    def handle_load_model(data):
        model_path = data.get('path')
        ext = os.path.splitext(model_path)[1].lower()
        model = None

        try:
            if ext == '.gguf' or ext == '.bin':
                model = load_llama_cpp_model(model_path)
            elif ext == '.mamba' or ext == '.pth' or ext == '.pt':
                model = load_mamba_model(model_path)
            else:
                emit('debug', {'msg': f'Extensión no compatible: {ext}'})
                return

            # Aquí puedes guardar el modelo en cache global, si es necesario
            state.app.config['CURRENT_MODEL'] = model
            emit('debug', {'msg': f'Modelo cargado: {os.path.basename(model_path)}'})

        except Exception as e:
            print(f"[ERROR] Fallo al cargar modelo: {e}")
            emit('debug', {'msg': f'Error al cargar modelo: {str(e)}'})

# Para registrar en tu app Flask principal:
# from model_finder_api import model_api
# app.register_blueprint(model_api)


# ==================== Rutas API ====================
@app.route('/')
def index():
    return render_template_string(open('index.html').read())

@app.route('/api/agi', methods=['POST'])


@app.route('/api/players', methods=['POST'])
def register_player():
    data = request.json
    new_player = Player(
        id=str(uuid.uuid4()),
        username=data['username'],
        avatar_data=json.dumps({'items': [], 'effects': []})
    )
    db.session.add(new_player)
    db.session.commit()
    return jsonify({'player_id': new_player.id}), 201

@app.route('/api/players/<player_id>', methods=['GET'])
def get_player(player_id):
    player = Player.query.get(player_id)
    if not player:
        return jsonify({'error': 'Player not found'}), 404
    
    return jsonify({
        'id': player.id,
        'username': player.username,
        'rating': player.rating,
        'ai_level': player.ai_level,
        'avatar': json.loads(player.avatar_data),
        'achievements': json.loads(player.achievements or '[]')
    })

@app.route('/models', methods=['GET'])
def list_models():
    return jsonify(agi.model_manager.list_models())

@app.route('/upload', methods=['POST'])
def upload_file():
    file = request.files['file']
    filename = secure_filename(file.filename)
    file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
    return jsonify({"url": f"/uploads/{filename}", "name": filename})

# ==================== Núcleo AGI ====================
class AGICore:
    def __init__(self):
        self.model_manager = GGUFModelManager()
        self.dqn_agent = DQNAgent(state_size=10, action_size=3)
        self.network = AGINetwork()
        self.camera_manager = AGICameraManager()
        self.repair_agent = RepairAgent()
        self.expander = AutoExpansorAGI()
        self.matchmaking = MatchmakingSystem()
        self.visual_effects = VisualEffectEngine()
        self.achievement_system = AchievementSystem()
        self.audio_streamer = AudioStreamer()
        self.current_model = self.model_manager.load_model("Crazy_Reasoning_1,5B.Q4_K_M.gguf")
        self.network.register_service(5000)
        self.network.discover_peers()
        self.init_agi_listeners()
        self.cycle_count = 0
        
        if self.camera_manager.buscar_camaras():
            self.camera_manager.iniciar_streaming(0)
    

    # ...
        # self.current_model = self.model_manager.load_model("Crazy_Reasoning_1,5B.Q4_K_M.gguf")
        self.current_model = None # Will be loaded via UI or a default
        self.current_model_path = None
        self.current_model_type = None
        self._load_default_model() # New method to load a default
        
        self.network.register_service(5000) # Ensure port is consistent
        # ...
        # IMPORTANT: Register SocketIO events for model loading
        register_socketio_events(socketio, app) # socketio and app must be accessible

    def _load_default_model(self):
        # Try to load a default model if available
        finder = ModelFinder() # ModelFinder is now in model_finder_api
        models = finder.find_models()
        if models:
            # Prioritize GGUF, then Mamba, etc.
            default_model_to_load = None
            for m_ext_priority in ['.gguf', '.bin', '.mamba', '.pth', '.pt']:
                for m in models:
                    if m['extension'] == m_ext_priority:
                        default_model_to_load = m
                        break
                if default_model_to_load:
                    break
            
            if default_model_to_load:
                print(f"[AGICore] Attempting to load default model: {default_model_to_load['path']}")
            try:
                    if default_model_to_load['extension'] in ['.gguf', '.bin']:
                        from model_finder_api import load_llama_cpp_model # Import helper
                        self.current_model = load_llama_cpp_model(default_model_to_load['path'])
                        self.current_model_type = 'llama_cpp'
                    elif default_model_to_load['extension'] in ['.mamba', '.pth', '.pt']:
                        from model_finder_api import load_mamba_model # Import helper
                        self.current_model = load_mamba_model(default_model_to_load['path'])
                        self.current_model_type = 'mamba' # Or torch generic
                    
                    if self.current_model:
                        self.current_model_path = default_model_to_load['path']
                        app.config['CURRENT_MODEL'] = self.current_model
                        app.config['CURRENT_MODEL_PATH'] = self.current_model_path
                        app.config['CURRENT_MODEL_TYPE'] = self.current_model_type
                        print(f"[AGICore] Default model loaded: {os.path.basename(self.current_model_path)}")
                        socketio.emit('debug', {'msg': f'Default model loaded: {os.path.basename(self.current_model_path)}'})

                except Exception as e:
                    print(f"[AGICore] Error loading default model {default_model_to_load['path']}: {e}")
                    socketio.emit('debug', {'msg': f'Error loading default model: {str(e)}'})
            else:
                print("[AGICore] No suitable default models found in 'models' directory.")
                socketio.emit('debug', {'msg': "No default models found. Please upload or select one."})

        else:
            print("[AGICore] 'models' directory is empty or not found.")
            socketio.emit('debug', {'msg': "'models' directory is empty. Please add models."})


    def process_query(self, text):
        self.cycle_count += 1
        if not self.current_model:
            return "No model is currently loaded. Please select a model from the list."

        try:
            if self.current_model_type == 'llama_cpp':
                response = self.current_model.create_chat_completion(
                    messages=[{"role": "user", "content": text}]
                )
                return response['choices'][0]['message']['content']
            elif self.current_model_type == 'mamba' or hasattr(self.current_model, 'generate'): # Basic check for Hugging Face model
                # This part needs a proper Mamba/Transformer pipeline
                # For simplicity, let's assume a generic text generation if it's not llama_cpp
                # You'd typically use a tokenizer and the model.generate() method
                # This is a placeholder for actual Mamba inference logic
                # For HuggingFace models:
                # tokenizer = AutoTokenizer.from_pretrained(self.current_model_path or "gpt2") # Or path to model
                # inputs = tokenizer(text, return_tensors="pt")
                # outputs = self.current_model.generate(**inputs)
                # return tokenizer.decode(outputs[0], skip_special_tokens=True)
                print(f"[AGICore] Mamba/Torch model query processing needs specific implementation for {self.current_model_path}")
                return f"Mamba/Torch model loaded, but inference for '{text}' is not fully implemented in this generic handler."
            else:
                return "Loaded model type is not recognized for processing."
        # ... (rest of process_query) ...


# Comment out or adapt these global inits if they conflict or are not needed for the chat UI
# dqn_game_agent = SuperautoDQNAgent() # Using the stub
# dqn_game_agent.load()
# game = GameConnector(dqn_game_agent) # Using the stub

# state = [0.5, 0.1, 0.0, 1.0]
# reward = 0.8
# done = False
# action = game.step(state, reward, done)
# print(f"Acción del AGI (Superautonomatics stub): {action}")

# agi_api_instance = AGIAPI() # Using the stub
# action_api = agi_api_instance.process_game_step(state, reward, done)
# print(f"Acción elegida (AGIAPI stub): {action_api}")

# ...
# At the end of the file, where AGICore is instantiated and app runs:

    def init_agi_listeners(self):
        @socketio.on('agi_query')
        def handle_agi_query(data):
            response = self.process_query(data['text'])
            socketio.emit('agi_response', {'text': response})
            socketio.emit('agi_response', {'content': respuesta}, room=usuario_id)

        @socketio.on('vision_request')
        def handle_vision_request():
            if self.camera_manager.current_frame is not None:
                ret, buffer = cv2.imencode('.jpg', self.camera_manager.current_frame)
                frame_bytes = base64.b64encode(buffer).decode('utf-8')
                socketio.emit('video_feed', {'frame': f"data:image/jpeg;base64,{frame_bytes}"})
            self.analyze_environment()
        
        @socketio.on('start_audio_stream')
        def handle_start_audio_stream(data):
            output_device = data.get('output_device')
            input_device = data.get('input_device')
            success = self.audio_streamer.start_stream(output_device, input_device)
            emit('audio_stream_status', {'status': 'started' if success else 'error'})
            
        @socketio.on('stop_audio_stream')
        def handle_stop_audio_stream():
            self.audio_streamer.stop_stream()
            emit('audio_stream_status', {'status': 'stopped'})
            
        @socketio.on('set_audio_volume')
        def handle_set_audio_volume(data):
            volume = float(data.get('volume', 1.0))
            self.audio_streamer.set_volume(volume)
            
        @socketio.on('list_audio_devices')
        def handle_list_audio_devices():
            emit('audio_devices_list', {'devices': self.audio_streamer.list_devices()})

        @socketio.on('text_message')
        def handle_text_message(data):
            emit('text_message', {'text': f"Recibido: {data['text']}"})

        @socketio.on('audio_upload')
        def handle_audio_upload(data):
            try:
                file_path = os.path.join(app.config['AUDIO_FOLDER'], f'audio_{int(time.time())}.wav')
                with open(file_path, 'wb') as f:
                    f.write(base64.b64decode(data['data']))
                socketio.emit('audio_upload_success', {'path': file_path})
            except Exception as e:
                logging.error(f"Error procesando audio: {str(e)}")

        @socketio.on('file_upload')
        def handle_file_upload(data):
            try:
                filename = secure_filename(data['name'])
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                with open(file_path, 'wb') as f:
                    f.write(base64.b64decode(data['data']))
                socketio.emit('upload_success', {'filename': filename})
            except Exception as e:
                socketio.emit('upload_error', {'filename': data.get('name', 'unknown')})
                logging.error(f"Error subiendo archivo: {str(e)}")

        @socketio.on('connect')
        def handle_connect():
            player_id = request.args.get('player_id')
            if player_id:
                socketio.emit('connection_status', {'status': 'connected', 'player_id': player_id})

        @socketio.on('start_training')
        def handle_start_training():
            emit('training_update', {'score': 0, 'level': 1})

        @socketio.on('join_matchmaking')
        def handle_join_matchmaking(data):
            player_id = data['player_id']
            strategy = data.get('strategy', 'balanced')
            self.matchmaking.add_to_queue(player_id, strategy)
            socketio.emit('matchmaking_status', {'status': 'in_queue'}, room=player_id)

        @socketio.on('purchase_item')
        def handle_purchase_item(data):
            player_id = data['player_id']
            item_type = data['item_type']
            item_id = data['item_id']
            
            player = Player.query.get(player_id)
            avatar_data = json.loads(player.avatar_data)
            
            if item_id in [item['id'] for item in avatar_data['items']]:
                socketio.emit('error', {'message': 'Item already owned'}, room=player_id)
                return
            
            avatar_data['items'].append({'id': item_id, 'type': item_type})
            player.avatar_data = json.dumps(avatar_data)
            db.session.commit()
            
            socketio.emit('inventory_update', avatar_data, room=player_id)
            self.achievement_system.check_achievements(player_id, 'item_purchased', item_type=item_type)

    def process_query(self, text):
        self.cycle_count += 1
        try:
            response = self.current_model.create_chat_completion(
                messages=[{"role": "user", "content": text}]
            )
            return response['choices'][0]['message']['content']
        except Exception as e:
            logging.warning(f"Error con modelo local: {str(e)}")
            return self.fallback_external_query(text)
    
    def fallback_external_query(self, text):
        for peer_id in self.network.peers:
            response = self.network.send_to_peer(peer_id, {
                'type': 'query',
                'text': text,
                'source': socket.gethostname()
            })
            if response:
                return response.get('answer', 'No pude obtener una respuesta')
        
        try:
            hf_response = requests.post(
                "https://api-inference.huggingface.co/models/state-spaces/mamba-2.8b",
                headers={"Authorization": "Bearer YOUR_HF_TOKEN"},
                json={"inputs": text}
            )
            return hf_response.json()[0]['generated_text']
        except Exception as e:
            return f"Error al contactar servicios externos: {str(e)}"

    def analyze_environment(self):
        devices = self.camera_manager.buscar_camaras()
        if devices:
            self.camera_manager.detectar_objetos_en_camara(devices[0])
            socketio.emit('vision_update', {'devices': devices})


        # ======================= HANDLERS DE SOCKET.IO ========================
from flask_socketio import emit

@socketio.on('connect')
def handle_connect():
    print('Cliente conectado') en el
    emit('debug', {'msg': 'Conexión establecida con el servidor Flask'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Cliente desconectado')

@socketio.on('file_upload')
def handle_file_upload(data):
    print("Archivo recibido:", data['name'])
    emit('file_upload_status', {'status': 'recibido', 'name': data['name']})

@socketio.on('audio_upload')
def handle_audio_upload(data):
    print("Audio recibido, formato:", data.get('format', 'desconocido'))
    emit('audio_upload_status', {'status': 'recibido'})

@socketio.on('start_audio_stream')
def handle_start_audio_stream(data):
    input_device = data.get('input_device')
    output_device = data.get('output_device')
    if audio_streamer.start_stream(input_device_index=input_device, output_device_index=output_device):
        emit('audio_stream_status', {'status': 'started'})
    else:
        emit('audio_stream_status', {'status': 'error'})

@socketio.on('stop_audio_stream')
def handle_stop_audio_stream():
    audio_streamer.stop_stream()
    emit('audio_stream_status', {'status': 'stopped'})

@socketio.on('set_audio_volume')
def handle_set_audio_volume(data):
    volume = data.get('volume', 1.0)
    audio_streamer.set_volume(volume)
    emit('debug', {'msg': f'Volumen ajustado a {volume}'})


@socketio.on('*')
def catch_all(event, data):
    print(f"Evento recibido: {event} -> {data}")

@socketio.on('query_agi')
def handle_query_agi(data):
    mensaje = data.get('text')
    respuesta = "Respuesta simulada del backend para: " + mensaje
    emit('agi_response', {'content': respuesta})

@socketio.on('start_vision')
def handle_start_vision():
    agi_camera_manager.iniciar_streaming()
    emit('debug', {'msg': 'Visión artificial activada'})

@socketio.on('stop_vision')
def handle_stop_vision():
    agi_camera_manager.detener_streaming()
    emit('debug', {'msg': 'Visión artificial detenida'})

@socketio.on('game_event')
def handle_game_event(data):
    player_id = data.get('player_id')
    event_type = data.get('event')
    metadata = data.get('metadata', {})
    if player_id not in agi_agents:
        agi_agents[player_id] = NeuroSymbolicGameAgent(player_id)
    agi_agents[player_id].check_event(event_type, metadata)
    emit('debug', {'msg': f'Evento procesado para {player_id}: {event_type}'})

// Conexión de botones del frontend con backend AGI vía Socket.IO con notificaciones, sonidos y vibración

// Prepara sonidos
const notificationSounds = {
  success: new Audio('https://actions.google.com/sounds/v1/cartoon/clang_and_wobble.ogg'),
  info: new Audio('https://actions.google.com/sounds/v1/alarms/beep_short.ogg'),
  error: new Audio('https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg')
};

function vibrate(pattern) {
  if (navigator.vibrate) navigator.vibrate(pattern);
}

// Crear contenedor de notificaciones si no existe
document.addEventListener('DOMContentLoaded', () => {
  if (!document.getElementById('notification-container')) {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
  }
});

function showNotification(msg, type = 'info') {
  const container = document.getElementById('notification-container');
  const note = document.createElement('div');
  note.textContent = msg;
  note.style.background = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff';
  note.style.color = 'white';
  note.style.padding = '10px 15px';
  note.style.borderRadius = '8px';
  note.style.marginBottom = '10px';
  note.style.boxShadow = '0 2px 6px rgba(0,0,0,0.2)';
  note.style.transition = 'opacity 0.5s ease';
  note.style.opacity = '1';

  container.appendChild(note);
  setTimeout(() => {
    note.style.opacity = '0';
    setTimeout(() => container.removeChild(note), 500);
  }, 3000);

  // Reproducir sonido y vibrar
  if (notificationSounds[type]) notificationSounds[type].play();
  if (type === 'success') vibrate([100, 50, 100]);
  else if (type === 'info') vibrate([50]);
  else if (type === 'error') vibrate([200, 100, 200]);
}

// Botón de visión artificial
const startVisionBtn = document.getElementById('start-vision');
let visionActive = false;

startVisionBtn.addEventListener('click', () => {
  if (!visionActive) {
    socket.emit('start_vision');
    startVisionBtn.textContent = 'Detener Visión';
    showNotification('Visión artificial iniciada', 'success');
  } else {
    socket.emit('stop_vision');
    startVisionBtn.textContent = 'Iniciar Visión';
    showNotification('Visión artificial detenida', 'info');
  }
  visionActive = !visionActive;
});

// Envío de eventos del juego al agente AGI
function sendGameEvent(playerId, eventType, metadata = {}) {
  socket.emit('game_event', {
    player_id: playerId,
    event: eventType,
    metadata: metadata
  });
  addMessage(`[Evento enviado] ${eventType} de ${playerId}`, false);
  showNotification(`Evento: ${eventType} enviado`, 'info');
}

// Botón de votación de agente
const agentSelect = document.getElementById('agent-select');
const voteAgentBtn = document.getElementById('vote-agent');

voteAgentBtn.addEventListener('click', () => {
  const selected = agentSelect.value;
  sendGameEvent('jugador_demo', 'agent_voted', { selectedAgent: selected });
  showNotification(`Voto por agente ${selected}`, 'success');
});

// Manejo de selección de modo de juego
const modeSelectors = [
  document.getElementById('game-mode'),
  document.getElementById('game-mode1'),
  document.getElementById('game-mode2'),
  document.getElementById('game-mode3'),
  document.getElementById('game-mode4')
];

modeSelectors.forEach((selector, index) => {
  selector.addEventListener('change', () => {
    const selectedMode = selector.value;
    sendGameEvent(`jugador_demo_${index + 1}`, 'mode_selected', { mode: selectedMode });
    showNotification(`Modo de juego: ${selectedMode}`, 'info');
  });
});

// Envío de evento cuando se inicia un juego
const startButtons = [
  document.getElementById('start-game'),
  document.getElementById('start-game1'),
  document.getElementById('start-game2'),
  document.getElementById('start-game3'),
  document.getElementById('start-game4')
];

startButtons.forEach((btn, index) => {
  if (btn) {
    btn.addEventListener('click', () => {
      sendGameEvent(`jugador_demo_${index + 1}`, 'game_started');
      showNotification(`Juego iniciado (${index + 1})`, 'success');
    });
  }
});

// Mostrar respuestas del backend en el chat
socket.on('agi_response', (data) => {
  addMessage(`AGI: ${data.content}`, false);
  showNotification('Respuesta del AGI recibida', 'success');
});

// Mostrar mensajes de depuración si hay
socket.on('debug', (data) => {
  console.log('[DEBUG]', data.msg);
  addMessage(`[Sistema] ${data.msg}`, false);
  showNotification(data.msg, 'info');
});

// Conexión de botones del frontend con backend AGI vía Socket.IO con notificaciones, sonidos y vibración

// Prepara sonidos
const notificationSounds = {
  success: new Audio('https://actions.google.com/sounds/v1/cartoon/clang_and_wobble.ogg'),
  info: new Audio('https://actions.google.com/sounds/v1/alarms/beep_short.ogg'),
  error: new Audio('https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg')
};

function vibrate(pattern) {
  if (navigator.vibrate) navigator.vibrate(pattern);
}

// Crear contenedor de notificaciones si no existe
document.addEventListener('DOMContentLoaded', () => {
  if (!document.getElementById('notification-container')) {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
  }
});

function showNotification(msg, type = 'info') {
  const container = document.getElementById('notification-container');
  const note = document.createElement('div');
  note.textContent = msg;
  note.style.background = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff';
  note.style.color = 'white';
  note.style.padding = '10px 15px';
  note.style.borderRadius = '8px';
  note.style.marginBottom = '10px';
  note.style.boxShadow = '0 2px 6px rgba(0,0,0,0.2)';
  note.style.transition = 'opacity 0.5s ease';
  note.style.opacity = '1';

  container.appendChild(note);
  setTimeout(() => {
    note.style.opacity = '0';
    setTimeout(() => container.removeChild(note), 500);
  }, 3000);

  // Reproducir sonido y vibrar
  if (notificationSounds[type]) notificationSounds[type].play();
  if (type === 'success') vibrate([100, 50, 100]);
  else if (type === 'info') vibrate([50]);
  else if (type === 'error') vibrate([200, 100, 200]);
}

// Botón de visión artificial
const startVisionBtn = document.getElementById('start-vision');
let visionActive = false;

startVisionBtn.addEventListener('click', () => {
  if (!visionActive) {
    socket.emit('start_vision');
    startVisionBtn.textContent = 'Detener Visión';
    showNotification('Visión artificial iniciada', 'success');
  } else {
    socket.emit('stop_vision');
    startVisionBtn.textContent = 'Iniciar Visión';
    showNotification('Visión artificial detenida', 'info');
  }
  visionActive = !visionActive;
});

// Envío de eventos del juego al agente AGI
function sendGameEvent(playerId, eventType, metadata = {}) {
  socket.emit('game_event', {
    player_id: playerId,
    event: eventType,
    metadata: metadata
  });
  addMessage(`[Evento enviado] ${eventType} de ${playerId}`, false);
  showNotification(`Evento: ${eventType} enviado`, 'info');
}

// Botón de votación de agente
const agentSelect = document.getElementById('agent-select');
const voteAgentBtn = document.getElementById('vote-agent');

voteAgentBtn.addEventListener('click', () => {
  const selected = agentSelect.value;
  sendGameEvent('jugador_demo', 'agent_voted', { selectedAgent: selected });
  showNotification(`Voto por agente ${selected}`, 'success');
});

// Manejo de selección de modo de juego
const modeSelectors = [
  document.getElementById('game-mode'),
  document.getElementById('game-mode1'),
  document.getElementById('game-mode2'),
  document.getElementById('game-mode3'),
  document.getElementById('game-mode4')
];

modeSelectors.forEach((selector, index) => {
  selector.addEventListener('change', () => {
    const selectedMode = selector.value;
    sendGameEvent(`jugador_demo_${index + 1}`, 'mode_selected', { mode: selectedMode });
    showNotification(`Modo de juego: ${selectedMode}`, 'info');
  });
});

// Envío de evento cuando se inicia un juego
const startButtons = [
  document.getElementById('start-game'),
  document.getElementById('start-game1'),
  document.getElementById('start-game2'),
  document.getElementById('start-game3'),
  document.getElementById('start-game4')
];

startButtons.forEach((btn, index) => {
  if (btn) {
    btn.addEventListener('click', () => {
      sendGameEvent(`jugador_demo_${index + 1}`, 'game_started');
      showNotification(`Juego iniciado (${index + 1})`, 'success');
    });
  }
});

// Mostrar respuestas del backend en el chat
socket.on('agi_response', (data) => {
  addMessage(`AGI: ${data.content}`, false);
  showNotification('Respuesta del AGI recibida', 'success');
});

// Mostrar mensajes de depuración si hay
socket.on('debug', (data) => {
  console.log('[DEBUG]', data.msg);
  addMessage(`[Sistema] ${data.msg}`, false);
  showNotification(data.msg, 'info');
});

// Conexión de botones del frontend con backend AGI vía Socket.IO con notificaciones, sonidos y vibración

// Prepara sonidos
const notificationSounds = {
  success: new Audio('https://actions.google.com/sounds/v1/cartoon/clang_and_wobble.ogg'),
  info: new Audio('https://actions.google.com/sounds/v1/alarms/beep_short.ogg'),
  error: new Audio('https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg')
};

function vibrate(pattern) {
  if (navigator.vibrate) navigator.vibrate(pattern);
}

// Crear contenedor de notificaciones si no existe
document.addEventListener('DOMContentLoaded', () => {
  if (!document.getElementById('notification-container')) {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
  }
});

function showNotification(msg, type = 'info') {
  const container = document.getElementById('notification-container');
  const note = document.createElement('div');
  note.textContent = msg;
  note.style.background = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff';
  note.style.color = 'white';
  note.style.padding = '10px 15px';
  note.style.borderRadius = '8px';
  note.style.marginBottom = '10px';
  note.style.boxShadow = '0 2px 6px rgba(0,0,0,0.2)';
  note.style.transition = 'opacity 0.5s ease';
  note.style.opacity = '1';

  container.appendChild(note);
  setTimeout(() => {
    note.style.opacity = '0';
    setTimeout(() => container.removeChild(note), 500);
  }, 3000);

  // Reproducir sonido y vibrar
  if (notificationSounds[type]) notificationSounds[type].play();
  if (type === 'success') vibrate([100, 50, 100]);
  else if (type === 'info') vibrate([50]);
  else if (type === 'error') vibrate([200, 100, 200]);
}

// Botón de visión artificial
const startVisionBtn = document.getElementById('start-vision');
let visionActive = false;

startVisionBtn.addEventListener('click', () => {
  if (!visionActive) {
    socket.emit('start_vision');
    startVisionBtn.textContent = 'Detener Visión';
    showNotification('Visión artificial iniciada', 'success');
  } else {
    socket.emit('stop_vision');
    startVisionBtn.textContent = 'Iniciar Visión';
    showNotification('Visión artificial detenida', 'info');
  }
  visionActive = !visionActive;
});

// Envío de eventos del juego al agente AGI
function sendGameEvent(playerId, eventType, metadata = {}) {
  socket.emit('game_event', {
    player_id: playerId,
    event: eventType,
    metadata: metadata
  });
  addMessage(`[Evento enviado] ${eventType} de ${playerId}`, false);
  showNotification(`Evento: ${eventType} enviado`, 'info');
}

// Botón de votación de agente
const agentSelect = document.getElementById('agent-select');
const voteAgentBtn = document.getElementById('vote-agent');

voteAgentBtn.addEventListener('click', () => {
  const selected = agentSelect.value;
  sendGameEvent('jugador_demo', 'agent_voted', { selectedAgent: selected });
  showNotification(`Voto por agente ${selected}`, 'success');
});

// Manejo de selección de modo de juego
const modeSelectors = [
  document.getElementById('game-mode'),
  document.getElementById('game-mode1'),
  document.getElementById('game-mode2'),
  document.getElementById('game-mode3'),
  document.getElementById('game-mode4')
];

modeSelectors.forEach((selector, index) => {
  selector.addEventListener('change', () => {
    const selectedMode = selector.value;
    sendGameEvent(`jugador_demo_${index + 1}`, 'mode_selected', { mode: selectedMode });
    showNotification(`Modo de juego: ${selectedMode}`, 'info');
  });
});

// Envío de evento cuando se inicia un juego
const startButtons = [
  document.getElementById('start-game'),
  document.getElementById('start-game1'),
  document.getElementById('start-game2'),
  document.getElementById('start-game3'),
  document.getElementById('start-game4')
];

startButtons.forEach((btn, index) => {
  if (btn) {
    btn.addEventListener('click', () => {
      sendGameEvent(`jugador_demo_${index + 1}`, 'game_started');
      showNotification(`Juego iniciado (${index + 1})`, 'success');
    });
  }
});

// Mostrar respuestas del backend en el chat
socket.on('agi_response', (data) => {
  addMessage(`AGI: ${data.content}`, false);
  showNotification('Respuesta del AGI recibida', 'success');
});

// Mostrar mensajes de depuración si hay
socket.on('debug', (data) => {
  console.log('[DEBUG]', data.msg);
  addMessage(`[Sistema] ${data.msg}`, false);
  showNotification(data.msg, 'info');
});

// Conexión de botones del frontend con backend AGI vía Socket.IO con notificaciones, sonidos y vibración

// Prepara sonidos
const notificationSounds = {
  success: new Audio('https://actions.google.com/sounds/v1/cartoon/clang_and_wobble.ogg'),
  info: new Audio('https://actions.google.com/sounds/v1/alarms/beep_short.ogg'),
  error: new Audio('https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg')
};

function vibrate(pattern) {
  if (navigator.vibrate) navigator.vibrate(pattern);
}

// Crear contenedor de notificaciones si no existe
document.addEventListener('DOMContentLoaded', () => {
  if (!document.getElementById('notification-container')) {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
  }
});

function showNotification(msg, type = 'info') {
  const container = document.getElementById('notification-container');
  const note = document.createElement('div');
  note.textContent = msg;
  note.style.background = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff';
  note.style.color = 'white';
  note.style.padding = '10px 15px';
  note.style.borderRadius = '8px';
  note.style.marginBottom = '10px';
  note.style.boxShadow = '0 2px 6px rgba(0,0,0,0.2)';
  note.style.transition = 'opacity 0.5s ease';
  note.style.opacity = '1';

  container.appendChild(note);
  setTimeout(() => {
    note.style.opacity = '0';
    setTimeout(() => container.removeChild(note), 500);
  }, 3000);

  // Reproducir sonido y vibrar
  if (notificationSounds[type]) notificationSounds[type].play();
  if (type === 'success') vibrate([100, 50, 100]);
  else if (type === 'info') vibrate([50]);
  else if (type === 'error') vibrate([200, 100, 200]);
}

// Botón de visión artificial
const startVisionBtn = document.getElementById('start-vision');
let visionActive = false;

startVisionBtn.addEventListener('click', () => {
  if (!visionActive) {
    socket.emit('start_vision');
    startVisionBtn.textContent = 'Detener Visión';
    showNotification('Visión artificial iniciada', 'success');
  } else {
    socket.emit('stop_vision');
    startVisionBtn.textContent = 'Iniciar Visión';
    showNotification('Visión artificial detenida', 'info');
  }
  visionActive = !visionActive;
});

// Envío de eventos del juego al agente AGI
function sendGameEvent(playerId, eventType, metadata = {}) {
  socket.emit('game_event', {
    player_id: playerId,
    event: eventType,
    metadata: metadata
  });
  addMessage(`[Evento enviado] ${eventType} de ${playerId}`, false);
  showNotification(`Evento: ${eventType} enviado`, 'info');
}

// Botón de votación de agente
const agentSelect = document.getElementById('agent-select');
const voteAgentBtn = document.getElementById('vote-agent');

voteAgentBtn.addEventListener('click', () => {
  const selected = agentSelect.value;
  sendGameEvent('jugador_demo', 'agent_voted', { selectedAgent: selected });
  showNotification(`Voto por agente ${selected}`, 'success');
});

// Manejo de selección de modo de juego
const modeSelectors = [
  document.getElementById('game-mode'),
  document.getElementById('game-mode1'),
  document.getElementById('game-mode2'),
  document.getElementById('game-mode3'),
  document.getElementById('game-mode4')
];

modeSelectors.forEach((selector, index) => {
  selector.addEventListener('change', () => {
    const selectedMode = selector.value;
    sendGameEvent(`jugador_demo_${index + 1}`, 'mode_selected', { mode: selectedMode });
    showNotification(`Modo de juego: ${selectedMode}`, 'info');
  });
});

// Envío de evento cuando se inicia un juego
const startButtons = [
  document.getElementById('start-game'),
  document.getElementById('start-game1'),
  document.getElementById('start-game2'),
  document.getElementById('start-game3'),
  document.getElementById('start-game4')
];

startButtons.forEach((btn, index) => {
  if (btn) {
    btn.addEventListener('click', () => {
      sendGameEvent(`jugador_demo_${index + 1}`, 'game_started');
      showNotification(`Juego iniciado (${index + 1})`, 'success');
    });
  }
});

// Mostrar respuestas del backend en el chat
socket.on('agi_response', (data) => {
  addMessage(`AGI: ${data.content}`, false);
  showNotification('Respuesta del AGI recibida', 'success');
});

// Mostrar mensajes de depuración si hay
socket.on('debug', (data) => {
  console.log('[DEBUG]', data.msg);
  addMessage(`[Sistema] ${data.msg}`, false);
  showNotification(data.msg, 'info');
});
# ======================= HANDLERS DE SOCKET.IO ========================
from flask_socketio import emit

@socketio.on('connect')
def handle_connect():
    print('Cliente conectado')
    emit('debug', {'msg': 'Conexión establecida con el servidor Flask'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Cliente desconectado')

@socketio.on('file_upload')
def handle_file_upload(data):
    print("Archivo recibido:", data['name'])
    emit('file_upload_status', {'status': 'recibido', 'name': data['name']})

@socketio.on('audio_upload')
def handle_audio_upload(data):
    print("Audio recibido, formato:", data.get('format', 'desconocido'))
    emit('audio_upload_status', {'status': 'recibido'})

@socketio.on('start_audio_stream')
def handle_start_audio_stream(data):
    input_device = data.get('input_device')
    output_device = data.get('output_device')
    if audio_streamer.start_stream(input_device_index=input_device, output_device_index=output_device):
        emit('audio_stream_status', {'status': 'started'})
    else:
        emit('audio_stream_status', {'status': 'error'})

@socketio.on('stop_audio_stream')
def handle_stop_audio_stream():
    audio_streamer.stop_stream()
    emit('audio_stream_status', {'status': 'stopped'})

@socketio.on('set_audio_volume')
def handle_set_audio_volume(data):
    volume = data.get('volume', 1.0)
    audio_streamer.set_volume(volume)
    emit('debug', {'msg': f'Volumen ajustado a {volume}'})

@socketio.on('query_agi')
def handle_query_agi(data):
    mensaje = data.get('text')
    respuesta = "Respuesta simulada del backend para: " + mensaje
    emit('agi_response', {'content': respuesta})

@socketio.on('start_vision')
def handle_start_vision():
    agi_camera_manager.iniciar_streaming()
    emit('debug', {'msg': 'Visión artificial activada'})

@socketio.on('stop_vision')
def handle_stop_vision():
    agi_camera_manager.detener_streaming()
    emit('debug', {'msg': 'Visión artificial detenida'})

@socketio.on('game_event')
def handle_game_event(data):
    player_id = data.get('player_id')
    event_type = data.get('event')
    metadata = data.get('metadata', {})
    if player_id not in agi_agents:
        agi_agents[player_id] = NeuroSymbolicGameAgent(player_id)
    agi_agents[player_id].check_event(event_type, metadata)
    emit('debug', {'msg': f'Evento procesado para {player_id}: {event_type}'})

# ==================== Inicialización ====================
def start_background_tasks(agi_instance):
    def network_loop():
        while True:
            agi_instance.network.discover_peers()
            time.sleep(30)
    
    def expansion_loop():
        while True:
            agi_instance.expander.ejecutar_autoexpansion()
            time.sleep(3600)
    
    threading.Thread(target=network_loop, daemon=True).start()
    threading.Thread(target=expansion_loop, daemon=True).start()



// (con STT real usando SpeechRecognition + gTTS para respuesta hablada)


app = Flask(__name__)
app.config['SECRET_KEY'] = 'supersecretkey'
socketio = SocketIO(app, cors_allowed_origins="*")

user_response_modes = {}
recognizer = sr.Recognizer()

@socketio.on('connect')
def on_connect():
    print("Cliente conectado")
    emit('debug', {'msg': 'Conectado al servidor AGI'})

@socketio.on('set_response_mode')
def set_response_mode(data):
    sid = request.sid
    mode = data.get('mode', 'text')
    user_response_modes[sid] = mode
    print(f"[Modo respuesta actualizado] {sid} => {mode}")

@socketio.on('prompt_to_model')
def handle_prompt(data):
    prompt = data.get('prompt', '')
    sid = request.sid
    print(f"Prompt recibido: {prompt}")

    respuesta = f"Respuesta AGI para: {prompt}"
    emit('agi_response', {'content': respuesta})

    modo = user_response_modes.get(sid, 'text')
    if modo in ['voice', 'both']:
        tts = gTTS(respuesta, lang='es')
        buf = io.BytesIO()
        tts.write_to_fp(buf)
        audio_data = base64.b64encode(buf.getvalue()).decode('utf-8')
        emit('audio_response', {'audio_base64': audio_data})

@socketio.on('audio_stream_chunk')
def handle_audio_chunk(data):
    print("Chunk de audio recibido (procesando STT real)")
    audio_base64 = data.get('audio_base64')
    audio_bytes = base64.b64decode(audio_base64)

    with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as temp_audio:
        temp_audio.write(audio_bytes)
        temp_audio.flush()

        # Convertir webm a wav para SpeechRecognition
        sound = AudioSegment.from_file(temp_audio.name, format="webm")
        wav_path = temp_audio.name.replace('.webm', '.wav')
        sound.export(wav_path, format="wav")

        with sr.AudioFile(wav_path) as source:
            audio = recognizer.record(source)
            try:
                text = recognizer.recognize_google(audio, language='es-ES')
                print(f"Texto reconocido: {text}")
                emit('agi_response', {'content': text})
            except sr.UnknownValueError:
                emit('agi_response', {'content': 'No se entendió el audio.'})
            except sr.RequestError as e:
                emit('agi_response', {'content': f'Error STT: {str(e)}'})

@socketio.on('disconnect')
def on_disconnect():
    sid = request.sid
    print(f"Cliente desconectado: {sid}")
    if sid in user_response_modes:
        del user_response_modes[sid]


// (con STT real usando SpeechRecognition + gTTS para respuesta hablada)



app = Flask(__name__)
app.config['SECRET_KEY'] = 'supersecretkey'
socketio = SocketIO(app, cors_allowed_origins="*")

user_response_modes = {}
recognizer = sr.Recognizer()

@socketio.on('connect')
def on_connect():
    print("Cliente conectado")
    emit('debug', {'msg': 'Conectado al servidor AGI'})

@socketio.on('set_response_mode')
def set_response_mode(data):
    sid = request.sid
    mode = data.get('mode', 'text')
    user_response_modes[sid] = mode
    print(f"[Modo respuesta actualizado] {sid} => {mode}")

@socketio.on('prompt_to_model')
def handle_prompt(data):
    prompt = data.get('prompt', '')
    sid = request.sid
    print(f"Prompt recibido: {prompt}")

    respuesta = f"Respuesta AGI para: {prompt}"
    emit('agi_response', {'content': respuesta})

    modo = user_response_modes.get(sid, 'text')
    if modo in ['voice', 'both']:
        tts = gTTS(respuesta, lang='es')
        buf = io.BytesIO()
        tts.write_to_fp(buf)
        audio_data = base64.b64encode(buf.getvalue()).decode('utf-8')
        emit('audio_response', {'audio_base64': audio_data})

@socketio.on('audio_stream_chunk')
def handle_audio_chunk(data):
    print("Chunk de audio recibido (procesando STT real)")
    audio_base64 = data.get('audio_base64')
    audio_bytes = base64.b64decode(audio_base64)

    with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as temp_audio:
        temp_audio.write(audio_bytes)
        temp_audio.flush()

        # Convertir webm a wav para SpeechRecognition
        sound = AudioSegment.from_file(temp_audio.name, format="webm")
        wav_path = temp_audio.name.replace('.webm', '.wav')
        sound.export(wav_path, format="wav")

        with sr.AudioFile(wav_path) as source:
            audio = recognizer.record(source)
            try:
                text = recognizer.recognize_google(audio, language='es-ES')
                print(f"Texto reconocido: {text}")
                emit('agi_response', {'content': text})
            except sr.UnknownValueError:
                emit('agi_response', {'content': 'No se entendió el audio.'})
            except sr.RequestError as e:
                emit('agi_response', {'content': f'Error STT: {str(e)}'})

@socketio.on('disconnect')
def on_disconnect():
    sid = request.sid
    print(f"Cliente desconectado: {sid}")
    if sid in user_response_modes:
        del user_response_modes[sid]

// === RESPUESTAS DEL BACKEND ===
socket.on('agi_response', (data) => {
  addMessage(`AGI: ${data.content}`, false);
  showNotification('Respuesta del AGI recibida', 'success');
});

socket.on('debug', (data) => {
  console.log('[DEBUG]', data.msg);
  addMessage(`[Sistema] ${data.msg}`, false);
  showNotification(data.msg, 'info');
});
</script>
<script src="https://cdn.socket.io/4.0.1/socket.io.min.js"></script>
<script>
const socket = io('http://localhost:5000');
const promptInput = document.getElementById('prompt-input');
const promptSend = document.getElementById('prompt-send');
const micStart = document.getElementById('mic-start');
const audioPlayer = document.getElementById('audio-player');
const responseMode = document.getElementById('response-mode');
const recordingIndicator = document.getElementById('recording-indicator');
const recordingProgress = document.getElementById('recording-progress');

responseMode.addEventListener('change', () => {
  socket.emit('set_response_mode', { mode: responseMode.value });
});

promptSend.addEventListener('click', () => {
  const text = promptInput.value.trim();
  if (text) {
    socket.emit('prompt_to_model', { prompt: text });
    promptInput.value = '';
  }
});

socket.on('agi_response', data => {
  const msg = data.content;
  console.log('Texto:', msg);
  alert(`AGI: ${msg}`);
});
socket.on('audio_response', data => {
  const audioData = data.audio_base64;
  if (audioData) {
    audioPlayer.src = 'data:audio/mp3;base64,' + audioData;
    audioPlayer.play();
  }
});

</script>
    


if __name__ == "__main__":
    with app.app_context():
        db.create_all()
   
    obtener_imagen()
    escuchar_voz()
    asyncio.run(conversar())
    AGINetwork().register_service(5000)
    AGINetwork().discover_peers()
    socketio.run(app, debug=True)
socketio.run(app, host='0.0.0.0', port=5000)

    finder = ModelFinder()
    modelos = finder.find_models()
    for modelo in modelos:
        print(f"🔍 {modelo['name']} ({modelo['extension']}) -> {modelo['path']}")

    
    agi = AGICore()
    start_background_tasks(agi)
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    socketio = SocketIO(app, cors_allowed_origins="*")

     if __name__ == "__main__":
    # Ensure directories exist (moved from AGICore to be more general)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['AUDIO_FOLDER'], exist_ok=True)
    os.makedirs('models', exist_ok=True) # Ensure models dir exists
    os.makedirs('knowledge_base_ns', exist_ok=True)
    os.makedirs('agent_directory', exist_ok=True)


    with app.app_context():
        db.create_all() # Creates database if it doesn't exist

    # The global mamba model loading could be removed if dynamic loading is sufficient
    # print("Loading global Mamba models (if any specified)...")
    # try:
    #     model_mamba2_370m = AutoModel.from_pretrained("state-spaces/mamba2-370m")
    #     print("mamba2-370m loaded globally.")
    #     model_mamba_370m = AutoModel.from_pretrained("state-spaces/mamba-370m")
    #     print("mamba-370m loaded globally.")
    # except Exception as e:
    #     print(f"Error loading global Mamba models: {e}")


    # The `agi_system` simulation part (from the other main.py)
    # This is separate from the Flask app. If you want to run it, it's a different process.
    # from agi_system.cognitive_agent import NeuroCognitiveAgent
    # from agi_system.simulated_environment import SimulatedEnvironment
    # print("\n--- Starting AGI System Simulation (separate from Flask app) ---")
    # try:
    #     environment = SimulatedEnvironment(agent_directory_path="agent_directory")
    #     possible_symbolic_actions = ["request_task", "process_data", "search_internet_info", "send_message_to_agent", "idle", "diagnose_system"]
    #     main_sim_agent = NeuroCognitiveAgent(agent_id="SimCognito", environment=environment, symbolic_actions=possible_symbolic_actions)
    #     for i in range(3): # Run a few cycles for demo
    #         print(f"--- SIMULATION CYCLE {i+1} ---")
    #         main_sim_agent.run_cycle()
    #         time.sleep(0.1)
    #     print("--- AGI System Simulation Part Done ---")
    # except Exception as e:
    #     print(f"Error running agi_system simulation: {e}")


    print("Initializing AGICore for Flask app...")
    agi_core_instance = AGICore() # Renamed to avoid conflict
    start_background_tasks(agi_core_instance)
    
    print("Starting Flask-SocketIO server on http://0.0.0.0:5000")
    # Use gunicorn for better performance if available, otherwise Flask dev server
    # For development:
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    # For a more production-like setup (if gunicorn is installed):
    # import subprocess
    # subprocess.call(['gunicorn', '-w', '4', '-k', 'geventwebsocket.gunicorn.workers.GeventWebSocketWorker', '-b', '0.0.0.0:5000', 'main_app:app'])






