### config.py - Selección dinámica del entorno según capacidades de la máquina

def seleccionar_entorno(info_maquina):
    """
    Elige un entorno basado en las características de la máquina.
    Puedes personalizar esta lógica según tus necesidades reales.
    """
    ram = info_maquina["ram_gb"]
    cpu = info_maquina["cpu_cores"]

    if ram < 4 or cpu <= 2:
        return "ecosistema"  # entorno más liviano
    elif ram >= 4 and cpu <= 4:
        return "financiero"
    elif ram >= 8 and cpu > 4:
        return "industrial"
    elif ram >= 16 and cpu > 6:
        return "espacial"
    else:
        return "ecosistema"  # fallback
