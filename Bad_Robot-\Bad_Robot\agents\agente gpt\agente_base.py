### agentes/base.py - Clase base de agente evolutivo con predicción y comunicación

import random
from agentes.prediccion import predecir_estado

class Agente:
    def __init__(self, id, entorno):
        self.id = id
        self.entorno = entorno
        self.historial = []
        self.genotipo = {
            "buscar": random.random(),
            "esconder": random.random(),
            "explorar": random.random()
        # Ejemplo de genotipo más complejo en agentes/base.py (constructor)
        self.genotipo = genotipo if genotipo else {
            "tipo": random.choice(["inversor", "especulador", "conservador"]),
            "agresividad": random.uniform(0.1, 1.0),
            "paciencia": random.uniform(0.1, 1.0),
}

        }

    def decidir(self, observacion):
        pred = predecir_estado(observacion)
        noticias = observacion.get("noticias", {})

        decision = "mantener"
    if self.genotipo["tipo"] == "inversor":
        if pred.get("tendencia") == "alcista" and noticias.get("positiva", False):
            decision = "comprar"
        elif pred.get("tendencia") == "bajista":
            decision = "vender"
    elif self.genotipo["tipo"] == "especulador":
        if random.random() < self.genotipo["agresividad"]:
            decision = random.choice(["comprar", "vender"])
    elif self.genotipo["tipo"] == "conservador":
        if pred.get("tendencia") == "bajista" and noticias.get("negativa", False):
            decision = "vender"

    return decision

        scores = {
            "buscar_comida": self.genotipo["buscar"] * pred.get("comida", 1),
            "esconderse": self.genotipo["esconder"] * (1 + observacion.get("depredadores", 0)),
            "explorar": self.genotipo["explorar"] * random.random()
        }

        accion = max(scores, key=scores.get)
        self.historial.append((observacion, accion))
        return accion
