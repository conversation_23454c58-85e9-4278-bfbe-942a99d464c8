import subprocess
from pathlib import Path
import re

class GitManager:
    def __init__(self, repo_path=None):
        self.repo_path = repo_path or Path.cwd()
        
    async def execute_git_command(self, command):
        try:
            result = subprocess.run(
                ['git'] + command,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"Error: {e.stderr.strip()}"
    
    async def get_status(self):
        return await self.execute_git_command(['status'])
    
    async def commit_changes(self, message):
        await self.execute_git_command(['add', '.'])
        return await self.execute_git_command(['commit', '-m', message])
    
    async def push_changes(self, remote='origin', branch='main'):
        return await self.execute_git_command(['push', remote, branch])
    
    async def pull_changes(self, remote='origin', branch='main'):
        return await self.execute_git_command(['pull', remote, branch])
    
    async def resolve_conflicts(self):
        status = await self.get_status()
        if 'both modified:' in status:
            conflicted_files = re.findall(r'both modified:\s+(.*)', status)
            analysis = await self.analyze_conflicts(conflicted_files)
            return await self.apply_conflict_resolution(analysis)
        return "No hay conflictos para resolver"
    
    async def analyze_conflicts(self, files):
        analysis = {}
        for file in files:
            with open(self.repo_path/file, 'r') as f:
                content = f.read()
                conflict_markers = content.count('<<<<<<<')
                analysis[file] = {
                    'conflict_count': conflict_markers,
                    'file_type': file.split('.')[-1]
                }
        return analysis
    
    async def apply_conflict_resolution(self, analysis):
        # Usar IA para resolver conflictos complejos
        resolution_plan = {}
        for file, data in analysis.items():
            if data['file_type'] in ['py', 'js']:
                resolution_plan[file] = await self.ai_resolve_code_conflict(file)
            else:
                resolution_plan[file] = await self.simple_conflict_resolution(file)
        
        # Aplicar resoluciones
        results = {}
        for file, resolution in resolution_plan.items():
            with open(self.repo_path/file, 'w') as f:
                f.write(resolution)
            results[file] = "Conflicto resuelto"
        
        return results
    
    async def ai_resolve_code_conflict(self, file_path):
        from multi_ai_integration import MultiAIIntegration
        ai = MultiAIIntegration()
        
        with open(self.repo_path/file_path, 'r') as f:
            content = f.read()
        
        prompt = f"""
        Resuelve este conflicto de merge en el archivo {file_path}.
        Mantén las funcionalidades de ambas versiones cuando sea posible.
        
        Contenido conflictivo:
        {content}
        
        Devuelve SOLO el código resuelto, sin marcas de conflicto.
        """
        
        response = await ai.query_ai_models(prompt)
        return response['openai']  # Usar el mejor modelo disponible