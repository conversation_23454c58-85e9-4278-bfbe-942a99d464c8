import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
import json
import asyncio
import re
from concurrent.futures import ThreadPoolExecutor
import zmq  # Para comunicación entre agentes
from .base_agent import BaseAgent
# --- En tu archivo secundario.py (o donde esté la lógica de tus workers) ---
import zmq
import time
import sys
import uuid
import json # Si usas JSON
# --- En tu archivo secundario.py (o donde esté la lógica de tus workers) ---
import zmq
import time
import sys
import uuid
import json # Si usas JSON

PRINCIPAL_ADDRESS = "tcp://localhost:5555" # Dirección del ROUTER principal

def secundario_dealer_loop(worker_id):
    context = zmq.Context()
    dealer_socket = context.socket(zmq.DEALER)

    # MUY IMPORTANTE: Establecer una identidad única para este DEALER
    # Esto permite al ROUTER saber quién es quién y responder correctamente.
    # La identidad DEBE ser bytes.
    dealer_socket.setsockopt(zmq.IDENTITY, worker_id.encode('utf-8'))

    dealer_socket.connect(PRINCIPAL_ADDRESS)
    print(f"Secundario (DEALER {worker_id}) conectado a {PRINCIPAL_ADDRESS}")

    poller = zmq.Poller()
    poller.register(dealer_socket, zmq.POLLIN)

    # Enviar un mensaje inicial al principal para "registrarse" o decir "estoy listo"
    mensaje_inicial = {"id": worker_id, "status": "listo"}
    # DEALER envía multipart con un frame vacío primero, luego el payload
    dealer_socket.send_multipart([
        b'', # Frame vacío para compatibilidad con ROUTER que espera [identidad, delimitador, payload]
        json.dumps(mensaje_inicial).encode('utf-8')
    ])
    print(f"Secundario {worker_id}: Enviado mensaje inicial/listo.")
    
    # También podrías enviar un PING simple para probar la conexión
    # dealer_socket.send_multipart([b'', b"PING"])

    try:
        while True: # Bucle del worker
            # 1. Esperar mensajes/tareas del principal (ROUTER)
            socks = dict(poller.poll(timeout=5000)) # Esperar 5s por mensajes

            if dealer_socket in socks and socks[dealer_socket] == zmq.POLLIN:
                # DEALER recibe mensajes como [ (delimitador_vacio opcional), payload ]
                # El ROUTER ya gestionó la identidad.
                message_parts = dealer_socket.recv_multipart()
                
                payload_bytes = None
                if message_parts:
                    if len(message_parts) > 1 and message_parts[0] == b'': # Hay delimitador
                        payload_bytes = message_parts[1]
                    else:
                        payload_bytes = message_parts[0]

                if payload_bytes:
                    try:
                        mensaje_recibido = json.loads(payload_bytes.decode('utf-8'))
                        print(f"Secundario {worker_id}: Recibido (JSON) del Principal: {mensaje_recibido}")

                        # Lógica para procesar el mensaje/tarea
                        if mensaje_recibido.get("type") == "PROCESAR_DATOS":
                            data_id = mensaje_recibido.get("data_id")
                            print(f"Secundario {worker_id}: Procesando datos para ID {data_id}...")
                            time.sleep(2) # Simular trabajo
                            resultado = {"worker_id": worker_id, "data_id": data_id, "resultado": f"Datos {data_id} procesados OK"}
                            
                            dealer_socket.send_multipart([
                                b'',
                                json.dumps(resultado).encode('utf-8')
                            ])
                            print(f"Secundario {worker_id}: Enviado resultado al Principal.")

                        elif mensaje_recibido.get("status") == "recibido": # Es un ACK
                            print(f"Secundario {worker_id}: Principal confirmó recepción de: {mensaje_recibido.get('original_message')}")
                        
                    except json.JSONDecodeError:
                        mensaje_str = payload_bytes.decode('utf-8')
                        print(f"Secundario {worker_id}: Recibido (str) del Principal: '{mensaje_str}'")
                        if mensaje_str == "PONG":
                            print(f"Secundario {worker_id}: Recibido PONG del Principal.")
                        elif "ACK_RESULTADO" in mensaje_str:
                             print(f"Secundario {worker_id}: Principal confirmó resultado.")


            # 2. Lógica del secundario para enviar datos proactivamente (si es necesario)
            # (Por ejemplo, si el secundario genera datos y los envía sin que se los pidan)
            # if alguna_condicion_para_enviar_datos_proactivamente:
            #     datos_proactivos = {"type": "DATOS_NUEVOS", "payload": "info importante", "source_id": worker_id}
            #     print(f"Secundario {worker_id}: Enviando datos proactivos...")
            #     dealer_socket.send_multipart([
            #         b'',
            #         json.dumps(datos_proactivos).encode('utf-8')
            #     ])

            # Pequeña pausa para no saturar la CPU si no hay mensajes
            # time.sleep(0.1)


    except KeyboardInterrupt:
        print(f"\nSecundario (DEALER {worker_id}) terminando...")
    finally:
        print(f"Cerrando socket de {worker_id} y terminando contexto ZMQ.")
        dealer_socket.close()
        context.term()

# Esta función sería llamada para iniciar cada proceso secundario
def lanzar_secundario_proceso(worker_id_arg):
    # Llama a la función principal del secundario
    secundario_dealer_loop(worker_id_arg)



PRINCIPAL_ADDRESS = "tcp://localhost:5000" # Dirección del ROUTER principal

def secundario_dealer_loop(worker_id):
    context = zmq.Context()
    dealer_socket = context.socket(zmq.DEALER)

    # MUY IMPORTANTE: Establecer una identidad única para este DEALER
    # Esto permite al ROUTER saber quién es quién y responder correctamente.
    # La identidad DEBE ser bytes.
    dealer_socket.setsockopt(zmq.IDENTITY, worker_id.encode('utf-8'))

    dealer_socket.connect(PRINCIPAL_ADDRESS)
    print(f"Secundario (DEALER {worker_id}) conectado a {PRINCIPAL_ADDRESS}")

    poller = zmq.Poller()
    poller.register(dealer_socket, zmq.POLLIN)

    # Enviar un mensaje inicial al principal para "registrarse" o decir "estoy listo"
    mensaje_inicial = {"id": worker_id, "status": "listo"}
    # DEALER envía multipart con un frame vacío primero, luego el payload
    dealer_socket.send_multipart([
        b'', # Frame vacío para compatibilidad con ROUTER que espera [identidad, delimitador, payload]
        json.dumps(mensaje_inicial).encode('utf-8')
    ])
    print(f"Secundario {worker_id}: Enviado mensaje inicial/listo.")
    
    # También podrías enviar un PING simple para probar la conexión
    # dealer_socket.send_multipart([b'', b"PING"])

    try:
        while True: # Bucle del worker
            # 1. Esperar mensajes/tareas del principal (ROUTER)
            socks = dict(poller.poll(timeout=5000)) # Esperar 5s por mensajes

            if dealer_socket in socks and socks[dealer_socket] == zmq.POLLIN:
                # DEALER recibe mensajes como [ (delimitador_vacio opcional), payload ]
                # El ROUTER ya gestionó la identidad.
                message_parts = dealer_socket.recv_multipart()
                
                payload_bytes = None
                if message_parts:
                    if len(message_parts) > 1 and message_parts[0] == b'': # Hay delimitador
                        payload_bytes = message_parts[1]
                    else:
                        payload_bytes = message_parts[0]

                if payload_bytes:
                    try:
                        mensaje_recibido = json.loads(payload_bytes.decode('utf-8'))
                        print(f"Secundario {worker_id}: Recibido (JSON) del Principal: {mensaje_recibido}")

                        # Lógica para procesar el mensaje/tarea
                        if mensaje_recibido.get("type") == "PROCESAR_DATOS":
                            data_id = mensaje_recibido.get("data_id")
                            print(f"Secundario {worker_id}: Procesando datos para ID {data_id}...")
                            time.sleep(2) # Simular trabajo
                            resultado = {"worker_id": worker_id, "data_id": data_id, "resultado": f"Datos {data_id} procesados OK"}
                            
                            dealer_socket.send_multipart([
                                b'',
                                json.dumps(resultado).encode('utf-8')
                            ])
                            print(f"Secundario {worker_id}: Enviado resultado al Principal.")

                        elif mensaje_recibido.get("status") == "recibido": # Es un ACK
                            print(f"Secundario {worker_id}: Principal confirmó recepción de: {mensaje_recibido.get('original_message')}")
                        
                    except json.JSONDecodeError:
                        mensaje_str = payload_bytes.decode('utf-8')
                        print(f"Secundario {worker_id}: Recibido (str) del Principal: '{mensaje_str}'")
                        if mensaje_str == "PONG":
                            print(f"Secundario {worker_id}: Recibido PONG del Principal.")
                        elif "ACK_RESULTADO" in mensaje_str:
                             print(f"Secundario {worker_id}: Principal confirmó resultado.")


            # 2. Lógica del secundario para enviar datos proactivamente (si es necesario)
            # (Por ejemplo, si el secundario genera datos y los envía sin que se los pidan)
            # if alguna_condicion_para_enviar_datos_proactivamente:
            #     datos_proactivos = {"type": "DATOS_NUEVOS", "payload": "info importante", "source_id": worker_id}
            #     print(f"Secundario {worker_id}: Enviando datos proactivos...")
            #     dealer_socket.send_multipart([
            #         b'',
            #         json.dumps(datos_proactivos).encode('utf-8')
            #     ])

            # Pequeña pausa para no saturar la CPU si no hay mensajes
            # time.sleep(0.1)


    except KeyboardInterrupt:
        print(f"\nSecundario (DEALER {worker_id}) terminando...")
    finally:
        print(f"Cerrando socket de {worker_id} y terminando contexto ZMQ.")
        dealer_socket.close()
        context.term()

# Esta función sería llamada para iniciar cada proceso secundario
def lanzar_secundario_proceso(worker_id_arg):
    # Llama a la función principal del secundario
    secundario_dealer_loop(worker_id_arg)





class AGIAgent(BaseAgent):
    def __init__(self, agent_id: str = "agi_agent"):
        super().__init__(agent_id)
        self.knowledge_base = {}
        
    def receive_message(self, message: dict):
        self.logger.info(f"Mensaje recibido: {json.dumps(message, indent=2)}")
        
        if message.get('type') == 'knowledge_update':
            self._update_knowledge(message)
        elif message.get('type') == 'query':
            self._process_query(message)
    
    def _update_knowledge(self, message: dict):
        topic = message.get('topic')
        data = message.get('data')
        if topic and data:
            self.knowledge_base[topic] = data
            self.logger.info(f"Base de conocimiento actualizada: {topic}")
    
    def _process_query(self, message: dict):
        query = message.get('query')
        if query in self.knowledge_base:
            response = {
                "type": "query_response",
                "query": query,
                "result": self.knowledge_base[query]
            }
            self.broadcast_message(message['broker'], response)

# Configuración de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AgentMessage:
    sender: str
    receiver: str
    content: Dict[str, Any]
    message_type: str = "directive"

class SymbolicReasoner:
    def __init__(self, agent_id: str = "symbolic_reasoner"):
        """Inicializa el motor de razonamiento simbólico avanzado"""
        self.agent_id = agent_id
        self.knowledge_base = self._initialize_knowledge_base()
        self.operators = self._initialize_operators()
        self.connected_agents = {}  # {agent_id: address}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Configuración de ZMQ para comunicación
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.REP)
        self.socket.bind(f"tcp://*:5555")  # Puerto base para comunicación
        
        # Iniciar el loop de mensajes en segundo plano
        self.running = True
        self.message_loop_task = asyncio.create_task(self._message_loop())

    def _initialize_knowledge_base(self) -> Dict[str, Dict]:
        """Inicializa la base de conocimiento con reglas predefinidas"""
        return {
            'device_status_rules': {
                'if': ['device_status', '==', 'error'],
                'then': 'send_alert',
                'params': {'priority': 'high'},
                'target': 'alert_manager'
            },
            'temperature_rules': {
                'if': ['temperature', '>', 30],
                'then': 'activate_cooling',
                'params': {'duration': 300},
                'target': 'climate_control'
            },
            'connection_rules': {
                'if': ['connection_status', '==', 'disconnected'],
                'then': 'reconnect',
                'params': {'retries': 3},
                'target': 'network_manager'
            },
            'collaboration_rules': {
                'if': ['task_complexity', '>', 5],
                'then': 'request_collaboration',
                'params': {'min_agents': 2},
                'target': 'agent_coordinator'
            }
        }

    def _initialize_operators(self) -> Dict[str, callable]:
        """Inicializa los operadores de comparación disponibles"""
        return {
            '==': lambda x, y: x == y,
            '!=': lambda x, y: x != y,
            '>': lambda x, y: x > y,
            '<': lambda x, y: x < y,
            '>=': lambda x, y: x >= y,
            '<=': lambda x, y: x <= y,
            'in': lambda x, y: x in y,
            'contains': lambda x, y: y in x,
            'matches': lambda x, y: bool(re.match(y, str(x)))
        }

    async def _message_loop(self):
        """Loop asíncrono para recibir y procesar mensajes"""
        while self.running:
            try:
                message = await asyncio.get_event_loop().run_in_executor(
                    self.executor, 
                    lambda: self.socket.recv_json()
                )
                logger.info(f"Recibido mensaje: {message}")
                
                response = await self._handle_message(message)
                self.socket.send_json(asdict(response))
                
            except Exception as e:
                logger.error(f"Error en message_loop: {e}")
                await asyncio.sleep(1)

    async def _handle_message(self, message_data: Dict) -> AgentMessage:
        """Procesa un mensaje entrante"""
        try:
            message = AgentMessage(**message_data)
            
            if message.receiver != self.agent_id and message.receiver != "broadcast":
                return AgentMessage(
                    sender=self.agent_id,
                    receiver=message.sender,
                    content={"status": "error", "message": "Invalid recipient"},
                    message_type="error"
                )
            
            # Procesamiento basado en el tipo de mensaje
            if message.message_type == "directive":
                result = await self.process(message.content)
                return AgentMessage(
                    sender=self.agent_id,
                    receiver=message.sender,
                    content=result,
                    message_type="response"
                )
                
            elif message.message_type == "query":
                response = self._handle_query(message.content)
                return AgentMessage(
                    sender=self.agent_id,
                    receiver=message.sender,
                    content=response,
                    message_type="response"
                )
                
            else:
                return AgentMessage(
                    sender=self.agent_id,
                    receiver=message.sender,
                    content={"status": "error", "message": "Unknown message type"},
                    message_type="error"
                )
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return AgentMessage(
                sender=self.agent_id,
                receiver=message.sender if 'sender' in locals() else "unknown",
                content={"status": "error", "message": str(e)},
                message_type="error"
            )

    def _handle_query(self, query: Dict) -> Dict:
        """Maneja consultas sobre el estado o conocimiento del agente"""
        query_type = query.get("type")
        
        if query_type == "get_rules":
            return {
                "status": "success",
                "rules": list(self.knowledge_base.keys())
            }
            
        elif query_type == "get_rule_details":
            rule_name = query.get("rule_name")
            if rule_name in self.knowledge_base:
                return {
                    "status": "success",
                    "rule": self.knowledge_base[rule_name]
                }
            return {"status": "error", "message": "Rule not found"}
            
        else:
            return {"status": "error", "message": "Unknown query type"}

    async def send_message(self, receiver: str, content: Dict, msg_type: str = "directive") -> Dict:
        """Envía un mensaje a otro agente y espera respuesta"""
        if receiver not in self.connected_agents:
            return {"status": "error", "message": "Agent not connected"}
            
        try:
            message = AgentMessage(
                sender=self.agent_id,
                receiver=receiver,
                content=content,
                message_type=msg_type
            )
            
            # Enviar a través de ZMQ (simplificado)
            response = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                lambda: self._send_zmq_message(self.connected_agents[receiver], message)
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return {"status": "error", "message": str(e)}

    def _send_zmq_message(self, address: str, message: AgentMessage) -> Dict:
        """Envía un mensaje usando ZMQ (síncrono)"""
        try:
            context = zmq.Context()
            socket = context.socket(zmq.REQ)
            socket.connect(address)
            socket.send_json(asdict(message))
            
            # Esperar respuesta con timeout
            if socket.poll(3000):  # 3 segundos timeout
                return socket.recv_json()
            return {"status": "error", "message": "Timeout waiting for response"}
            
        finally:
            socket.close()
            context.term()

    def connect_to_agent(self, agent_id: str, address: str):
        """Establece conexión con otro agente"""
        self.connected_agents[agent_id] = address
        logger.info(f"Conectado a agente {agent_id} en {address}")

    def add_rule(self, rule_name: str, condition: List, action: str, 
               params: Optional[Dict] = None, target: Optional[str] = None):
        """Añade una nueva regla a la base de conocimiento"""
        self.knowledge_base[rule_name] = {
            'if': condition,
            'then': action,
            'params': params or {},
            'target': target or self.agent_id
        }
        logger.info(f"Regla añadida: {rule_name}")

    async def process(self, message: Dict) -> Dict:
        """Procesa un mensaje usando razonamiento simbólico"""
        if not isinstance(message, dict):
            return {
                "status": "error",
                "message": "El mensaje debe ser un diccionario",
                "matched_rule": None
            }
            
        for rule_name, rule in self.knowledge_base.items():
            if self._match_condition(rule['if'], message):
                logger.info(f"Regla activada: {rule_name}")
                
                # Determinar si la acción es local o remota
                if rule.get('target') != self.agent_id:
                    return await self._execute_remote_action(rule, message)
                return self._execute_action(rule['then'], message, rule.get('params', {}))
        
        return {
            "status": "no_match",
            "action": "no_op",
            "response": None,
            "matched_rule": None
        }

    async def _execute_remote_action(self, rule: Dict, message: Dict) -> Dict:
        """Ejecuta una acción en un agente remoto"""
        target_agent = rule.get('target')
        if target_agent not in self.connected_agents:
            return {
                "status": "error",
                "message": f"Agente objetivo no conectado: {target_agent}",
                "matched_rule": None
            }
            
        action_message = {
            "trigger": rule['then'],
            "parameters": rule.get('params', {}),
            "context": message
        }
        
        response = await self.send_message(
            receiver=target_agent,
            content=action_message,
            msg_type="directive"
        )
        
        return {
            "status": "remote_action",
            "action": rule['then'],
            "response": response,
            "matched_rule": rule.get('target')
        }

    def _match_condition(self, condition: List, message: Dict) -> bool:
        """Evalúa si la condición coincide con el mensaje"""
        if len(condition) != 3:
            logger.error("La condición debe tener 3 elementos [variable, operador, valor]")
            return False
            
        variable, operator, expected_value = condition
        
        if variable not in message:
            return False
            
        actual_value = message[variable]
        
        if operator not in self.operators:
            logger.error(f"Operador no soportado: {operator}")
            return False
            
        try:
            return self.operators[operator](actual_value, expected_value)
        except Exception as e:
            logger.error(f"Error evaluando condición: {e}")
            return False

    def _execute_action(self, action: str, message: Dict, params: Dict) -> Dict:
        """Ejecuta una acción local"""
        action_method = getattr(self, f"action_{action}", None)
        
        if action_method:
            try:
                response = action_method(message, **params)
                return {
                    "status": "success",
                    "action": action,
                    "response": response,
                    "matched_rule": True
                }
            except Exception as e:
                logger.error(f"Error ejecutando acción {action}: {e}")
                return {
                    "status": "error",
                    "action": action,
                    "response": {"error": str(e)},
                    "matched_rule": True
                }
        
        # Acción por defecto si no se encuentra el método específico
        return {
            "status": "default_action",
            "action": action,
            "response": {
                "message": message,
                "params": params,
                "default_action": True
            },
            "matched_rule": True
        }

    # Métodos de acción específicos
    def action_send_alert(self, message: Dict, priority: str = 'medium') -> Dict:
        """Envía una alerta al sistema de notificaciones"""
        alert_data = {
            "source": self.agent_id,
            "priority": priority,
            "context": message
        }
        
        # Aquí iría la lógica real de envío de alerta
        logger.info(f"Enviando alerta: {alert_data}")
        return {"alert_sent": True, "alert_data": alert_data}

    def action_activate_cooling(self, message: Dict, duration: int = 60) -> Dict:
        """Activa el sistema de enfriamiento"""
        logger.info(f"Activando enfriamiento por {duration} segundos")
        return {
            "cooling_activated": True,
            "duration": duration,
            "temperature": message.get('temperature')
        }

    def action_reconnect(self, message: Dict, retries: int = 1) -> Dict:
        """Intenta reconectar un dispositivo"""
        logger.info(f"Intentando reconexión con {retries} reintentos")
        return {
            "reconnection_attempted": True,
            "retries": retries,
            "device": message.get('device_id')
        }

    def action_request_collaboration(self, message: Dict, min_agents: int = 1) -> Dict:
        """Solicita colaboración a otros agentes"""
        logger.info(f"Solicitando colaboración de {min_agents} agentes")
        return {
            "collaboration_requested": True,
            "min_agents": min_agents,
            "task": message.get('task_description')
        }

    async def shutdown(self):
        """Apaga el agente de manera controlada"""
        self.running = False
        self.message_loop_task.cancel()
        self.socket.close()
        self.context.term()
        self.executor.shutdown()
        logger.info("Agente apagado correctamente")


# Ejemplo de integración con otros agentes
async def integrate_with_dqn_agent(reasoner: SymbolicReasoner, dqn_address: str):
    """Configura la integración con un agente DQN"""
    reasoner.connect_to_agent("dqn_agent", dqn_address)
    
    # Añadir regla especial para decisiones complejas
    reasoner.add_rule(
        "complex_decision_rule",
        ["decision_complexity", ">", 7],
        "request_dqn_decision",
        {"state_vars": ["situation", "options"]},
        "dqn_agent"
    )
    logger.info("Integración con DQN Agent configurada")


if __name__ == "__main__":
    async def main():
        # Crear instancia del razonador
        reasoner = SymbolicReasoner("symbolic_reasoner_1")
        
        # Simular conexión con otros agentes
        reasoner.connect_to_agent("alert_manager", "tcp://localhost:5000")
        reasoner.connect_to_agent("dqn_agent", "tcp://localhost:5000")
        
        # Ejemplo de procesamiento
        test_messages = [
            {"device_status": "error", "temperature": 25},
            {"temperature": 35, "connection_status": "connected"},
            {"connection_status": "disconnected", "battery_level": 15},
            {"decision_complexity": 8, "situation": "crisis", "options": ["a", "b"]},
            {"invalid": "message"}
        ]
        
        for msg in test_messages:
            result = await reasoner.process(msg)
            logger.info(f"\nMensaje: {msg}\nResultado: {result}\n{'-'*50}")
        
        await asyncio.sleep(2)
        await reasoner.shutdown()

    asyncio.run(main())


    # Si ejecutas este script directamente, toma el ID del argumento o genera uno
    if len(sys.argv) > 1:
        worker_id_cli = sys.argv[1]
    else:
        worker_id_cli = f"Worker-{uuid.uuid4().hex[:6]}" # ID único si no se proporciona
    
    secundario_dealer_loop(worker_id_cli)