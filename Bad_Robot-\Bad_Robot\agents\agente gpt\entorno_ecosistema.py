### entornos/ecosistema.py - Entorno de simulación natural

import random

class Ecosistema:
    def __init__(self):
        self.estado = {
            "comida": 100,
            "depredadores": 5,
            "clima": "templado",
            "ciclo": 0
        }
        self.recompensas = {}

    def obtener_estado(self):
        return self.estado.copy()

    def aplicar_accion(self, agente_id, accion):
        recompensa = 0
        if accion == "buscar_comida":
            encontrada = random.randint(0, 20)
            self.estado["comida"] -= encontrada
            recompensa = encontrada
        elif accion == "esconderse":
            recompensa = random.choice([0, 1])  # evitar depredador
        elif accion == "explorar":
            cambio = random.choice(["soleado", "lluvia", "tormenta"])
            self.estado["clima"] = cambio
            recompensa = 1 if cambio == "soleado" else -1

        self.recompensas[agente_id] = recompensa

    def actualizar(self):
        self.estado["comida"] += random.randint(5, 15)
        self.estado["depredadores"] += random.choice([-1, 0, 1])
        self.estado["ciclo"] += 1

    def mostrar_resultados(self):
        print("\n--- Resultados del ecosistema ---")
        for agente_id, recompensa in self.recompensas.items():
            print(f"Agente {agente_id}: Recompensa total {recompensa}")
