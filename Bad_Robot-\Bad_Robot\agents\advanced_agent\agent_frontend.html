<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Autonomous Agent Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            height: 100vh;
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 20px;
            color: white;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff88;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.agent .message-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.agent .message-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-container {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-field {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            border-color: #4facfe;
        }

        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
        }

        .btn-send {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-voice {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-voice.recording {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            animation: pulse 1s infinite;
        }

        .btn:hover {
            transform: scale(1.1);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .panel h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 1.2rem;
        }

        .system-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4facfe;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .task-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #4facfe;
        }

        .task-item.completed {
            border-left-color: #00ff88;
            opacity: 0.7;
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-btn {
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .listening-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 20px;
            display: none;
            align-items: center;
            gap: 15px;
            z-index: 1000;
        }

        .listening-wave {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff416c;
            animation: wave 1s infinite;
        }

        @keyframes wave {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.5); }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px;
            }
            
            .message-content {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <div class="chat-header">
                <h1><span class="status-indicator"></span>Agente Autónomo Mejorado</h1>
                <p>Interfaz inteligente con reconocimiento de voz</p>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div>¡Hola! Soy tu agente autónomo mejorado. Puedo ayudarte con tareas de sistema, mantenimiento y comunicación. ¿En qué puedo asistirte hoy?</div>
                        <div class="message-time">Ahora</div>
                    </div>
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" class="input-field" id="messageInput" placeholder="Escribe tu mensaje o usa el micrófono...">
                <button class="btn btn-voice" id="voiceBtn" title="Reconocimiento de voz">🎤</button>
                <button class="btn btn-send" id="sendBtn" title="Enviar mensaje">📤</button>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="panel">
                <h3>📊 Estado del Sistema</h3>
                <div class="system-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="cpuUsage">--</div>
                        <div class="stat-label">CPU</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="memoryUsage">--</div>
                        <div class="stat-label">Memoria</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="diskUsage">--</div>
                        <div class="stat-label">Disco</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="networkStatus">--</div>
                        <div class="stat-label">Red</div>
                    </div>
                </div>
            </div>
            
            <div class="panel">
                <h3>📝 Tareas Activas</h3>
                <div id="tasksList">
                    <div class="task-item">
                        <strong>Sistema iniciado</strong><br>
                        <small>Agente listo para recibir comandos</small>
                    </div>
                </div>
            </div>
            
            <div class="panel">
                <h3>🔧 Controles</h3>
                <div class="controls">
                    <button class="control-btn" onclick="runSystemDiagnosis()">Diagnóstico del Sistema</button>
                    <button class="control-btn" onclick="checkInternetConnection()">Verificar Internet</button>
                    <button class="control-btn" onclick="repairSystem()">Reparar Sistema</button>
                    <button class="control-btn" onclick="clearChat()">Limpiar Chat</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="listening-indicator" id="listeningIndicator">
        <div class="listening-wave"></div>
        <span>Escuchando...</span>
    </div>

    <script>
        class AgentInterface {
            constructor() {
                this.recognition = null;
                this.synthesis = window.speechSynthesis;
                this.isListening = false;
                this.chatHistory = [];
                this.currentTasks = [];
                
                this.initSpeechRecognition();
                this.initEventListeners();
                this.startSystemMonitoring();
            }
            
            initSpeechRecognition() {
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();
                    
                    this.recognition.continuous = false;
                    this.recognition.interimResults = false;
                    this.recognition.lang = 'es-ES';
                    
                    this.recognition.onstart = () => {
                        this.isListening = true;
                        document.getElementById('voiceBtn').classList.add('recording');
                        document.getElementById('listeningIndicator').style.display = 'flex';
                    };
                    
                    this.recognition.onend = () => {
                        this.isListening = false;
                        document.getElementById('voiceBtn').classList.remove('recording');
                        document.getElementById('listeningIndicator').style.display = 'none';
                    };
                    
                    this.recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        document.getElementById('messageInput').value = transcript;
                        this.sendMessage(transcript, 'voice');
                    };
                    
                    this.recognition.onerror = (event) => {
                        console.error('Speech recognition error:', event.error);
                        this.addMessage('agent', 'Error en el reconocimiento de voz: ' + event.error, 'error');
                    };
                } else {
                    console.warn('Speech recognition not supported');
                }
            }
            
            initEventListeners() {
                document.getElementById('sendBtn').addEventListener('click', () => {
                    const input = document.getElementById('messageInput');
                    if (input.value.trim()) {
                        this.sendMessage(input.value, 'text');
                        input.value = '';
                    }
                });
                
                document.getElementById('messageInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const input = e.target;
                        if (input.value.trim()) {
                            this.sendMessage(input.value, 'text');
                            input.value = '';
                        }
                    }
                });
                
                document.getElementById('voiceBtn').addEventListener('click', () => {
                    if (this.recognition) {
                        if (this.isListening) {
                            this.recognition.stop();
                        } else {
                            this.recognition.start();
                        }
                    }
                });
            }
            
            addMessage(sender, content, type = 'normal') {
                const messagesContainer = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const avatar = sender === 'user' ? '👤' : '🤖';
                const now = new Date().toLocaleTimeString('es-ES', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                messageDiv.innerHTML = `
                    <div class="message-avatar">${avatar}</div>
                    <div class="message-content">
                        <div>${content}</div>
                        <div class="message-time">${now}</div>
                    </div>
                `;
                
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                // Store in history
                this.chatHistory.push({
                    sender,
                    content,
                    type,
                    timestamp: new Date()
                });
            }
            
            async sendMessage(message, inputType) {
                this.addMessage('user', message);
                
                try {
                    // Simulate backend call - replace with actual API endpoint
                    const response = await this.callAgentBackend(message, inputType);
                    
                    this.addMessage('agent', response.text);
                    
                    if (response.speak && this.synthesis) {
                        this.speak(response.text);
                    }
                    
                    if (response.actions) {
                        this.handleAgentActions(response.actions);
                    }
                    
                    if (response.tasks) {
                        this.updateTasks(response.tasks);
                    }
                    
                } catch (error) {
                    console.error('Error sending message:', error);
                    this.addMessage('agent', 'Error al procesar el mensaje. Intenta de nuevo.', 'error');
                }
            }
            
            async callAgentBackend(message, inputType) {
                try {
                    const response = await fetch('/api/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            input_type: inputType
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // Update tasks if provided
                    if (data.tasks) {
                        this.currentTasks = data.tasks;
                    }
                    
                    // Update system stats if provided
                    if (data.system_stats) {
                        this.updateSystemStatsFromBackend(data.system_stats);
                    }
                    
                    return data;
                    
                } catch (error) {
                    console.error('Backend connection error:', error);
                    
                    // Fallback to simulated responses if backend is unavailable
                    const lowerMessage = message.toLowerCase();
                    
                    if (lowerMessage.includes('hola') || lowerMessage.includes('buenos días')) {
                        return {
                            text: `¡Hola! Soy tu agente autónomo (modo offline). Reconocí tu mensaje por ${inputType === 'voice' ? 'voz' : 'texto'}. Backend no disponible.`,
                            speak: true,
                            actions: []
                        };
                    }
                    
                    return {
                        text: `Error de conexión con el backend. Mensaje recibido: "${message}". Funcionando en modo offline.`,
                        speak: true,
                        actions: [],
                        error: error.message
                    };
                }
            }
            
            speak(text) {
                if (this.synthesis) {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = 'es-ES';
                    utterance.rate = 0.9;
                    utterance.pitch = 1;
                    this.synthesis.speak(utterance);
                }
            }
            
            handleAgentActions(actions) {
                actions.forEach(action => {
                    switch (action) {
                        case 'update_tasks':
                            this.updateTasksDisplay();
                            break;
                        case 'system_repair':
                            this.simulateSystemRepair();
                            break;
                        case 'network_check':
                            this.updateNetworkStatus();
                            break;
                    }
                });
            }
            
            updateTasksDisplay() {
                const tasksList = document.getElementById('tasksList');
                tasksList.innerHTML = '';
                
                this.currentTasks.forEach(task => {
                    const taskDiv = document.createElement('div');
                    taskDiv.className = `task-item ${task.status === 'completed' ? 'completed' : ''}`;
                    taskDiv.innerHTML = `
                        <strong>${task.description}</strong><br>
                        <small>Estado: ${task.status} | ${task.created.toLocaleTimeString()}</small>
                    `;
                    tasksList.appendChild(taskDiv);
                });
            }
            
            simulateSystemRepair() {
                setTimeout(() => {
                    this.addMessage('agent', 'Reparación del sistema completada. Se encontraron y corrigieron 3 errores menores.');
                    this.updateSystemStats();
                }, 3000);
            }
            
            updateNetworkStatus() {
                document.getElementById('networkStatus').textContent = '✅';
            }
            
            startSystemMonitoring() {
                // Get real system stats from backend
                this.updateSystemStatsFromBackend();
                
                // Update every 10 seconds
                setInterval(() => {
                    this.updateSystemStatsFromBackend();
                }, 10000);
            }
            
            async updateSystemStatsFromBackend() {
                try {
                    const response = await fetch('/api/system_stats');
                    if (response.ok) {
                        const stats = await response.json();
                        
                        if (stats.cpu_usage !== undefined) {
                            document.getElementById('cpuUsage').textContent = Math.round(stats.cpu_usage) + '%';
                        }
                        if (stats.memory_usage !== undefined) {
                            document.getElementById('memoryUsage').textContent = Math.round(stats.memory_usage) + '%';
                        }
                        if (stats.disk_usage !== undefined) {
                            document.getElementById('diskUsage').textContent = Math.round(stats.disk_usage) + '%';
                        }
                        if (stats.network && stats.network.connection) {
                            const networkIcon = stats.network.connection === 'active' ? '🟢' : '🔴';
                            document.getElementById('networkStatus').textContent = networkIcon;
                        }
                    } else {
                        // Fallback to simulated stats
                        this.updateSystemStats();
                    }
                } catch (error) {
                    console.error('Error fetching system stats:', error);
                    // Fallback to simulated stats
                    this.updateSystemStats();
                }
            }
            
            updateSystemStats() {
                // Simulate system stats
                document.getElementById('cpuUsage').textContent = Math.floor(Math.random() * 30 + 10) + '%';
                document.getElementById('memoryUsage').textContent = Math.floor(Math.random() * 40 + 30) + '%';
                document.getElementById('diskUsage').textContent = Math.floor(Math.random() * 20 + 60) + '%';
                document.getElementById('networkStatus').textContent = '🌐';
            }
        }
        
        // Global functions for control buttons
        async function runSystemDiagnosis() {
            agent.addMessage('user', 'Ejecutar diagnóstico completo del sistema');
            
            try {
                const response = await fetch('/api/system_stats');
                const stats = await response.json();
                
                let diagnosisText = 'Diagnóstico del sistema completado:\n';
                diagnosisText += `CPU: ${Math.round(stats.cpu_usage || 0)}%\n`;
                diagnosisText += `Memoria: ${Math.round(stats.memory_usage || 0)}%\n`;
                diagnosisText += `Disco: ${Math.round(stats.disk_usage || 0)}%\n`;
                
                if (stats.issues && stats.issues.length > 0) {
                    diagnosisText += `\nProblemas detectados:\n${stats.issues.join('\n')}`;
                } else {
                    diagnosisText += '\nNo se detectaron problemas críticos.';
                }
                
                agent.addMessage('agent', diagnosisText);
                agent.speak('Diagnóstico del sistema completado');
                
            } catch (error) {
                agent.addMessage('agent', 'Error al ejecutar diagnóstico: ' + error.message);
            }
        }
        
        async function checkInternetConnection() {
            agent.addMessage('user', 'Verificar conexión a Internet');
            
            try {
                const response = await fetch('/api/network/check', { method: 'POST' });
                const result = await response.json();
                
                let statusText = 'Estado de la conexión:\n';
                if (result.result) {
                    statusText += `Conexión: ${result.result.connection || 'Desconocido'}\n`;
                    statusText += `DNS: ${result.result.dns || 'Desconocido'}`;
                    
                    if (result.result.repair_attempted) {
                        statusText += '\nSe intentó reparar la conexión automáticamente.';
                    }
                }
                
                agent.addMessage('agent', statusText);
                agent.speak('Verificación de internet completada');
                
            } catch (error) {
                agent.addMessage('agent', 'Error al verificar internet: ' + error.message);
            }
        }
        
        async function repairSystem() {
            agent.addMessage('user', 'Iniciar reparación del sistema');
            
            try {
                const response = await fetch('/api/system/repair', { method: 'POST' });
                const result = await response.json();
                
                agent.addMessage('agent', result.message || 'Reparación del sistema iniciada');
                agent.speak('Reparación del sistema en proceso');
                
                // Check progress after 5 seconds
                setTimeout(async () => {
                    agent.addMessage('agent', 'Reparación del sistema en progreso. Por favor espera...');
                }, 5000);
                
            } catch (error) {
                agent.addMessage('agent', 'Error al iniciar reparación: ' + error.message);
            }
        }
        
        async function clearChat() {
            try {
                await fetch('/api/chat/clear', { method: 'POST' });
            } catch (error) {
                console.log('Could not clear backend chat history:', error);
            }
            
            document.getElementById('chatMessages').innerHTML = `
                <div class="message agent">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div>Chat limpiado. ¿En qué puedo ayudarte?</div>
                        <div class="message-time">Ahora</div>
                    </div>
                </div>
            `;
            
            agent.chatHistory = [];
        }
        
        // Initialize the agent interface
        const agent = new AgentInterface();
        
        // Welcome message
        setTimeout(() => {
            agent.speak('Agente autónomo listo. Puedes hablarme o escribirme.');
        }, 1000);
    </script>
</body>
</html>