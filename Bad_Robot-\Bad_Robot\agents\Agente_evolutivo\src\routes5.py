from flask import Blueprint, request, jsonify, abort
from pydantic import ValidationError
from . import task_service
from .schemas import TaskCreate, TaskUpdate # Importamos nuestros esquemas

tasks_bp = Blueprint('tasks', __name__)

@tasks_bp.route('/tasks', methods=['GET'])
def get_tasks():
    tasks = task_service.get_all_tasks()
    return jsonify(tasks)

@tasks_bp.route('/tasks', methods=['POST'])
def add_task():
    try:
        # Validamos los datos de entrada con nuestro esquema TaskCreate
        task_data = TaskCreate.parse_obj(request.get_json())
    except ValidationError as e:
        # Si la validación falla, Pydantic nos da un error detallado
        return jsonify(e.errors()), 400

    new_task = task_service.create_task(
        title=task_data.title,
        completed=task_data.completed
    )
    return jsonify(new_task), 201

@tasks_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    return jsonify(task)

@tasks_bp.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task_route(task_id):
    # Primero, verificamos que la tarea exista
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    try:
        # Validamos los datos de entrada con TaskUpdate
        update_data = TaskUpdate.parse_obj(request.get_json())
    except ValidationError as e:
        return jsonify(e.errors()), 400
    
    # Usamos .dict(exclude_unset=True) para obtener solo los campos que el usuario envió
    update_payload = update_data.dict(exclude_unset=True)

    # Creamos el payload final fusionando datos existentes y nuevos
    final_data = {
        'title': update_payload.get('title', task['title']),
        'completed': update_payload.get('completed', task['completed'])
    }
    
    updated_task = task_service.update_task(task_id, final_data['title'], final_data['completed'])
    return jsonify(updated_task)


@tasks_bp.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task_route(task_id):
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    task_service.delete_task(task_id)
    return '', 204