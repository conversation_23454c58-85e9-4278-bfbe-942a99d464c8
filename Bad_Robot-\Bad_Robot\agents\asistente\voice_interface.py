import speech_recognition as sr
import pyttsx3
import asyncio
from queue import Queue
from threading import Thread

class VoiceInterface:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.engine = pyttsx3.init()
        self.command_queue = Queue()
        self.is_listening = False
        
        # Configurar voz
        voices = self.engine.getProperty('voices')
        self.engine.setProperty('voice', voices[0].id)  # Español si está disponible
        self.engine.setProperty('rate', 150)
    
    async def speak(self, text):
        def _speak():
            self.engine.say(text)
            self.engine.runAndWait()
        
        await asyncio.get_event_loop().run_in_executor(None, _speak)
    
    async def start_continuous_listening(self, callback):
        self.is_listening = True
        def _listen():
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
                while self.is_listening:
                    try:
                        audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)
                        text = self.recognizer.recognize_google(audio, language='es-ES')
                        self.command_queue.put(text)
                    except sr.WaitTimeoutError:
                        continue
                    except Exception as e:
                        print(f"Error en reconocimiento: {e}")
        
        Thread(target=_listen, daemon=True).start()
        
        while self.is_listening:
            if not self.command_queue.empty():
                command = self.command_queue.get()
                await callback(command)
            await asyncio.sleep(0.1)
    
    async def stop_listening(self):
        self.is_listening = False
    
    async def process_voice_command(self, command):
        # Aquí se integraría con el núcleo del asistente
        print(f"Comando de voz recibido: {command}")
        await self.speak(f"Procesando comando: {command}")
        return command