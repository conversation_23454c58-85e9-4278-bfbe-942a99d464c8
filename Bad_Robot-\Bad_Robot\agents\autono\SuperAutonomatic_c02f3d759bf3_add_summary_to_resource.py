"""add summary to resource

Revision ID: c02f3d759bf3
Revises: 1d54db311055
Create Date: 2023-06-27 05:07:29.016704

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c02f3d759bf3'
down_revision = 'c5c19944c90c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ##
    op.add_column('resources', sa.Column('summary', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('resources', 'summary')
    # ### end Alembic commands ###
