const userCommandEl = document.getElementById('user-command');
const sendCommandBtn = document.getElementById('send-command-btn');
const aiLogEl = document.getElementById('ai-log');
const feedbackControlsEl = document.getElementById('feedback-controls');
const rewardBtn = document.getElementById('reward-btn');
const penaltyBtn = document.getElementById('penalty-btn');
const filePathEl = document.getElementById('current-path');
const fileContentEl = document.getElementById('file-content');

const API_URL = 'http://127.0.0.1:5000';

// --- Manejadores de Eventos ---
sendCommandBtn.addEventListener('click', startTask);
rewardBtn.addEventListener('click', () => provideFeedback(10));
penaltyBtn.addEventListener('click', () => provideFeedback(-10));

// --- Lógica de la Aplicación ---
let currentTask = '';

async function startTask() {
    currentTask = userCommandEl.value;
    if (!currentTask) {
        alert("Por favor, introduce una orden.");
        return;
    }
    
    logMessage("🤖", `Nueva tarea recibida: "${currentTask}"`);
    logMessage("🧠", "Analizando la tarea y decidiendo la primera acción...");
    feedbackControlsEl.style.display = 'none';

    const response = await fetch(`${API_URL}/start_task`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command: currentTask })
    });
    const data = await response.json();
    handleAIResponse(data);
}

async function provideFeedback(reward) {
    logMessage("👨‍🏫", `Feedback enviado: ${reward > 0 ? 'Positivo' : 'Negativo'} (${reward})`);
    logMessage("🧠", "Aprendiendo del feedback y decidiendo la siguiente acción...");
    feedbackControlsEl.style.display = 'none';

    const response = await fetch(`${API_URL}/feedback_and_continue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reward: reward, command: currentTask }) // Enviamos el comando de nuevo para mantener el estado
    });
    const data = await response.json();
    handleAIResponse(data);
}

function handleAIResponse(data) {
    if (data.error) {
        logMessage("🔥", `Error: ${data.error}`);
        return;
    }

    logMessage("💡", `Decisión: ${data.decision_type}. Razón: ${data.reason}`);
    logMessage("⚡️", `Ejecutando Acción: ${data.action_name}(${data.action_params || ''})`);
    
    // Actualizar el visor de archivos
    filePathEl.textContent = data.workspace_status.current_path;
    fileContentEl.textContent = data.workspace_status.output;

    if (data.task_complete) {
        logMessage("✅", "La IA considera que la tarea ha finalizado.");
        feedbackControlsEl.style.display = 'none';
    } else {
        feedbackControlsEl.style.display = 'block';
    }
}

function logMessage(icon, text) {
    aiLogEl.innerHTML += `<div class="log-entry"><strong>${icon}</strong> ${text}</div>`;
    aiLogEl.scrollTop = aiLogEl.scrollHeight;
}