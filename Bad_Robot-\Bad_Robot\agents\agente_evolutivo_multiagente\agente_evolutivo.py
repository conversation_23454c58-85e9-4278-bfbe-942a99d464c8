import random
from datetime import datetime
import psutil

class AgenteEvolutivo:
    def __init__(self, nombre):
        self.nombre = nombre
        self.memoria = []
        self.modelo_prediccion = None  # Placeholder para modelo ML
        self.estado_sistema = self.diagnostico_sistema()

    def diagnostico_sistema(self):
        return {
            "cpu": psutil.cpu_percent(),
            "ram": psutil.virtual_memory().percent,
            "hora": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def percibir_entorno(self):
        # Simulando percepción sensorial
        return {
            "temperatura": random.uniform(18.0, 25.0),
            "movimiento": random.choice([True, False]),
            "sonido": random.choice(["silencio", "voz", "ruido"])
        }

    def predecir(self, datos):
        # Aquí iría el modelo real
        return {"accion_recomendada": "activar_calefaccion" if datos["temperatura"] < 20 else "esperar"}

    def actuar(self, accion):
        print(f"[{self.nombre}] Ejecutando acción: {accion}")

    def ciclo_principal(self):
        datos = self.percibir_entorno()
        decision = self.predecir(datos)
        self.actuar(decision["accion_recomendada"])

# Crear agente y ejecutar ciclo
agente = AgenteEvolutivo("AgenteCasaInteligente")
agente.ciclo_principal()