import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.optimizers import <PERSON>
from collections import deque
import random
import hashlib

class FileDQNAgent:
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.9  # El descuento es menos importante en tareas no secuenciales
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.model = self._build_model()

    def _build_model(self):
        model = Sequential([
            Dense(32, input_dim=self.state_size, activation='relu'),
            Dense(32, activation='relu'),
            Dense(self.action_size, activation='linear')
        ])
        model.compile(loss='mse', optimizer=Adam(learning_rate=self.learning_rate))
        return model

    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state, user_command):
        """Decide una acción usando una capa simbólica y luego la red neuronal."""
        
        # --- CAPA DE RAZONAMIENTO SIMBÓLICO/COGNITIVO (Basado en el comando del usuario) ---
        # Analiza el comando para una decisión rápida y lógica.
        cmd = user_command.lower()
        if "lista" in cmd or "muestra los archivos" in cmd or "ls" in cmd:
            return 0, "Simbólico", "El comando pide listar archivos." # Acción 0: LISTAR
        if "lee" in cmd or "contenido de" in cmd or "cat" in cmd:
            return 1, "Simbólico", "El comando pide leer un archivo." # Acción 1: LEER
        if "escribe" in cmd or "crea" in cmd or "guarda" in cmd:
            return 2, "Simbólico", "El comando pide escribir/crear un archivo." # Acción 2: ESCRIBIR
        
        # --- CAPA NEURONAL (DQN) con Epsilon-Greedy ---
        if np.random.rand() <= self.epsilon:
            action = random.randrange(self.action_size)
            return action, "Neuronal (Exploración)", "No hay regla simbólica clara, explorando."
        
        act_values = self.model.predict(state, verbose=0)
        action = np.argmax(act_values[0])
        return action, "Neuronal (Explotación)", f"Q-Values: {[float(f'{q:.2f}') for q in act_values[0]]}"

    def replay(self, batch_size):
        if len(self.memory) < batch_size:
            return
        
        minibatch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state, done in minibatch:
            target = reward
            if not done:
                target = (reward + self.gamma * np.amax(self.model.predict(next_state, verbose=0)[0]))
            
            target_f = self.model.predict(state, verbose=0)
            target_f[0][action] = target
            self.model.fit(state, target_f, epochs=1, verbose=0)
        
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            
    # --- PROCESAMIENTO DE ESTADO ---
    @staticmethod
    def get_state_representation(user_command, file_list, last_action_result):
        """Convierte el estado del mundo en un vector numérico para la IA."""
        # Usamos hashes para convertir texto en números consistentes
        cmd_hash = int(hashlib.sha256(user_command.encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)
        files_hash = int(hashlib.sha256("".join(file_list).encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)
        
        num_files = len(file_list) / 10.0 # Normalizar
        
        # Simplificamos el resultado de la última acción
        result_code = 1.0 if last_action_result else 0.0

        state_vector = [cmd_hash, files_hash, num_files, result_code]
        return np.reshape(state_vector, [1, len(state_vector)])