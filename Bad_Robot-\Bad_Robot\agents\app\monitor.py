from prometheus_client import start_http_server, Counter, Gauge, <PERSON>to<PERSON>
import time
from threading import Thread
import websockets
import asyncio

# Métricas Prometheus
METRICS = {
    "ejecuciones": Counter("agente_ejecuciones_total", "Total de ejecuciones", ["modulo"]),
    "errores": Counter("agente_errores_total", "Errores por tipo", ["modulo", "tipo"]),
    "tiempo_ejecucion": Histogram("agente_tiempo_ejecucion", "Duración por módulo", ["modulo"]),
    "retries": Gauge("agente_reintentos", "Reintentos actuales", ["modulo"])
}

class Monitor:
    def __init__(self, prometheus_port=9090, websocket_port=8765):
        self.prometheus_port = prometheus_port
        self.websocket_port = websocket_port

    def start_prometheus(self):
        """Inicia servidor de métricas."""
        start_http_server(self.prometheus_port)
        print(f"🔮 Prometheus escuchando en puerto {self.prometheus_port}")

    async def websocket_server(self):
        """Envía datos en tiempo real via WebSockets."""
        async with websockets.serve(self._handler, "0.0.0.0", self.websocket_port):
            print(f"📡 WebSocket escuchando en puerto {self.websocket_port}")
            await asyncio.Future()  # Ejecución infinita

    async def _handler(self, websocket):
        while True:
            # Ejemplo: enviar métricas cada segundo
            metricas = {
                "ejecuciones": METRICS["ejecuciones"]._metrics.copy(),
                "retries": METRICS["retries"]._metrics.copy()
            }
            await websocket.send(str(metricas))
            await asyncio.sleep(1)

    def run(self):
        """Inicia todos los servicios de monitorización."""
        Thread(target=self.start_prometheus, daemon=True).start()
        asyncio.run(self.websocket_server())