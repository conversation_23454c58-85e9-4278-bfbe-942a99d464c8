<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>IA Asistente de Código</title>
    <style>
        body { font-family: 'Consolas', 'Courier New', monospace; display: flex; height: 100vh; margin: 0; background: #282c34; color: #abb2bf; }
        .panel { padding: 15px; overflow-y: auto; }
        #left-panel { flex: 1; border-right: 1px solid #444; display: flex; flex-direction: column; }
        #right-panel { flex: 2; display: flex; flex-direction: column; }
        h1, h2 { color: #61afef; border-bottom: 1px solid #444; padding-bottom: 5px; }
        textarea, input { width: 95%; background: #21252b; color: #abb2bf; border: 1px solid #444; padding: 10px; margin-bottom: 10px; }
        button { background: #61afef; color: #282c34; border: none; padding: 10px 15px; cursor: pointer; margin: 5px; border-radius: 3px; }
        .reward-btn { background: #98c379; }
        .penalty-btn { background: #e06c75; }
        .execute-btn { background: #c678dd; }
        .danger-btn { background: #e06c75; }
        #ai-log { background: #21252b; flex-grow: 1; padding: 10px; border-radius: 5px; white-space: pre-wrap; font-size: 0.9em; }
        .code-view { background: #21252b; flex-grow: 1; padding: 10px; border-radius: 5px; white-space: pre-wrap; font-family: 'Consolas', 'Courier New', monospace; }
        #feedback-controls { display: none; }
        .log-entry { margin-bottom: 10px; border-bottom: 1px dashed #444; padding-bottom: 10px; }
        .error-log { color: #e06c75; }
    </style>
</head>
<body>
    <div id="left-panel" class="panel">
        <h1>Control de la IA</h1>
        <h2>1. Tarea a Realizar</h2>
        <textarea id="user-command" rows="4" placeholder="Ej: 'Crea un script de python que imprima hola mundo' o 'Depura el archivo buggy_code.py'"></textarea>
        <button id="send-command-btn">Iniciar Tarea</button>

        <h2>2. Log de Pensamiento de la IA</h2>
        <div id="ai-log">Esperando instrucciones...</div>

        <h2>3. Feedback y Control</h2>
        <div id="feedback-controls">
            <p>¿La IA realizó un paso útil?</p>
            <button id="reward-btn" class="reward-btn">Sí, buen paso (+10)</button>
            <button id="penalty-btn" class="penalty-btn">No, mal paso (-10)</button>
        </div>
        <div id="execution-confirmation" style="display: none;">
             <p class="error-log"><strong>¡Atención!</strong> La IA quiere ejecutar y modificar código.</p>
             <button id="confirm-execute-btn" class="danger-btn">Confirmar y Ejecutar</button>
             <button id="cancel-execute-btn">Cancelar</button>
        </div>
    </div>

    <div id="right-panel" class="panel">
        <h1>Workspace (`ai_workspace/`)</h1>
        <h2 id="current-filename">Ningún archivo seleccionado</h2>
        <div id="code-content" class="code-view">Contenido del código...</div>
        <h2>Salida de Ejecución</h2>
        <div id="execution-output" class="code-view">Salida del programa...</div>
    </div>

    <script src="script.js"></script>
</body>
</html>