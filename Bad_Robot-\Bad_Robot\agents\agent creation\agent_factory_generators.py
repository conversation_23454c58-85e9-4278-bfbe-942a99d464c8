# agent_factory_generators.py
import asyncio
from typing import List, Dict, Optional, AsyncGenerator, TypeVar, Generic
from dataclasses import dataclass, field
from enum import Enum, auto
import json
import logging
from functools import partial
import aiohttp
from aiohttp import ClientSession, ClientTimeout
import backoff

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('agent_factory.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AgentFactory")

T = TypeVar('T')

class MessageSource(Enum):
    """Enhanced message sources with additional types"""
    USER = auto()
    SYSTEM = auto()
    ASSISTANT = auto()
    SENSOR = auto()
    EXTERNAL_API = auto()
    DATABASE = auto()

class MessagePriority(Enum):
    """Message priority levels"""
    LOW = auto()
    NORMAL = auto()
    HIGH = auto()
    CRITICAL = auto()

class AgentType(Enum):
    """Types of agents that can be generated"""
    CHAT = auto()
    TASK = auto()
    DATA = auto()
    CONTROL = auto()
    MONITORING = auto()

@dataclass
class MessageMetadata:
    """Enhanced metadata container for messages"""
    conversation_id: Optional[str] = None
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: float = field(default_factory=lambda: time.time())
    context: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)

@dataclass
class UserMessage(Generic[T]):
    """Enhanced message class with typing support"""
    content: T
    source: MessageSource = MessageSource.USER
    metadata: MessageMetadata = field(default_factory=MessageMetadata)

    def to_dict(self) -> Dict[str, Any]:
        """Serialize message to dictionary"""
        return {
            "content": self.content,
            "source": self.source.name,
            "metadata": {
                "conversation_id": self.metadata.conversation_id,
                "priority": self.metadata.priority.name,
                "timestamp": self.metadata.timestamp,
                "context": self.metadata.context,
                "tags": self.metadata.tags
            }
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserMessage":
        """Deserialize message from dictionary"""
        return cls(
            content=data["content"],
            source=MessageSource[data["source"]],
            metadata=MessageMetadata(
                conversation_id=data["metadata"].get("conversation_id"),
                priority=MessagePriority[data["metadata"].get("priority", "NORMAL")],
                timestamp=data["metadata"].get("timestamp", time.time()),
                context=data["metadata"].get("context", {}),
                tags=data["metadata"].get("tags", [])
            )
        )

class AgentFactory:
    """Factory for creating and managing different types of agents"""
    
    def __init__(self, base_config: Dict[str, Any]):
        """
        Initialize the agent factory.
        
        Args:
            base_config: Configuration dictionary for the factory
        """
        self.config = base_config
        self.active_agents: Dict[str, 'AgentBase'] = {}
        self.session: Optional[ClientSession] = None
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize factory resources"""
        self.session = ClientSession(
            timeout=ClientTimeout(total=self.config.get("timeout", 30))
        
    async def create_agent(
        self,
        agent_type: AgentType,
        agent_id: str,
        specific_config: Optional[Dict[str, Any]] = None
    ) -> 'AgentBase':
        """Create a new agent of specified type"""
        async with self._lock:
            if agent_id in self.active_agents:
                raise ValueError(f"Agent {agent_id} already exists")
            
            config = {**self.config, **(specific_config or {})}
            
            if agent_type == AgentType.CHAT:
                agent = ChatAgent(agent_id, config, self.session)
            elif agent_type == AgentType.TASK:
                agent = TaskAgent(agent_id, config, self.session)
            elif agent_type == AgentType.DATA:
                agent = DataAgent(agent_id, config, self.session)
            elif agent_type == AgentType.CONTROL:
                agent = ControlAgent(agent_id, config, self.session)
            elif agent_type == AgentType.MONITORING:
                agent = MonitoringAgent(agent_id, config, self.session)
            else:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            await agent.initialize()
            self.active_agents[agent_id] = agent
            logger.info(f"Created {agent_type.name} agent {agent_id}")
            return agent
        
    async def get_agent(self, agent_id: str) -> Optional['AgentBase']:
        """Get an existing agent by ID"""
        return self.active_agents.get(agent_id)
    
    async def shutdown_agent(self, agent_id: str) -> bool:
        """Shutdown and remove an agent"""
        async with self._lock:
            agent = self.active_agents.pop(agent_id, None)
            if agent:
                await agent.shutdown()
                logger.info(f"Shutdown agent {agent_id}")
                return True
            return False
    
    async def shutdown_all(self):
        """Shutdown all agents"""
        async with self._lock:
            for agent_id in list(self.active_agents.keys()):
                await self.shutdown_agent(agent_id)
    
    async def close(self):
        """Clean up factory resources"""
        await self.shutdown_all()
        if self.session:
            await self.session.close()
            self.session = None

class AgentBase(ABC):
    """Base class for all agents with common functionality"""
    
    def __init__(
        self,
        agent_id: str,
        config: Dict[str, Any],
        session: Optional[ClientSession] = None
    ):
        self.agent_id = agent_id
        self.config = config
        self.session = session or ClientSession()
        self._running = True
        self._message_queue = asyncio.Queue()
        self._background_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """Initialize agent resources"""
        self._background_tasks.append(
            asyncio.create_task(self._process_messages()))
        logger.info(f"Agent {self.agent_id} initialized")
        
    async def shutdown(self):
        """Shutdown the agent"""
        self._running = False
        for task in self._background_tasks:
            task.cancel()
        await self.session.close()
        logger.info(f"Agent {self.agent_id} shutdown complete")
        
    async def receive_message(self, message: UserMessage):
        """Receive a message for processing"""
        await self._message_queue.put(message)
        
    async def _process_messages(self):
        """Background message processing"""
        while self._running:
            try:
                message = await self._message_queue.get()
                await self.handle_message(message)
                self._message_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error processing message in {self.agent_id}: {e}")
    
    @abstractmethod
    async def handle_message(self, message: UserMessage):
        """Handle an incoming message (to be implemented by subclasses)"""
        pass
    
    async def send_message(self, recipient: 'AgentBase', message: UserMessage):
        """Send a message to another agent"""
        await recipient.receive_message(message)
        
    async def generate_response(self, input_message: UserMessage) -> AsyncGenerator[str, None]:
        """Generate a streaming response (default implementation)"""
        yield f"Agent {self.agent_id} received: {input_message.content}"

class ChatAgent(AgentBase):
    """Enhanced chat agent with LLM integration"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any], session: ClientSession):
        super().__init__(agent_id, config, session)
        self.llm_client = OllamaChatCompletionClient(
            model=config.get("model", "llama3.2"),
            base_url=config.get("llama_base_url", "http://localhost:11434"),
            timeout=config.get("timeout", 30)
        )
        self.conversation_history: List[UserMessage] = []
        
    async def handle_message(self, message: UserMessage):
        """Handle chat messages with LLM integration"""
        self.conversation_history.append(message)
        
        async for chunk in self.llm_client.stream_create(self.conversation_history):
            response_msg = UserMessage(
                content=chunk,
                source=MessageSource.ASSISTANT,
                metadata=MessageMetadata(
                    conversation_id=message.metadata.conversation_id,
                    priority=message.metadata.priority
                )
            )
            # In a real implementation, we'd send this to the appropriate recipient
            logger.info(f"Generated response: {chunk}")
            
    async def generate_response(self, input_message: UserMessage) -> AsyncGenerator[str, None]:
        """Generate streaming response from LLM"""
        self.conversation_history.append(input_message)
        
        async for chunk in self.llm_client.stream_create(self.conversation_history):
            yield chunk

class TaskAgent(AgentBase):
    """Agent for handling and delegating tasks"""
    
    async def handle_message(self, message: UserMessage):
        """Handle task-related messages"""
        task = json.loads(message.content)
        logger.info(f"Processing task: {task.get('name')}")
        # Task processing logic would go here

class OllamaChatCompletionClient:
    """Enhanced Ollama client with streaming and retry support"""
    
    def __init__(
        self,
        model: str,
        base_url: str = "http://localhost:11434",
        timeout: int = 30
    ):
        self.model = model
        self.base_url = base_url
        self.timeout = timeout
        self.session: Optional[ClientSession] = None
        
    async def initialize(self):
        """Initialize client resources"""
        self.session = ClientSession(
            timeout=ClientTimeout(total=self.timeout))
        
    @backoff.on_exception(
        backoff.expo,
        (aiohttp.ClientError, asyncio.TimeoutError),
        max_tries=3
    )
    async def create(self, messages: List[UserMessage]) -> str:
        """Create chat completion with retry logic"""
        if not self.session:
            await self.initialize()
            
        url = f"{self.base_url}/api/chat"
        payload = {
            "model": self.model,
            "messages": [msg.to_dict() for msg in messages]
        }
        
        async with self.session.post(url, json=payload) as resp:
            resp.raise_for_status()
            data = await resp.json()
            return data["message"]["content"]
        
    async def stream_create(self, messages: List[UserMessage]) -> AsyncGenerator[str, None]:
        """Stream chat completion response"""
        if not self.session:
            await self.initialize()
            
        url = f"{self.base_url}/api/chat"
        payload = {
            "model": self.model,
            "messages": [msg.to_dict() for msg in messages],
            "stream": True
        }
        
        async with self.session.post(url, json=payload) as resp:
            resp.raise_for_status()
            
            async for line in resp.content:
                if line:
                    data = json.loads(line)
                    yield data["message"]["content"]
                    
    async def close(self):
        """Clean up resources"""
        if self.session:
            await self.session.close()
            self.session = None

async def main():
    """Demonstration of the agent factory system"""
    # Configuration
    config = {
        "timeout": 45,
        "llama_base_url": "http://localhost:11434",
        "default_model": "llama3.2"
    }
    
    # Initialize factory
    factory = AgentFactory(config)
    await factory.initialize()
    
    try:
        # Create a chat agent
        chat_agent = await factory.create_agent(
            AgentType.CHAT,
            "chat_agent_1",
            {"model": "llama3.2"}
        )
        
        # Create a message
        message = UserMessage(
            content="What is the capital of France?",
            source=MessageSource.USER,
            metadata=MessageMetadata(
                conversation_id="conv_123",
                priority=MessagePriority.NORMAL
            )
        )
        
        # Process the message
        await chat_agent.receive_message(message)
        
        # Demonstrate streaming response
        print("Streaming response:")
        async for chunk in chat_agent.generate_response(message):
            print(chunk, end="", flush=True)
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        await factory.close()

if __name__ == "__main__":
    asyncio.run(main())