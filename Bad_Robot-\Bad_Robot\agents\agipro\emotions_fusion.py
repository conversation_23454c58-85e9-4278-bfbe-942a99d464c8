# emotion_fusion.py
import numpy as np
from collections import deque

class EmotionFusionEngine:
    def __init__(self):
        # Pesos iniciales para cada modalidad
        self.modality_weights = {
            'facial': 0.5,
            'vocal': 0.3,
            'textual': 0.2
        }
        
        # Historial para ajuste dinámico
        self.history = deque(maxlen=100)
        self.confidence_threshold = 0.6
        self.minimum_modality_weight = 0.1
        
    def fuse_emotions(self, modality_results: dict) -> dict:
        """
        Combina resultados de múltiples modalidades usando pesos adaptativos
        
        Args:
            modality_results: {
                'facial': {'happy': 0.7, 'sad': 0.3},
                'vocal': {'happy': 0.4, 'angry': 0.6},
                'textual': {'happy': 0.8}
            }
            
        Returns:
            dict: Emociones combinadas con pesos
        """
        # Validar entradas
        valid_modalities = {k: v for k, v in modality_results.items() 
                          if v and any(x >= self.confidence_threshold for x in v.values())}
        
        if not valid_modalities:
            return {'neutral': 1.0}
        
        # Ajustar pesos basado en calidad de señales
        self._adjust_weights_based_on_confidence(modality_results)
        
        # Combinar resultados
        combined = {}
        for modality, emotions in valid_modalities.items():
            weight = self.modality_weights[modality]
            for emotion, score in emotions.items():
                if score >= self.confidence_threshold:
                    combined[emotion] = combined.get(emotion, 0) + score * weight
        
        # Normalizar
        if combined:
            total = sum(combined.values())
            return {k: v/total for k, v in combined.items()}
        return {'neutral': 1.0}
    
    def _adjust_weights_based_on_confidence(self, results: dict):
        """Ajusta pesos dinámicamente basado en confianza de cada modalidad"""
        # Calcular confianza promedio por modalidad
        confidences = {}
        for modality, emotions in results.items():
            if emotions:
                confidences[modality] = sum(emotions.values()) / len(emotions)
            else:
                confidences[modality] = 0.0
        
        # Actualizar pesos (sin caer debajo del mínimo)
        total_conf = sum(confidences.values())
        if total_conf > 0:
            for modality in self.modality_weights:
                new_weight = max(self.minimum_modality_weight, 
                                confidences.get(modality, 0) / total_conf)
                self.modality_weights[modality] = (
                    0.8 * self.modality_weights[modality] + 0.2 * new_weight
                )
        
        # Normalizar pesos
        total_weights = sum(self.modality_weights.values())
        self.modality_weights = {k: v/total_weights 
                               for k, v in self.modality_weights.items()}
        
    def update_based_on_feedback(self, correct_emotion: str, detected_emotions: dict):
        """Aprende de feedback explícito del usuario"""
        # Registrar en historial
        self.history.append((detected_emotions, correct_emotion))
        
        # Recalibrar umbrales si hay suficiente historial
        if len(self.history) >= 20:
            self._recalibrate_thresholds()
    
    def _recalibrate_thresholds(self):
        """Ajusta umbrales basado en historial"""
        # Análisis de falsos positivos/negativos
        confusion_matrix = {}
        for detected, actual in self.history:
            for emotion, score in detected.items():
                if emotion not in confusion_matrix:
                    confusion_matrix[emotion] = {'tp': 0, 'fp': 0, 'fn': 0}
                
                if emotion == actual:
                    if score >= self.confidence_threshold:
                        confusion_matrix[emotion]['tp'] += 1
                    else:
                        confusion_matrix[emotion]['fn'] += 1
                else:
                    if score >= self.confidence_threshold:
                        confusion_matrix[emotion]['fp'] += 1
        
        # Ajustar umbrales para optimizar F1-score
        for emotion, counts in confusion_matrix.items():
            precision = counts['tp'] / (counts['tp'] + counts['fp'] + 1e-9)
            recall = counts['tp'] / (counts['tp'] + counts['fn'] + 1e-9)
            
            # Aumentar umbral si muchos FP, disminuir si muchos FN
            current_thresh = self.confidence_threshold
            if precision < 0.6:  # Muchos falsos positivos
                new_thresh = min(0.9, current_thresh + 0.05)
            elif recall < 0.5:  # Muchos falsos negativos
                new_thresh = max(0.1, current_thresh - 0.03)
            else:
                continue
            
            print(f"Ajustando umbral para {emotion}: {current_thresh:.2f} → {new_thresh:.2f}")
            self.confidence_threshold = new_thresh