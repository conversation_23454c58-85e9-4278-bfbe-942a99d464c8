import ast
import esprima
import astor
import json
from typing import Dict, List
from pathlib import Path

class AdvancedCodeManager:
    def __init__(self):
        self.analyzer = CodeAnalyzer()
    
    async def refactor_code(self, file_path: str, patterns: Dict):
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.py':
            return await self._refactor_python(file_path, patterns)
        elif file_ext == '.js':
            return await self._refactor_javascript(file_path, patterns)
        else:
            return f"Tipo de archivo no soportado: {file_ext}"
    
    async def _refactor_python(self, file_path: str, patterns: Dict):
        with open(file_path, 'r') as f:
            code = f.read()
        
        tree = ast.parse(code)
        
        # Refactorización basada en patrones
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if any(p in node.name.lower() for p in patterns.get('bad_names', [])):
                    node.name = self._generate_better_name(node.name)
            
            if isinstance(node, ast.For) and patterns.get('replace_for_with_comprehension', False):
                if self._is_simple_for_loop(node):
                    return await self._convert_for_to_comprehension(node)
        
        refactored_code = astor.to_source(tree)
        
        with open(file_path, 'w') as f:
            f.write(refactored_code)
        
        return f"Código refactorizado en {file_path}"
    
    async def _refactor_javascript(self, file_path: str, patterns: Dict):
        with open(file_path, 'r') as f:
            code = f.read()
        
        parsed = esprima.parseScript(code, {'range': True})
        
        # Implementar lógica de refactorización para JS
        # (ejemplo simplificado)
        
        # Guardar cambios
        with open(file_path, 'w') as f:
            f.write(code)
        
        return f"Código JavaScript refactorizado en {file_path}"
    
    async def optimize_code(self, file_path: str):
        analysis = await self.analyzer.analyze(file_path)
        
        optimizations = {
            'python': self._optimize_python,
            'javascript': self._optimize_javascript
        }
        
        file_ext = Path(file_path).suffix.lower()[1:]
        if file_ext in optimizations:
            return await optimizations[file_ext](file_path, analysis)
        return "Tipo de archivo no soportado para optimización"
    
    async def _optimize_python(self, file_path: str, analysis: Dict):
        # Implementar optimizaciones específicas para Python
        optimizations = []
        
        if analysis['performance_issues']['nested_loops'] > 3:
            optimizations.append("Demasiados bucles anidados - considerar vectorización con NumPy")
        
        if analysis['code_smells']['long_methods'] > 5:
            optimizations.append("Métodos demasiado largos - dividir en funciones más pequeñas")
        
        if optimizations:
            with open(file_path + '.optimization_suggestions', 'w') as f:
                f.write("\n".join(optimizations))
            return f"Sugerencias de optimización generadas para {file_path}"
        return f"No se encontraron oportunidades de optimización obvias en {file_path}"