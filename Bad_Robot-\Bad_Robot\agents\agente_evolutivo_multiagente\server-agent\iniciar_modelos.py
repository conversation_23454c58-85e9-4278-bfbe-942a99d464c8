import pickle
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import IsolationForest
import numpy as np

# Modelo temperatura
model_temp = LinearRegression()
X_dummy = np.array([[0]])
y_dummy = np.array([20])
model_temp.fit(X_dummy, y_dummy)
pickle.dump(model_temp, open("server_agent/model_temp.pkl", "wb"))

# Modelo anomalías
model_anomaly = IsolationForest(contamination=0.1)
model_anomaly.fit(np.random.rand(10, 3))
pickle.dump(model_anomaly, open("server_agent/model_anomaly.pkl", "wb"))

print("Modelos iniciales creados: model_temp.pkl y model_anomaly.pkl")