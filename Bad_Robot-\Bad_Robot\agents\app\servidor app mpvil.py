# En el servidor Flask
@socketio.on('process_message')
def handle_agi_message(data):
    from symbolic_reasoning import SymbolicReasoner
    from dqn_agent import DQNAgent
    
    # 1. Procesamiento simbólico
    reasoner = SymbolicReasoner()
    symbolic_result = asyncio.run(reasoner.process(data))
    
    # 2. Procesamiento DQN
    dqn_agent = DQNAgent()
    state = preprocess_message(data['message'])
    dqn_action = dqn_agent.act(state)
    
    # 3. Integrar respuestas
    response = {
        'symbolic': symbolic_result,
        'dqn_action': dqn_action,
        'combined_response': f"{symbolic_result.get('response')} (Acción recomendada: {dqn_action})"
    }
    
    emit('agi_response', response)