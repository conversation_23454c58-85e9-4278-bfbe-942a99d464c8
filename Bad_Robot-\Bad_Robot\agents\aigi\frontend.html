<!DOCTYPE html>
<html>
<head>
    <title>AGI Interface</title>
    <style>
        #chat-container { height: 400px; border: 1px solid #ccc; overflow-y: scroll; }
        #camera-feed { width: 320px; height: 240px; }
    </style>
</head>
<body>
    <video id="camera-feed"></video>
    <div id="chat-container"></div>
    <input id="user-input" type="text">
    <button id="send-button">Enviar</button>
    <button id="start-listening">Escuchar</button>

    <script>
        // Acceso a cámara
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => document.getElementById('camera-feed').srcObject = stream);
        
        // Conexión WebSocket
        const socket = new WebSocket('ws://localhost:5000/ws');
        
        // Manejo de chat
        document.getElementById('send-button').addEventListener('click', () => {
            const message = document.getElementById('user-input').value;
            socket.send(JSON.stringify({ type: 'text', content: message }));
        });
    </script>
</body>
</html>