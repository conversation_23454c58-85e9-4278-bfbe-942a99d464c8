from flask import Blueprint, request, jsonify

tasks_bp = Blueprint('tasks', __name__)

# Base de datos en memoria (simple lista de diccionarios)
tasks = []
task_id_counter = 1

@tasks_bp.route('/tasks', methods=['GET'])
def get_tasks():
    return jsonify(tasks)

@tasks_bp.route('/tasks', methods=['POST'])
def add_task():
    global task_id_counter
    data = request.get_json()
    new_task = {
        'id': task_id_counter,
        'title': data['title'],
        'completed': False
    }
    tasks.append(new_task)
    task_id_counter += 1
    return jsonify(new_task), 201