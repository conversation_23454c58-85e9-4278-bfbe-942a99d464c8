import cv2
import mediapipe as mp
from typing import List, Tuple, Optional
import numpy as np

class MediaPipeDetector:
    def __init__(self, 
                 static_image_mode: bool = False,
                 max_num_hands: int = 2,
                 model_complexity: int = 1,
                 min_detection_confidence: float = 0.5,
                 min_tracking_confidence: float = 0.5):
        """
        Inicializa el detector de MediaPipe para manos y postura.
        
        Args:
            static_image_mode: Si True, el detector detecta en cada imagen (más lento).
            max_num_hands: Número máximo de manos a detectar.
            model_complexity: Complejidad del modelo de postura (0, 1 o 2).
            min_detection_confidence: Confianza mínima para la detección inicial.
            min_tracking_confidence: Confianza mínima para el seguimiento continuo.
        """
        # Inicializar soluciones de MediaPipe
        self.mp_hands = mp.solutions.hands
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # Configurar detectores
        self.hands = self.mp_hands.Hands(
            static_image_mode=static_image_mode,
            max_num_hands=max_num_hands,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        self.pose = self.mp_pose.Pose(
            static_image_mode=static_image_mode,
            model_complexity=model_complexity,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
    
    def process_image(self, image: np.ndarray) -> Tuple[Optional[List], Optional[List]]:
        """
        Procesa una imagen y detecta manos y postura.
        
        Args:
            image: Imagen BGR (formato OpenCV) para procesar.
            
        Returns:
            Tuple con (resultados_manos, resultados_postura)
        """
        # Convertir a RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Procesar con MediaPipe
        hand_results = self.hands.process(image_rgb)
        pose_results = self.pose.process(image_rgb)
        
        return hand_results, pose_results
    
    def draw_landmarks(self, image: np.ndarray, 
                      hand_results, pose_results) -> np.ndarray:
        """
        Dibuja los landmarks detectados en la imagen.
        
        Args:
            image: Imagen BGR donde dibujar.
            hand_results: Resultados de detección de manos.
            pose_results: Resultados de detección de postura.
            
        Returns:
            Imagen con los landmarks dibujados.
        """
        # Dibujar landmarks de manos
        if hand_results.multi_hand_landmarks:
            for hand_landmarks in hand_results.multi_hand_landmarks:
                self.mp_drawing.draw_landmarks(
                    image,
                    hand_landmarks,
                    self.mp_hands.HAND_CONNECTIONS,
                    self.mp_drawing_styles.get_default_hand_landmarks_style(),
                    self.mp_drawing_styles.get_default_hand_connections_style()
                )
        
        # Dibujar landmarks de postura
        if pose_results.pose_landmarks:
            self.mp_drawing.draw_landmarks(
                image,
                pose_results.pose_landmarks,
                self.mp_pose.POSE_CONNECTIONS,
                landmark_drawing_spec=self.mp_drawing_styles.get_default_pose_landmarks_style()
            )
        
        return image
    
    def get_hand_landmarks_array(self, hand_results, hand_index: int = 0) -> Optional[np.ndarray]:
        """
        Obtiene los landmarks de una mano como array numpy.
        
        Args:
            hand_results: Resultados de detección de manos.
            hand_index: Índice de la mano a obtener (0 para la primera detectada).
            
        Returns:
            Array numpy con shape (21, 3) con landmarks normalizados (x, y, z),
            o None si no se detectó la mano.
        """
        if not hand_results.multi_hand_landmarks:
            return None
        
        if hand_index >= len(hand_results.multi_hand_landmarks):
            return None
        
        landmarks = hand_results.multi_hand_landmarks[hand_index]
        return np.array([[lm.x, lm.y, lm.z] for lm in landmarks.landmark])
    
    def get_pose_landmarks_array(self, pose_results) -> Optional[np.ndarray]:
        """
        Obtiene los landmarks de postura como array numpy.
        
        Args:
            pose_results: Resultados de detección de postura.
            
        Returns:
            Array numpy con shape (33, 4) con landmarks (x, y, z, visibility),
            o None si no se detectó postura.
        """
        if not pose_results.pose_landmarks:
            return None
        
        return np.array([[lm.x, lm.y, lm.z, lm.visibility] 
                         for lm in pose_results.pose_landmarks.landmark])
    
    def release(self):
        """Libera los recursos de los detectores."""
        self.hands.close()
        self.pose.close()


# Ejemplo de uso
if __name__ == "__main__":
    # Inicializar detector
    detector = MediaPipeDetector()
    
    # Inicializar cámara
    cap = cv2.VideoCapture(0)
    
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # Procesar frame
        hand_results, pose_results = detector.process_image(frame)
        
        # Dibujar resultados
        frame = detector.draw_landmarks(frame, hand_results, pose_results)
        
        # Mostrar frame
        cv2.imshow('MediaPipe Detection', frame)
        
        # Salir con 'q'
        if cv2.waitKey(10) & 0xFF == ord('q'):
            break
    
    # Liberar recursos
    detector.release()
    cap.release()
    cv2.destroyAllWindows()