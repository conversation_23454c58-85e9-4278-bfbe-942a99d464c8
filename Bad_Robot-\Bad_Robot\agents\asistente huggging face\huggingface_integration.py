from transformers import pipeline, AutoModelForSeq2SeqLM, AutoTokenizer
import torch
from typing import Dict, List
import os

class HuggingFaceIntegration:
    def __init__(self):
        self.models = {
            'codegen': None,
            'codet5': None,
            'codebert': None
        }
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    async def load_model(self, model_name: str):
        if model_name not in self.models:
            return f"Modelo {model_name} no disponible"
        
        if self.models[model_name] is None:
            try:
                if model_name == 'codegen':
                    self.models[model_name] = pipeline(
                        'text-generation',
                        model='Salesforce/codegen-350M-mono',
                        device=self.device
                    )
                elif model_name == 'codet5':
                    tokenizer = AutoTokenizer.from_pretrained('Salesforce/codet5-base')
                    model = AutoModelForSeq2SeqLM.from_pretrained(
                        'Salesforce/codet5-base'
                    ).to(self.device)
                    self.models[model_name] = (model, tokenizer)
                elif model_name == 'codebert':
                    self.models[model_name] = pipeline(
                        'fill-mask',
                        model='microsoft/codebert-base',
                        device=self.device
                    )
                return f"Modelo {model_name} cargado correctamente"
            except Exception as e:
                return f"Error al cargar {model_name}: {str(e)}"
        return f"Modelo {model_name} ya estaba cargado"
    
    async def generate_code(self, prompt: str, lang: str = 'python'):
        if 'codegen' not in self.models or self.models['codegen'] is None:
            await self.load_model('codegen')
        
        generated = self.models['codegen'](
            prompt,
            max_length=200,
            num_return_sequences=1,
            temperature=0.7,
            pad_token_id=50256
        )
        
        return generated[0]['generated_text']
    
    async def explain_code(self, code: str):
        if 'codet5' not in self.models or self.models['codet5'] is None:
            await self.load_model('codet5')
        
        model, tokenizer = self.models['codet5']
        input_text = f"Explain this code: {code}"
        input_ids = tokenizer.encode(input_text, return_tensors='pt').to(self.device)
        
        outputs = model.generate(
            input_ids,
            max_length=200,
            num_beams=4,
            early_stopping=True
        )
        
        explanation = tokenizer.decode(outputs[0], skip_special_tokens=True)
        return explanation
    
    async def detect_vulnerabilities(self, code: str):
        if 'codebert' not in self.models or self.models['codebert'] is None:
            await self.load_model('codebert')
        
        # Análisis simplificado - en producción usar un modelo especializado
        masked_code = code.replace('=', '<mask>')
        predictions = self.models['codebert'](masked_code)
        
        vulnerabilities = []
        for pred in predictions:
            if any(bad in pred['token_str'] for bad in ['exec', 'eval', 'system']):
                vulnerabilities.append(f"Posible inyección en: {pred['sequence']}")
        
        return vulnerabilities if vulnerabilities else "No se detectaron vulnerabilidades obvias"