from flask import Blueprint, request, jsonify, abort
from . import task_service # Importamos nuestro nuevo servicio

tasks_bp = Blueprint('tasks', __name__)

@tasks_bp.route('/tasks', methods=['GET'])
def get_tasks():
    tasks = task_service.get_all_tasks()
    return jsonify(tasks)

@tasks_bp.route('/tasks', methods=['POST'])
def add_task():
    data = request.get_json()
    if not data or 'title' not in data:
        abort(400, description="El campo 'title' es requerido.")
    
    new_task = task_service.create_task(
        title=data['title'],
        completed=data.get('completed', False)
    )
    return jsonify(new_task), 201

@tasks_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    return jsonify(task)

@tasks_bp.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task_route(task_id):
    # Primero, verificamos que la tarea exista
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    data = request.get_json()
    # Usamos los valores existentes como fallback
    updated_title = data.get('title', task['title'])
    updated_completed = data.get('completed', task['completed'])
    
    updated_task = task_service.update_task(task_id, updated_title, updated_completed)
    return jsonify(updated_task)

@tasks_bp.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task_route(task_id):
    task = task_service.get_task_by_id(task_id)
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    
    task_service.delete_task(task_id)
    return '', 204