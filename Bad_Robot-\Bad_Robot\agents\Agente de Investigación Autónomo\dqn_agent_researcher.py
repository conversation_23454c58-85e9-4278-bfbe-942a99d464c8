import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.optimizers import <PERSON>
from collections import deque
import random

class ResearchDQNAgent:
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.95
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.model = self._build_model()

    def _build_model(self):
        model = Sequential([
            Dense(24, input_dim=self.state_size, activation='relu'),
            <PERSON><PERSON>(24, activation='relu'),
            Dense(self.action_size, activation='linear')
        ])
        model.compile(loss='mse', optimizer=Adam(learning_rate=0.001))
        return model

    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state, command):
        # --- CAPA SIMBÓLICA/COGNITIVA para un flujo de trabajo lógico ---
        # state es un vector [has_text, has_image, text_analyzed, image_analyzed]
        has_text, has_image, text_analyzed, image_analyzed = state[0] > 0, state[1] > 0, state[2] > 0, state[3] > 0
        cmd = command.lower()

        if "imagen" in cmd or "foto" in cmd or "dibuja" in cmd:
            if not has_image: return 1, "Simbólico", "Comando pide imagen, buscando imagen." # BUSCAR_IMAGEN
        
        if not has_text: return 0, "Simbólico", "Inicio de investigación, buscando texto." # BUSCAR_TEXTO

        if has_text and not text_analyzed: return 2, "Simbólico", "Texto encontrado, analizando patrones." # ANALIZAR_TEXTO
        
        if has_image and not image_analyzed: return 3, "Simbólico", "Imagen encontrada, analizando patrones." # ANALIZAR_IMAGEN

        if text_analyzed and (not ("imagen" in cmd) or image_analyzed):
            return 4, "Simbólico", "Análisis completo, sintetizando hallazgos." # SINTETIZAR

        # --- CAPA NEURONAL ---
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size), "Neuronal (Exploración)", "Sin regla clara, explorando."
        
        act_values = self.model.predict(np.reshape(state, [1, self.state_size]), verbose=0)
        return np.argmax(act_values[0]), "Neuronal (Explotación)", "Usando experiencia para decidir."

    def replay(self, batch_size):
        if len(self.memory) < batch_size: return
        minibatch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state, done in minibatch:
            target = reward
            if not done:
                target = reward + self.gamma * np.amax(self.model.predict(np.reshape(next_state, [1, self.state_size]), verbose=0)[0])
            
            target_f = self.model.predict(np.reshape(state, [1, self.state_size]), verbose=0)
            target_f[0][action] = target
            self.model.fit(np.reshape(state, [1, self.state_size]), target_f, epochs=1, verbose=0)
        
        if self.epsilon > self.epsilon_min: self.epsilon *= self.epsilon_decay