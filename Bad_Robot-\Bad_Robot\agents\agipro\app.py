import os
from dotenv import load_dotenv
from flask import Flask, render_template
from flask_socketio import SocketIO
import openai
import face_recognition
import pyttsx3
import speech_recognition as sr
from emotions import EmotionRecognizer

# Inicializa el reconocedor de emociones
emotion_recognizer = EmotionRecognizer()
# En app.py
emotion_recognizer = EmotionRecognizer()
avatar = EmotionalAvatar()  # Opcional para versión de escritorio

@socketio.on('voice_message')
def handle_voice(data):
    emotion_from_voice = emotion_recognizer.analyze_voice(data['audio'])
    emotion_recognizer.save_emotion(data['user_id'], emotion_from_voice, 'voice')
    
    # Combinar con otras fuentes de emoción
    combined_emotion = emotion_recognizer.combine_emotion_sources(data['user_id'])
    avatar.update_emotion(combined_emotion['dominant'])
    # Renderizar la página con la nueva emoción
# Dentro de tu función de manejo de mensajes:
@socketio.on('message')
def handle_message(data):
    try:
        # Detectar emoción del texto
        text_emotion = emotion_recognizer.detect_emotion_from_text(data['message'])
        
        # Obtener respuesta emocional adecuada
        emotional_response = emotion_recognizer.get_emotional_response(text_emotion)
        
        # Generar respuesta con el modelo de lenguaje
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": f"Eres un asistente AGI. Usuario parece {text_emotion}. {emotional_response}"},
                {"role": "user", "content": data['message']}
            ]
        )
        
        reply = response.choices[0].message['content']
        
        # Opcional: convertir respuesta a voz
        engine.say(reply)
        engine.runAndWait()
        
        socketio.emit('response', {
            'text': reply,
            'emotion': text_emotion,
            'emotion_icon': emotion_recognizer.get_emotion_face_icon(text_emotion)
        })
        
    except Exception as e:
        socketio.emit('error', {'text': str(e)})

known_face_encodings = []
known_face_names = []

def load_known_faces():
    for filename in os.listdir('known_faces'):
        image = face_recognition.load_image_file(f"known_faces/{filename}")
        encoding = face_recognition.face_encodings(image)[0]
        known_face_encodings.append(encoding)
        known_face_names.append(filename.split('.')[0])

load_known_faces(),known_face_encodings = []
known_face_names = []

def load_known_faces():
    for filename in os.listdir('known_faces'):
        image = face_recognition.load_image_file(f"known_faces/{filename}")
        encoding = face_recognition.face_encodings(image)[0]
        known_face_encodings.append(encoding)
        known_face_names.append(filename.split('.')[0])

load_known_faces()
app = Flask(__name__)
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
socketio = SocketIO(app)
openai.api_key = os.getenv('OPENAI_API_KEY')

# Configuración de voz
engine = pyttsx3.init()
voices = engine.getProperty('voices')
engine.setProperty('voice', voices[0].id)  # Selecciona voz en español si disponible

@app.route('/')
def home():
    return render_template('index.html')

@socketio.on('message')
def handle_message(data):
    try:
        response = openai.ChatCompletion.create( # type: ignore
            model="gpt-4",
            messages=[{"role": "system", "content": "Eres un asistente AGI inteligente."},
                     {"role": "user", "content": data['message']}]
        )
        reply = response.choices[0].message['content']
        
        # Opcional: convertir respuesta a voz
        engine.say(reply)
        engine.runAndWait()
        
        socketio.emit('response', {'text': reply})
    except Exception as e:
        socketio.emit('error', {'text': str(e)})

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)