### main.py - Entrada principal con detección de entorno y selección dinámica

import platform
import os
import psutil
from entornos import ecosistema, financiero, industrial, espacial
from agentes.base import Agente
from config import seleccionar_entorno


def detectar_entorno_maquina():
    info = {
        "sistema": platform.system(),
        "nucleo": platform.release(),
        "cpu_cores": psutil.cpu_count(logical=False),
        "ram_gb": round(psutil.virtual_memory().total / (1024**3), 2),
    }
    return info


def main():
    info_maquina = detectar_entorno_maquina()
    entorno_id = seleccionar_entorno(info_maquina)

    if entorno_id == "ecosistema":
        entorno = ecosistema.Ecosistema()
    elif entorno_id == "financiero":
        entorno = financiero.MercadoFinanciero()
    elif entorno_id == "industrial":
        entorno = industrial.EntornoIndustrial()
    elif entorno_id == "espacial":
        entorno = espacial.MisionEspacial()
    else:
        raise ValueError("Entorno no reconocido")

    agentes = [Agente(id=i, entorno=entorno) for i in range(5)]

    for _ in range(100):  # ciclos de simulación
        observaciones = entorno.obtener_estado()
        for agente in agentes:
            accion = agente.decidir(observaciones)
            entorno.aplicar_accion(agente.id, accion)
        entorno.actualizar()

    entorno.mostrar_resultados()


if __name__ == "__main__":
    main()
