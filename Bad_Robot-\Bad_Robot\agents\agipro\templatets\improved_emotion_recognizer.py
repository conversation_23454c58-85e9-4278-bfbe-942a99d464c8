# EmotionRecognizer.py - Versión mejorada
import cv2
import numpy as np
import dlib
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing.image import img_to_array
import os
from typing import Dict, Tuple, Optional
import logging

class EmotionRecognizer:
    def __init__(self, model_path: Optional[str] = None):
        """
        Inicializar el reconocedor de emociones
        
        Args:
            model_path: Ruta al modelo preentrenado (opcional)
        """
        # Configuración de emociones y umbrales
        self.emotion_model = {
            "happy": {
                "threshold": 0.65,
                "icon": "😊",
                "color": "#FFD700",
                "description": "Felicidad"
            },
            "sad": {
                "threshold": 0.6,
                "icon": "😢",
                "color": "#1E90FF",
                "description": "Tristeza"
            },
            "angry": {
                "threshold": 0.55,
                "icon": "😠", 
                "color": "#FF4500",
                "description": "Enfado"
            },
            "surprised": {
                "threshold": 0.5,
                "icon": "😲",
                "color": "#9370DB",
                "description": "Sorpresa"
            },
            "fear": {
                "threshold": 0.5,
                "icon": "😰",
                "color": "#800080",
                "description": "Miedo"
            },
            "disgust": {
                "threshold": 0.5,
                "icon": "🤢",
                "color": "#008000",
                "description": "Disgusto"
            },
            "neutral": {
                "threshold": 0.0,
                "icon": "😐",
                "color": "#808080",
                "description": "Neutral"
            }
        }
        
        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Inicializar componentes
        self.face_cascade = None
        self.face_detector = None
        self.emotion_classifier = None
        self.model_loaded = False
        
        # Cargar detectores y modelos
        self._load_face_detector()
        if model_path and os.path.exists(model_path):
            self._load_emotion_model(model_path)
        else:
            self.logger.warning("Modelo de emociones no encontrado, usando simulación")
    
    def _load_face_detector(self):
        """Cargar detector de rostros"""
        try:
            # Intentar cargar detector de OpenCV
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            if os.path.exists(cascade_path):
                self.face_cascade = cv2.CascadeClassifier(cascade_path)
                self.logger.info("Detector de rostros OpenCV cargado")
            
            # Intentar cargar detector dlib si está disponible
            try:
                predictor_path = "shape_predictor_68_face_landmarks.dat"
                if os.path.exists(predictor_path):
                    self.face_detector = dlib.get_frontal_face_detector()
                    self.logger.info("Detector dlib cargado")
            except ImportError:
                self.logger.warning("dlib no disponible")
                
        except Exception as e:
            self.logger.error(f"Error cargando detector de rostros: {e}")
    
    def _load_emotion_model(self, model_path: str):
        """Cargar modelo de clasificación de emociones"""
        try:
            self.emotion_classifier = load_model(model_path)
            self.model_loaded = True
            self.logger.info(f"Modelo de emociones cargado: {model_path}")
        except Exception as e:
            self.logger.error(f"Error cargando modelo de emociones: {e}")
            self.model_loaded = False
    
    def _detect_faces(self, image: np.ndarray) -> list:
        """Detectar rostros en la imagen"""
        faces = []
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Usar detector OpenCV si está disponible
        if self.face_cascade is not None:
            detected_faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            faces.extend(detected_faces)
        
        # Usar detector dlib como alternativa
        elif self.face_detector is not None:
            detected_faces = self.face_detector(gray)
            for face in detected_faces:
                x, y, w, h = face.left(), face.top(), face.width(), face.height()
                faces.append([x, y, w, h])
        
        return faces
    
    def _preprocess_face(self, face_img: np.ndarray) -> np.ndarray:
        """Preprocesar imagen de rostro para el modelo"""
        try:
            # Redimensionar a 48x48 (tamaño común para modelos de emociones)
            face_img = cv2.resize(face_img, (48, 48))
            
            # Convertir a escala de grises si es necesario
            if len(face_img.shape) == 3:
                face_img = cv2.cvtColor(face_img, cv2.COLOR_BGR2GRAY)
            
            # Normalizar
            face_img = face_img.astype('float32') / 255.0
            
            # Expandir dimensiones para el modelo
            face_img = img_to_array(face_img)
            face_img = np.expand_dims(face_img, axis=0)
            
            return face_img
            
        except Exception as e:
            self.logger.error(f"Error preprocesando rostro: {e}")
            return None
    
    def _classify_emotion_real(self, face_img: np.ndarray) -> Dict[str, float]:
        """Clasificar emoción usando modelo real"""
        if not self.model_loaded or self.emotion_classifier is None:
            return self._simulate_emotion_detection()
        
        try:
            processed_face = self._preprocess_face(face_img)
            if processed_face is None:
                return self._simulate_emotion_detection()
            
            # Predicción
            predictions = self.emotion_classifier.predict(processed_face, verbose=0)
            
            # Mapear predicciones a emociones (ajustar según tu modelo)
            emotion_labels = list(self.emotion_model.keys())
            emotion_probs = {}
            
            for i, emotion in enumerate(emotion_labels[:len(predictions[0])]):
                emotion_probs[emotion] = float(predictions[0][i])
            
            return emotion_probs
            
        except Exception as e:
            self.logger.error(f"Error en clasificación real: {e}")
            return self._simulate_emotion_detection()
    
    def _simulate_emotion_detection(self) -> Dict[str, float]:
        """Simulación de detección de emociones (para desarrollo/testing)"""
        emotions = list(self.emotion_model.keys())
        # Generar probabilidades más realistas
        base_probs = np.random.dirichlet(np.ones(len(emotions)) * 0.5)
        
        # Dar más peso a neutral y happy para simulación más realista
        weights = [0.8, 0.6, 0.3, 0.4, 0.2, 0.2, 1.2]  # neutral tiene más peso
        weighted_probs = base_probs * weights
        weighted_probs = weighted_probs / weighted_probs.sum()
        
        return {emotion: float(prob) for emotion, prob in zip(emotions, weighted_probs)}
    
    def _apply_emotion_thresholds(self, emotion_probs: Dict[str, float]) -> Dict[str, float]:
        """Aplicar umbrales de confianza a las emociones"""
        filtered_emotions = {}
        
        for emotion, prob in emotion_probs.items():
            threshold = self.emotion_model[emotion]["threshold"]
            if prob >= threshold:
                filtered_emotions[emotion] = prob
        
        return filtered_emotions
    
    def _get_dominant_emotion(self, emotion_probs: Dict[str, float]) -> Tuple[str, float]:
        """Obtener la emoción dominante"""
        if not emotion_probs:
            return "neutral", 1.0
        
        dominant = max(emotion_probs.items(), key=lambda x: x[1])
        return dominant[0], dominant[1]
    
    def analyze_face(self, image: np.ndarray, return_all_faces: bool = False) -> Dict:
        """
        Analizar emoción en imagen facial
        
        Args:
            image: Imagen de entrada (formato OpenCV)
            return_all_faces: Si devolver análisis de todos los rostros detectados
            
        Returns:
            Diccionario con resultado del análisis
        """
        try:
            if image is None or image.size == 0:
                return self._get_default_result("Imagen vacía")
            
            # Detectar rostros
            faces = self._detect_faces(image)
            
            if len(faces) == 0:
                return self._get_default_result("No se detectaron rostros")
            
            results = []
            
            # Analizar cada rostro detectado
            for i, (x, y, w, h) in enumerate(faces):
                try:
                    # Extraer región del rostro
                    face_roi = image[y:y+h, x:x+w]
                    
                    # Clasificar emoción
                    emotion_probs = self._classify_emotion_real(face_roi)
                    
                    # Aplicar umbrales
                    filtered_emotions = self._apply_emotion_thresholds(emotion_probs)
                    
                    # Normalizar probabilidades filtradas
                    if filtered_emotions:
                        total = sum(filtered_emotions.values())
                        normalized_emotions = {k: v/total for k, v in filtered_emotions.items()}
                    else:
                        normalized_emotions = {"neutral": 1.0}
                    
                    # Obtener emoción dominante
                    dominant_emotion, confidence = self._get_dominant_emotion(normalized_emotions)
                    
                    # Crear resultado para este rostro
                    face_result = {
                        "face_id": i,
                        "emotion": dominant_emotion,
                        "confidence": round(confidence, 3),
                        "emotion_icon": self.emotion_model[dominant_emotion]["icon"],
                        "color": self.emotion_model[dominant_emotion]["color"],
                        "description": self.emotion_model[dominant_emotion]["description"],
                        "all_emotions": {k: round(v, 3) for k, v in normalized_emotions.items()},
                        "face_coordinates": {"x": int(x), "y": int(y), "w": int(w), "h": int(h)}
                    }
                    
                    results.append(face_result)
                    
                except Exception as e:
                    self.logger.error(f"Error analizando rostro {i}: {e}")
                    continue
            
            # Retornar resultado
            if not results:
                return self._get_default_result("Error procesando rostros")
            
            if return_all_faces:
                return {
                    "success": True,
                    "faces_detected": len(results),
                    "faces": results
                }
            else:
                # Retornar el rostro con mayor confianza
                best_face = max(results, key=lambda x: x["confidence"])
                return {
                    "success": True,
                    "faces_detected": len(results),
                    **{k: v for k, v in best_face.items() if k != "face_id"}
                }
                
        except Exception as e:
            self.logger.error(f"Error en análisis facial: {e}")
            return self._get_default_result(f"Error: {str(e)}")
    
    def _get_default_result(self, error_msg: str = None) -> Dict:
        """Obtener resultado por defecto en caso de error"""
        return {
            "success": False,
            "emotion": "neutral",
            "confidence": 0.0,
            "emotion_icon": "😐",
            "color": "#808080",
            "description": "Neutral",
            "error": error_msg,
            "faces_detected": 0
        }
    
    def get_emotion_info(self, emotion: str) -> Dict:
        """Obtener información detallada de una emoción"""
        return self.emotion_model.get(emotion, self.emotion_model["neutral"])
    
    def set_emotion_threshold(self, emotion: str, threshold: float):
        """Ajustar umbral de una emoción específica"""
        if emotion in self.emotion_model:
            self.emotion_model[emotion]["threshold"] = max(0.0, min(1.0, threshold))
            self.logger.info(f"Umbral de {emotion} ajustado a {threshold}")
    
    def get_available_emotions(self) -> list:
        """Obtener lista de emociones disponibles"""
        return list(self.emotion_model.keys())

# Función de utilidad para testing
def test_emotion_recognizer():
    """Función de prueba para el reconocedor de emociones"""
    recognizer = EmotionRecognizer()
    
    # Crear imagen de prueba
    test_image = np.random.randint(0, 255, (300, 300, 3), dtype=np.uint8)
    
    # Analizar
    result = recognizer.analyze_face(test_image)
    print("Resultado de prueba:", result)
    
    return result

if __name__ == "__main__":
    test_emotion_recognizer()
