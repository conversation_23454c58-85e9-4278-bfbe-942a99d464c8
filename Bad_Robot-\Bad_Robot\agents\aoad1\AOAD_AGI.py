import os
import datetime
import json
import subprocess
from dotenv import load_dotenv
from transformers import pipeline
import openai

# Cargar variables de entorno
load_dotenv()

# Configuración global
AGI_NAME = "AOAD_AGI"
HISTORY_FILE = "historial_interacciones.json"
ERROR_HISTORY = []

# Inicializar NLU pipeline
nlu_classifier = pipeline("text-classification", model="joeddav/distilbert-base-uncased-go-emotions-student")

# Configurar OpenAI API
openai.api_key = os.getenv("OPENAI_API_KEY")

# === FUNCIONES DE UTILIDAD ===

def log_message(message):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {AGI_NAME}: {message}")

def guardar_historial(tipo, entrada, salida=None):
    historial = []
    if os.path.exists(HISTORY_FILE):
        with open(HISTORY_FILE, 'r') as f:
            historial = json.load(f)
    historial.append({
        "tipo": tipo,
        "entrada": entrada,
        "salida": salida,
        "fecha": datetime.datetime.now().isoformat()
    })
    with open(HISTORY_FILE, 'w') as f:
        json.dump(historial, f, indent=2)

def analizar_intencion(peticion):
    resultado = nlu_classifier(peticion)
    return resultado[0]['label'], resultado[0]['score']

def generar_codigo(prompt):
    try:
        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt=prompt,
            max_tokens=500,
            temperature=0.7
        )
        return response.choices[0].text.strip()
    except Exception as e:
        log_message(f"Error al generar código: {e}")
        return None

def planificar_tarea(peticion):
    peticion_lower = peticion.lower()
    if "web" in peticion_lower or "html" in peticion_lower:
        return {"lenguaje": "html", "extension": ".html"}
    elif "script" in peticion_lower and "bash" in peticion_lower:
        return {"lenguaje": "bash", "extension": ".sh"}
    elif "funcion" in peticion_lower and "python" in peticion_lower:
        return {"lenguaje": "python", "extension": ".py"}
    else:
        return {"lenguaje": "python", "extension": ".py"}  # Default

def verificar_seguridad(codigo, lenguaje):
    if lenguaje == "bash":
        peligrosos = ["rm -rf", "sudo", "dd", "mkfs"]
        for cmd in peligrosos:
            if cmd in codigo.lower():
                return False
    return True

def crear_archivo_con_codigo(nombre_archivo, codigo, lenguaje="desconocido"):
    try:
        with open(nombre_archivo, "w", encoding="utf-8") as f:
            f.write(f"# Archivo generado por {AGI_NAME} en {datetime.datetime.now()}\n")
            f.write(f"# Lenguaje: {lenguaje}\n\n")
            f.write(codigo)
        log_message(f"Archivo '{nombre_archivo}' creado exitosamente.")
        return True
    except Exception as e:
        log_message(f"Error al crear el archivo '{nombre_archivo}': {e}")
        ERROR_HISTORY.append({"archivo": nombre_archivo, "error": str(e)})
        return False

def procesar_peticion_usuario(peticion):
    log_message("Analizando intención...")
    emocion, confianza = analizar_intencion(peticion)
    log_message(f"Intención detectada: {emocion} ({confianza:.2f})")

    if "solicitud" not in emocion.lower():
        log_message("No parece ser una solicitud clara de generación de código.")
        return

    log_message("Planificando tarea...")
    plan = planificar_tarea(peticion)
    lenguaje = plan["lenguaje"]
    extension = plan["extension"]

    prompt = f"""
Eres un experto desarrollador. Escribe un ejemplo útil de código en {lenguaje} que resuelva lo siguiente:

"{peticion}"

Asegúrate de incluir comentarios claros y ejemplos de uso si aplica.
"""

    log_message("Generando código...")
    codigo_generado = generar_codigo(prompt)

    if not codigo_generado:
        log_message("No se pudo generar código.")
        return

    if not verificar_seguridad(codigo_generado, lenguaje):
        log_message("El código generado no es seguro. No se ejecutará.")
        return

    nombre_archivo = input(f"{AGI_NAME}: ¿Qué nombre deseas darle al archivo? (por defecto: agi_codigo{extension}): ") or f"agi_codigo{extension}"

    confirmacion = input(f"{AGI_NAME}: ¿Quieres crear el archivo '{nombre_archivo}'? (s/n): ").lower()
    if confirmacion != 's':
        log_message("Operación cancelada.")
        return

    if crear_archivo_con_codigo(nombre_archivo, codigo_generado, lenguaje):
        guardar_historial("codigo", peticion, nombre_archivo)
        log_message("Código guardado correctamente.")

# === BUCLE PRINCIPAL ===

def iniciar_chat_agi():
    log_message("Iniciando AGI Observadora y Autocodificadora Dinámica Avanzada.")
    log_message("Escribe tu petición o 'salir' para terminar.")
    
    while True:
        try:
            peticion_usuario = input("Usuario: ")
            if peticion_usuario.lower() in ["salir", "adios", "exit"]:
                log_message("Terminando simulación.")
                break
            procesar_peticion_usuario(peticion_usuario)
        except KeyboardInterrupt:
            log_message("\nInterrupción por teclado. Terminando simulación.")
            break
        except Exception as e:
            log_message(f"Ha ocurrido un error inesperado: {e}")
            ERROR_HISTORY.append({"error": str(e)})

# === PUNTO DE ENTRADA ===

if __name__ == "__main__":
    iniciar_chat_agi()