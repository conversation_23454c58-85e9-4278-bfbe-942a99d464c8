import random

def seleccionar_mejores(agentes, recompensas, top_n=3):
    # Selecciona los agentes con mejor recompensa
    agentes_ordenados = sorted(agentes, key=lambda a: recompensas.get(a.id, 0), reverse=True)
    return agentes_ordenados[:top_n]

def cruzar_genotipos(gen1, gen2):
    # Cruza dos genotipos intercambiando aleatoriamente valores
    nuevo_gen = {}
    for clave in gen1.keys():
        nuevo_gen[clave] = random.choice([gen1[clave], gen2[clave]])
    return nuevo_gen

def mutar_genotipo(genotipo, tasa_mutacion=0.1):
    # Aplica mutación aleatoria en algunos atributos
    gen = genotipo.copy()
    if random.random() < tasa_mutacion:
        # Mutar tipo (puede cambiar a otro)
        gen["tipo"] = random.choice(["inversor", "especulador", "conservador"])
    if random.random() < tasa_mutacion:
        # Mutar agresividad (0.1 a 1.0)
        gen["agresividad"] = min(1.0, max(0.1, gen["agresividad"] + random.uniform(-0.2, 0.2)))
    if random.random() < tasa_mutacion:
        # Mutar paciencia (0.1 a 1.0)
        gen["paciencia"] = min(1.0, max(0.1, gen["paciencia"] + random.uniform(-0.2, 0.2)))
    return gen
