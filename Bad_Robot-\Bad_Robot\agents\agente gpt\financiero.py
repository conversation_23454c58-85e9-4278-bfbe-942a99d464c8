### entornos/financiero.py - Simulación de un mercado bursátil con oferta y demanda

import random
from collections import defaultdict

class MercadoFinanciero:
    def __init__(self):
        self.precio_accion = 100.0
        self.historial = []
        self.recompensas = defaultdict(float)
        self.ofertas = defaultdict(list)  # agente_id -> lista de ofertas
        self.demandas = defaultdict(list)  # agente_id -> lista de demandas
        self.noticias = {"positiva": False, "negativa": False}

    def obtener_estado(self):
        return {
            "precio_accion": round(self.precio_accion, 2),
            "tendencia": random.choice(["alcista", "bajista", "estable"]),
            "volatilidad": random.uniform(0.01, 0.1)
        }

    def aplicar_accion(self, agente_id, accion):
        if accion == "comprar":
            self.demandas[agente_id].append(self.precio_accion)
        elif accion == "vender":
            self.ofertas[agente_id].append(self.precio_accion)
        elif accion == "mantener":
            pass  # sin efecto directo

    def actualizar(self):
        total_oferta = sum(len(v) for v in self.ofertas.values())
        total_demanda = sum(len(v) for v in self.demandas.values())
        variacion = (total_demanda - total_oferta) * 0.5 + random.gauss(0, 1)
        self.precio_accion = max(1, self.precio_accion + variacion)

        for ag_id, compras in self.demandas.items():
            ganancia = sum(self.precio_accion - p for p in compras)
            self.recompensas[ag_id] += ganancia

        for ag_id, ventas in self.ofertas.items():
            ganancia = sum(p - self.precio_accion for p in ventas)
            self.recompensas[ag_id] += ganancia

        self.historial.append(self.precio_accion)
        self.ofertas.clear()
        self.demandas.clear()

    def mostrar_resultados(self):
        print("\nResultados del mercado financiero:")
        for ag_id, score in sorted(self.recompensas.items(), key=lambda x: x[1], reverse=True):
            print(f"Agente {ag_id}: ganancia total = {round(score, 2)}")
        print(f"Precio final de la acción: {round(self.precio_accion, 2)}")
        self.noticias = {
            "positiva": random.random() < 0.3,
            "negativa": random.random() < 0.2,
        }
        estado = {
            "precio_accion": round(self.precio_accion, 2),
            "tendencia": random.choice(["alcista", "bajista", "estable"]),
            "volatilidad": random.uniform(0.01, 0.1),
            "noticias": self.noticias,
        }
        return estado