<!DOCTYPE html>
<html>
<head>
    <title>AGI Assistant</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        #chat-container { height: 400px; border: 1px solid #ddd; padding: 10px; overflow-y: scroll; margin-bottom: 10px; }
        #camera-feed { width: 320px; height: 240px; border: 1px solid #ccc; margin-bottom: 10px; }
        #user-input { width: 70%; padding: 8px; }
        button { padding: 8px 15px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Asistente AGI</h1>
    
    <div id="camera-container">
        <video id="camera-feed" autoplay></video>
        <button id="capture-btn">Capturar Rostro</button>
    </div>
    
    <div id="chat-container"></div>
    
    <div>
        <input id="user-input" type="text" placeholder="Escribe tu mensaje...">
        <button id="send-btn">Enviar</button>
        <button id="listen-btn">🎤 Hablar</button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        const socket = io();
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const listenBtn = document.getElementById('listen-btn');
        const cameraFeed = document.getElementById('camera-feed');
        const captureBtn = document.getElementById('capture-btn');
        
        // Configurar cámara
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => cameraFeed.srcObject = stream)
            .catch(err => console.error("Error con la cámara:", err));
        
        // Capturar rostro
        captureBtn.addEventListener('click', () => {
            const canvas = document.createElement('canvas');
            canvas.width = cameraFeed.videoWidth;
            canvas.height = cameraFeed.videoHeight;
            canvas.getContext('2d').drawImage(cameraFeed, 0, 0);
            const imageData = canvas.toDataURL('image/jpeg');
            socket.emit('face_capture', { image: imageData });
        });
        
        // Manejar chat
        function sendMessage() {
            const message = userInput.value;
            if (message.trim()) {
                addMessage('Tú', message);
                socket.emit('message', { message });
                userInput.value = '';
            }
        }
        
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
        
        // Reconocimiento de voz
        listenBtn.addEventListener('click', () => {
            if (!('webkitSpeechRecognition' in window)) {
                alert("Tu navegador no soporta reconocimiento de voz");
                return;
            }
            
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'es-ES';
            recognition.start();
            
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                userInput.value = transcript;
                sendMessage();
            };
        });
        
        // Mostrar respuestas
        socket.on('response', (data) => {
            addMessage('AGI', data.text);
        });
        
        socket.on('error', (data) => {
            addMessage('Sistema', `Error: ${data.text}`);
        });
        
        function addMessage(sender, text) {
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${text}`;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
    </script>
</body>
</html>