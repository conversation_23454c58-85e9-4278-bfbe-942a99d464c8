import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.optimizers import <PERSON>
from collections import deque
import random
import hashlib

class CoderDQNAgent:
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.9
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.998 # Decae un poco más lento para aprender tareas complejas
        self.learning_rate = 0.001
        self.model = self._build_model()

    def _build_model(self):
        # Una red un poco más profunda puede ayudar
        model = Sequential([
            Dense(64, input_dim=self.state_size, activation='relu'),
            <PERSON>se(64, activation='relu'),
            Dense(self.action_size, activation='linear')
        ])
        model.compile(loss='huber_loss', optimizer=<PERSON>(learning_rate=self.learning_rate))
        return model

    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state, user_command, last_exec_error=None):
        """Decide una acción basada en el comando, errores previos y la red neuronal."""
        
        # --- CAPA DE RAZONAMIENTO SIMBÓLICO/COGNITIVO ---
        cmd = user_command.lower()
        
        # Prioridad 1: Si hay un error, la acción más lógica es intentar corregirlo.
        if last_exec_error:
            # Esta es la parte "cognitiva": el error informa la acción.
            return 2, "Cognitivo", f"Error detectado. Intentando corregir el código (EJECUTAR_Y_CORREGIR)." # Acción 2
            
        # Prioridad 2: Intenciones claras del usuario
        if "crea" in cmd or "genera" in cmd or "escribe un script" in cmd:
            return 3, "Simbólico", "El comando pide crear código nuevo (CREAR_NUEVO_CODIGO)." # Acción 3
        if "depura" in cmd or "corrige" in cmd or "arregla" in cmd or "ejecuta" in cmd:
            return 2, "Simbólico", "El comando pide ejecutar/depurar código (EJECUTAR_Y_CORREGIR)." # Acción 2
        if "lee" in cmd or "muestra" in cmd:
            return 0, "Simbólico", "El comando pide leer código (LEER_CODIGO)." # Acción 0
        if "modifica" in cmd or "cambia" in cmd or "escribe en" in cmd:
            return 1, "Simbólico", "El comando pide escribir/modificar código (ESCRIBIR_CODIGO)." # Acción 1

        # --- CAPA NEURONAL (DQN) ---
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size), "Neuronal (Exploración)", "Sin regla clara, explorando."
        
        act_values = self.model.predict(state, verbose=0)
        return np.argmax(act_values[0]), "Neuronal (Explotación)", f"Q-Values: {[f'{q:.2f}' for q in act_values[0]]}"

    def replay(self, batch_size):
        if len(self.memory) < batch_size:
            return
        
        minibatch = random.sample(self.memory, batch_size)
        
        states = np.array([t[0] for t in minibatch])
        next_states = np.array([t[3] for t in minibatch])
        
        # Predecir en lote para eficiencia
        targets = self.model.predict(states, verbose=0)
        next_q_values = self.model.predict(next_states, verbose=0)

        for i, (state, action, reward, next_state, done) in enumerate(minibatch):
            target = reward
            if not done:
                target = reward + self.gamma * np.amax(next_q_values[i])
            targets[i][action] = target
        
        self.model.fit(states, targets, epochs=1, verbose=0)
        
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

    @staticmethod
    def get_state_representation(command, last_action_result):
        """Convierte el estado del mundo en un vector numérico."""
        # Hashes para texto
        cmd_hash = int(hashlib.sha256(command.encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)
        
        # Representación numérica del resultado de la última acción
        code_content = last_action_result.get('code_content', '')
        stdout = last_action_result.get('stdout', '')
        stderr = last_action_result.get('stderr', '')

        code_hash = int(hashlib.sha256(code_content.encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)
        stdout_hash = int(hashlib.sha256(stdout.encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)
        stderr_hash = int(hashlib.sha256(stderr.encode('utf-8')).hexdigest(), 16) % (10**4) / (10**4)

        has_error = 1.0 if stderr else 0.0

        state_vector = [cmd_hash, code_hash, stdout_hash, stderr_hash, has_error]
        return np.reshape(state_vector, [1, len(state_vector)])