> [!NOTE]
> This is an archive of the original BabyAGI repo, snapshotted in September 2024. All issues and commits were closed as of this transition. For the newest BabyAGI (released September 2024), visit [https://github.com/yoheinakajima/babyagi](https://github.com/yoheinakajima/babyagi).


# Translations:

[<img title="عربي" alt="عربي" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/sa.svg" width="30">](docs/README-ar.md)
[<img title="Français" alt="Français" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/fr.svg" width="30">](docs/README-fr.md)
[<img title="Polski" alt="Polski" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/pl.svg" width="30">](docs/README-pl.md)
[<img title="Portuguese" alt="Portuguese" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/br.svg" width="30">](docs/README-pt-br.md)
[<img title="Romanian" alt="Romanian" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/ro.svg" width="30">](docs/README-ro.md)
[<img title="Russian" alt="Russian" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/ru.svg" width="30">](docs/README-ru.md)
[<img title="Slovenian" alt="Slovenian" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/si.svg" width="30">](docs/README-si.md)
[<img title="Spanish" alt="Spanish" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/es.svg" width="30">](docs/README-es.md)
[<img title="Turkish" alt="Turkish" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/tr.svg" width="30">](docs/README-tr.md)
[<img title="Ukrainian" alt="Ukrainian" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/ua.svg" width="30">](docs/README-ua.md)
[<img title="简体中文" alt="Simplified Chinese" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/cn.svg" width="30">](docs/README-cn.md)
[<img title="繁體中文 (Traditional Chinese)" alt="繁體中文 (Traditional Chinese)" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/tw.svg" width="30">](docs/README-zh-tw.md)
[<img title="日本語" alt="日本語" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/jp.svg" width="30">](docs/README-ja.md)
[<img title="한국어" alt="한국어" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/kr.svg" width="30">](docs/README-ko.md)
[<img title="Magyar" alt="Magyar" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/hu.svg" width="30">](docs/README-hu.md)
[<img title="فارسی" alt="فارسی" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/ir.svg" width="30">](docs/README-fa.md)
[<img title="German" alt="German" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/de.svg" width="30">](docs/README-de.md)
[<img title="Indian" alt="Indian" src="https://cdn.staticaly.com/gh/hjnilsson/country-flags/master/svg/in.svg" width="30">](docs/README-in.md)
[<img title="Vietnamese" alt="Vietnamese" src="https://raw.githubusercontent.com/hampusborgos/country-flags/main/svg/vn.svg" width="30">](docs/README-vn.md)


# Objective

This Python script is an example of an AI-powered task management system. The system uses OpenAI and vector databases such as Chroma or Weaviate to create, prioritize, and execute tasks. The main idea behind this system is that it creates tasks based on the result of previous tasks and a predefined objective. The script then uses OpenAI's natural language processing (NLP) capabilities to create new tasks based on the objective, and Chroma/Weaviate to store and retrieve task results for context. This is a pared-down version of the original [Task-Driven Autonomous Agent](https://twitter.com/yoheinakajima/status/1640934493489070080?s=20) (Mar 28, 2023).

This README will cover the following:

- [How the script works](#how-it-works)

- [How to use the script](#how-to-use)

- [Supported Models](#supported-models)

- [Warning about running the script continuously](#continuous-script-warning)

# How It Works<a name="how-it-works"></a>

The script works by running an infinite loop that does the following steps:

1. Pulls the first task from the task list.
2. Sends the task to the execution agent, which uses OpenAI's API to complete the task based on the context.
3. Enriches the result and stores it in [Chroma](https://docs.trychroma.com)/[Weaviate](https://weaviate.io/).
4. Creates new tasks and reprioritizes the task list based on the objective and the result of the previous task.
   </br>

![image](https://user-images.githubusercontent.com/21254008/235015461-543a897f-70cc-4b63-941a-2ae3c9172b11.png)

The `execution_agent()` function is where the OpenAI API is used. It takes two parameters: the objective and the task. It then sends a prompt to OpenAI's API, which returns the result of the task. The prompt consists of a description of the AI system's task, the objective, and the task itself. The result is then returned as a string.

The `task_creation_agent()` function is where OpenAI's API is used to create new tasks based on the objective and the result of the previous task. The function takes four parameters: the objective, the result of the previous task, the task description, and the current task list. It then sends a prompt to OpenAI's API, which returns a list of new tasks as strings. The function then returns the new tasks as a list of dictionaries, where each dictionary contains the name of the task.

The `prioritization_agent()` function is where OpenAI's API is used to reprioritize the task list. The function takes one parameter, the ID of the current task. It sends a prompt to OpenAI's API, which returns the reprioritized task list as a numbered list.

Finally, the script uses Chroma/Weaviate to store and retrieve task results for context. The script creates a Chroma/Weaviate collection based on the table name specified in the TABLE_NAME variable. Chroma/Weaviate is then used to store the results of the task in the collection, along with the task name and any additional metadata.

# How to Use<a name="how-to-use"></a>

To use the script, you will need to follow these steps:

1. Clone the repository via `git clone https://github.com/yoheinakajima/babyagi.git` and `cd` into the cloned repository.
2. Install the required packages: `pip install -r requirements.txt`
3. Copy the .env.example file to .env: `cp .env.example .env`. This is where you will set the following variables.
4. Set your OpenAI API key in the OPENAI_API_KEY and OPENAI_API_MODEL variables. In order to use with Weaviate you will also need to setup additional variables detailed [here](docs/weaviate.md).
5. Set the name of the table where the task results will be stored in the TABLE_NAME variable.
6. (Optional) Set the name of the BabyAGI instance in the BABY_NAME variable.
7. (Optional) Set the objective of the task management system in the OBJECTIVE variable.
8. (Optional) Set the first task of the system in the INITIAL_TASK variable.
9. Run the script: `python babyagi.py`

All optional values above can also be specified on the command line.

# Running inside a docker container

As a prerequisite, you will need docker and docker-compose installed. Docker desktop is the simplest option https://www.docker.com/products/docker-desktop/

To run the system inside a docker container, setup your .env file as per steps above and then run the following:

```
docker-compose up
```

# Supported Models<a name="supported-models"></a>

This script works with all OpenAI models, as well as Llama and its variations through Llama.cpp. Default model is **gpt-3.5-turbo**. To use a different model, specify it through LLM_MODEL or use the command line.

## Llama

Llama integration requires llama-cpp package. You will also need the Llama model weights. 

- **Under no circumstances share IPFS, magnet links, or any other links to model downloads anywhere in this repository, including in issues, discussions or pull requests. They will be immediately deleted.**

Once you have them, set LLAMA_MODEL_PATH to the path of the specific model to use. For convenience, you can link `models` in BabyAGI repo to the folder where you have the Llama model weights. Then run the script with `LLM_MODEL=llama` or `-l` argument.

# Warning<a name="continuous-script-warning"></a>

This script is designed to be run continuously as part of a task management system. Running this script continuously can result in high API usage, so please use it responsibly. Additionally, the script requires the OpenAI API to be set up correctly, so make sure you have set up the API before running the script.

# Contribution

Needless to say, BabyAGI is still in its infancy and thus we are still determining its direction and the steps to get there. Currently, a key design goal for BabyAGI is to be _simple_ such that it's easy to understand and build upon. To maintain this simplicity, we kindly request that you adhere to the following guidelines when submitting PRs:

- Focus on small, modular modifications rather than extensive refactoring.
- When introducing new features, provide a detailed description of the specific use case you are addressing.

A note from @yoheinakajima (Apr 5th, 2023):

> I know there are a growing number of PRs, appreciate your patience - as I am both new to GitHub/OpenSource, and did not plan my time availability accordingly this week. Re:direction, I've been torn on keeping it simple vs expanding - currently leaning towards keeping a core Baby AGI simple, and using this as a platform to support and promote different approaches to expanding this (eg. BabyAGIxLangchain as one direction). I believe there are various opinionated approaches that are worth exploring, and I see value in having a central place to compare and discuss. More updates coming shortly.

I am new to GitHub and open source, so please be patient as I learn to manage this project properly. I run a VC firm by day, so I will generally be checking PRs and issues at night after I get my kids down - which may not be every night. Open to the idea of bringing in support, will be updating this section soon (expectations, visions, etc). Talking to lots of people and learning - hang tight for updates!

# BabyAGI Activity Report

To help the BabyAGI community stay informed about the project's progress, Blueprint AI has developed a Github activity summarizer for BabyAGI. This concise report displays a summary of all contributions to the BabyAGI repository over the past 7 days (continuously updated), making it easy for you to keep track of the latest developments.

To view the BabyAGI 7-day activity report, go here: [https://app.blueprint.ai/github/yoheinakajima/babyagi](https://app.blueprint.ai/github/yoheinakajima/babyagi)

[<img width="293" alt="image" src="https://user-images.githubusercontent.com/334530/235789974-f49d3cbe-f4df-4c3d-89e9-bfb60eea6308.png">](https://app.blueprint.ai/github/yoheinakajima/babyagi)


# Inspired projects

In the short time since it was release, BabyAGI inspired many projects. You can see them all [here](docs/inspired-projects.md).

# Backstory

BabyAGI is a pared-down version of the original [Task-Driven Autonomous Agent](https://twitter.com/yoheinakajima/status/1640934493489070080?s=20) (Mar 28, 2023) shared on Twitter. This version is down to 140 lines: 13 comments, 22 blanks, and 105 code. The name of the repo came up in the reaction to the original autonomous agent - the author does not mean to imply that this is AGI.

Made with love by [@yoheinakajima](https://twitter.com/yoheinakajima), who happens to be a VC (would love to see what you're building!)
# 🎯 Task-Driven Autonomous Agent System

## 📑 Overview

**Task-Driven Autonomous Agent System using [GPT-4](https://openai.com/product/gpt-4), [pinecone](https://www.pinecone.io/) vector search, and the [LangChain](https://python.langchain.com/en/latest/) framework.**

This experimental task-driven autonomous agent system is a cutting-edge AI-powered solution that leverages GPT-4, Pinecone vector search, and the LangChain framework to efficiently complete, generate, and prioritize tasks, while also providing interfaces for code, source control, file systems, documentation, and more.

![tasks-cover-13-3444x1661-sharpened-new-2-proper-transparent](https://github.com/kalaspuff/ai-assisted-task-executor/assets/89139/26ac826a-82bc-482d-9a18-03f84ecbaaa3)

### Brief Description of Agents

* 📋 **Task Manager Agent**: Responsible for generating, managing, and prioritizing tasks in the task list.
* 🏃 **Task Executor Agent**: Processes and completes tasks using GPT-4 and the LangChain framework.
* 🧠 **Memory Manager Agent**: Manages memory storage of the system – short-term and long-term memory.
* ⚙️ **Execution Context Agent**: Handles external execution environments – file systems, APIs, unit tests.
* 🔒 **Security and Safety Agent**: Monitors and filtering throughout the process ensuring safe operation.

---

![Recent advancements in AI have shown that models can do more with memory. Memory-augmented neural networks can effectively store and retrieve information, leading to significant improvements in performance. At the same time, exploring the concept of interfaces and providers can help us create more adaptable and efficient AI systems. One example is the LangChain framework, which provides a flexible and extensible interface for natural language processing tasks.](https://user-images.githubusercontent.com/89139/229541256-3ace01cf-ed5a-45ba-88f7-8fad5718afc2.png)
<sup>See the presentation at [https://gamma.app/public/TaskDriven-Autonomous-Agent-System-m400jx4xqa22fcp](https://gamma.app/public/TaskDriven-Autonomous-Agent-System-m400jx4xqa22fcp).</sup>

## 🔄 System Operation

The system operates in the following order:

1. Task Manager Agent generates and prioritizes tasks in the task list.
2. Task Executor Agent processes the task at the front of the task list.
3. Memory Manager Agent provides necessary memory and context for task execution.
4. Execution Context Agent interfaces with external environments to fetch data or execute tasks as needed.
5. Task Executor Agent completes tasks, storing the results, and updates the Memory Manager Agent.
6. Task Manager Agent generates tasks based on the completed task result and reprioritizes the task list.
7. Security and Safety Agent monitors and acts to maintain ethical and safety standards.

## 🏗️ System Architecture

High level description of the entire system, followed by details about the purpose of each of agents.

### 📝 Pseudocode of Task-Driven Autonomous Agent System

By using this pseudocode, each agent can share an understanding of the entire system operation, allowing them to perform their respective tasks effectively and with greater focus.

```python
# Pseudocode for the Task-Driven Autonomous Agent System

# Initialize and configure all agents
task_manager_agent = TaskManagerAgent()
task_executor_agent = TaskExecutorAgent()
memory_manager_agent = MemoryManagerAgent()
execution_context_agent = ExecutionContextAgent()
security_safety_agent = SecuritySafetyAgent()

# Main loop for the system operation
while True:

    # Task Manager Agent generates and prioritizes tasks
    task_manager_agent.generate_and_prioritize_tasks()

    # Task Executor Agent processes the task at the front of the task list
    current_task = task_manager_agent.get_next_task()
    task_executor_agent.process_task(current_task)

    # Memory Manager Agent provides necessary memory and context for task execution
    memory_context = memory_manager_agent.get_memory_context(current_task)
    task_executor_agent.set_memory_context(memory_context)

    # Execution Context Agent interfaces with external environments
    execution_context = execution_context_agent.get_execution_context(current_task)
    task_executor_agent.set_execution_context(execution_context)

    # Task Executor Agent completes the task, stores the result in Pinecone, and updates the Memory Manager Agent
    task_result = task_executor_agent.complete_task(current_task)
    memory_manager_agent.store_task_result(current_task, task_result)

    # Task Manager Agent generates new tasks based on the completed task result and reprioritizes the task list
    new_tasks = task_manager_agent.generate_new_tasks(current_task, task_result)
    task_manager_agent.add_and_prioritize_tasks(new_tasks)

    # Security and Safety Agent monitors and filters content throughout the process
    security_safety_agent.monitor_and_filter_content(current_task, task_result)

    # Check for system termination conditions, such as all tasks completed or a specific goal reached
    if system_termination_condition():
        break
```

The system consists of the following agents and components:

### 📋 Task Manager Agent

The Task Manager Agent maintains the task list, generates new tasks based on completed results, and prioritizes tasks in real-time.

#### Initialization of Task Manager Agent

In order to enable the Task Manager Agent to perform its duties effectively in the proposed architecture and examples, the system message should contain the following components:

1. Task ID: A unique identifier for each task in the system, which can be used to track the progress, completion status, and dependencies of tasks.

2. Task Type: A label or category that indicates the nature of the task, such as "analysis", "implementation", "testing", or "deployment". This information can be used by the Task Manager Agent to prioritize tasks based on their types and dependencies.

3. Task Description: A detailed description of the task, including the input requirements, expected output, and any constraints or limitations. The Task Manager Agent can use this information to break down complex tasks into smaller, manageable subtasks.

4. Task Dependencies: A list of other tasks or subtasks that must be completed before the current task can be executed. This information helps the Task Manager Agent manage task sequencing and scheduling.

5. Task Priority: An assigned priority level for the task, which can be used by the Task Manager Agent to sort tasks in the task list and allocate resources accordingly.

6. Task Status: The current status of the task, such as "pending", "in progress", "completed", or "failed". The Task Manager Agent uses this information to monitor task progress and update the task list in real-time.

7. Task Deadline: An optional deadline for task completion, which can be used by the Task Manager Agent to prioritize time-sensitive tasks.

8. Task Owner: An optional field indicating the responsible party or team for the task, which can be useful in multi-agent or multi-team environments.

9. Task Metadata: Additional metadata related to the task, such as associated project, tags, or context information. This data can be used by the Task Manager Agent to filter, sort, or group tasks based on specific criteria.

#### A sample system message for the Task Manager Agent could look like

```json
{
  "task_id": "t123",
  "task_type": "analysis",
  "task_description": "Analyze the Sentry stack trace and identify the issue in the production system",
  "task_dependencies": [],
  "task_priority": "high",
  "task_status": "pending",
  "task_deadline": "2023-01-01T00:00:00Z",
  "task_owner": "team-abc",
  "task_metadata": {
    "project": "project-xyz",
    "tags": ["production", "sentry", "error"],
    "context": {
      "sentry_stack_trace": "<stack_trace_data_here>",
      "github_repository": "https://github.com/user/repo"
    }
  }
}
```

By crafting system messages with these components, the Task Manager Agent can effectively manage, prioritize, and schedule tasks in the proposed architecture and examples.

### 🏃 Task Executor Agent

The Task Executor Agent is responsible for completing tasks in the task list using GPT-4 and LangChain's capabilities.

#### Initialization of Task Executor Agent

In order to enable the Task Executor Agent to perform its duties effectively in the proposed architecture and examples, the system message should contain the following components:

1. Task ID: A unique identifier for the task to be executed, which can be used to track progress, manage dependencies, and report completion status.

2. Task Type: A label or category indicating the nature of the task, such as "analysis", "implementation", "testing", or "deployment". This information helps the Task Executor Agent to determine the appropriate execution strategy and resources.

3. Task Description: A detailed description of the task, including the input requirements, expected output, and any constraints or limitations. This information allows the Task Executor Agent to understand the task's requirements and generate appropriate solutions.

4. Task Dependencies: A list of other tasks or subtasks that must be completed before the current task can be executed. The Task Executor Agent can use this information to manage task sequencing and ensure that all necessary prerequisites are met before executing the task.

5. Task Context: A collection of contextual information related to the task, such as associated project, environment variables, or external resources. This context helps the Task Executor Agent to access necessary data, interfaces, or execution environments for the task.

6. Task Owner: An optional field indicating the responsible party or team for the task, which can be useful in multi-agent or multi-team environments for communication and collaboration.

7. Task Metadata: Additional metadata related to the task, such as input data, output data, or any intermediate results. This data can be used by the Task Executor Agent to store, retrieve, or communicate task-related information.

#### A sample system message for the Task Executor Agent could look like

```json
{
  "task_id": "t123",
  "task_type": "analysis",
  "task_description": "Analyze the Sentry stack trace and identify the issue in the production system",
  "task_dependencies": [],
  "task_context": {
    "project": "project-xyz",
    "environment_variables": {
      "SENTRY_API_KEY": "<sentry_api_key>",
      "GITHUB_API_KEY": "<github_api_key>"
    },
    "external_resources": {
      "sentry_stack_trace": "<stack_trace_data_here>",
      "github_repository": "https://github.com/user/repo"
    }
  },
  "task_owner": "team-abc",
  "task_metadata": {
    "input_data": "<input_data_here>",
    "output_data": null,
    "intermediate_results": null
  }
}
```

By crafting system messages with these components, the Task Executor Agent can effectively execute tasks, manage dependencies, and generate appropriate solutions in the proposed architecture and examples.

### 🧠 Memory Manager Agent

The Memory Manager Agent manages the memory of the system, including short-term and long-term memory.

#### Initialization of Memory Manager Agent

In order to enable the Memory Manager Agent to perform its duties effectively in the proposed architecture and examples, the system message should contain the following components:

1. Memory Action: An action indicating the operation to be performed by the Memory Manager Agent, such as "store", "retrieve", "update", or "delete".

2. Memory Key: A unique identifier for the memory item, which can be used to store, retrieve, update, or delete the associated data in the memory storage system (e.g., Pinecone).

3. Memory Data: The data associated with the memory item, such as task results, intermediate progress, or contextual information. This data is used by the Memory Manager Agent to store or update the memory item in the memory storage system.

4. Memory Metadata: Additional metadata related to the memory item, such as data type, access permissions, or expiration time. This information helps the Memory Manager Agent to manage memory items and enforce access controls or data retention policies.

5. Memory Query: An optional query used to retrieve memory items based on specific criteria, such as matching metadata, similarity to other items, or proximity in a vector space. This query is used by the Memory Manager Agent when performing retrieval operations.

#### A sample system message for the Memory Manager Agent could look like

```json
{
  "memory_action": "store",
  "memory_key": "m123",
  "memory_data": {
    "task_id": "t123",
    "task_result": "<task_result_data_here>"
  },
  "memory_metadata": {
    "data_type": "task_result",
    "access_permissions": {
      "read": ["team-abc"],
      "write": ["team-abc"]
    },
    "expiration_time": "2023-01-01T00:00:00Z"
  },
  "memory_query": null
}
```

By crafting system messages with these components, the Memory Manager Agent can effectively manage memory items, provide necessary memory and context for task execution, and enforce access controls and data retention policies in the proposed architecture and examples.

### ⚙️ Execution Context Agent

The Execution Context Agent interfaces with external execution environments such as file systems, APIs, and unit test frameworks.

#### Initialization of Execution Context Agent

In order to enable the Execution Context Agent to perform its duties effectively in the proposed architecture and examples, the system message should contain the following components:

1. Context Action: An action indicating the operation to be performed by the Execution Context Agent, such as "read", "write", "execute", or "interact".

2. Context Type: A label or category indicating the type of execution context, such as "file_system", "api", "unit_test_framework", or "external_resource". This information helps the Execution Context Agent determine the appropriate strategy and resources for interacting with the external environment.

3. Context Target: The target resource or component that the Execution Context Agent should interact with, such as a file path, API endpoint, or external service. This information is used by the Execution Context Agent to establish a connection with the target and perform the requested operation.

4. Context Parameters: A set of parameters or configuration options related to the context action, such as input data, output format, or authentication credentials. These parameters are used by the Execution Context Agent to customize the interaction and ensure the desired outcome is achieved.

5. Context Metadata: Additional metadata related to the execution context, such as access permissions, rate limits, or retry policies. This information helps the Execution Context Agent manage interactions with external environments and enforce operational constraints or policies.

#### A sample system message for the Execution Context Agent could look like

```json
{
  "context_action": "read",
  "context_type": "file_system",
  "context_target": "/path/to/your/application/codebase/file.js",
  "context_parameters": {
    "encoding": "utf-8"
  },
  "context_metadata": {
    "access_permissions": {
      "read": ["team-abc"],
      "write": ["team-abc"]
    },
    "rate_limits": {
      "requests_per_second": 10
    },
    "retry_policy": {
      "max_attempts": 3,
      "backoff_factor": 2
    }
  }
}
```

By crafting system messages with these components, the Execution Context Agent can effectively interface with external execution environments, such as file systems, APIs, and unit test frameworks, and manage operations according to the architecture and proposed examples.

### 🔒 Security and Safety Agent

The Security and Safety Agent ensures that the input and output generated by the system adhere to ethical and safety guidelines.

#### Initialization of Security and Safety Agent

In order to enable the Security and Safety Agent to perform its duties effectively in the proposed architecture and examples, the system message should contain the following components:

1. Safety Action: An action indicating the operation to be performed by the Security and Safety Agent, such as "monitor", "validate", "filter", or "enforce".

2. Safety Target: The target resource or component that the Security and Safety Agent should interact with, such as a task result, generated code, or external data. This information is used by the Security and Safety Agent to apply safety checks and ensure compliance with ethical guidelines and security requirements.

3. Safety Rules: A set of rules or policies that define the expected behavior, constraints, and limits of the system in terms of ethics, safety, and security. These rules are used by the Security and Safety Agent to validate, filter, or enforce compliance with the desired standards.

4. Safety Parameters: A set of parameters or configuration options related to the safety action, such as input data, output format, or validation options. These parameters are used by the Security and Safety Agent to customize the safety checks and ensure the desired level of compliance is achieved.

5. Safety Metadata: Additional metadata related to the safety and security aspects of the system, such as access permissions, audit logs, or incident reports. This information helps the Security and Safety Agent manage safety and security concerns and enforce operational constraints or policies.

#### A sample system message for the Security and Safety Agent could look like

```json
{
  "safety_action": "validate",
  "safety_target": {
    "task_id": "t123",
    "task_result": "<task_result_data_here>"
  },
  "safety_rules": [
    {
      "rule_id": "r1",
      "rule_description": "Ensure no personally identifiable information (PII) is exposed",
      "rule_type": "data_privacy"
    },
    {
      "rule_id": "r2",
      "rule_description": "Ensure compliance with ethical guidelines",
      "rule_type": "ethics"
    }
  ],
  "safety_parameters": {
    "validation_options": {
      "strict_mode": true
    }
  },
  "safety_metadata": {
    "access_permissions": {
      "read": ["team-abc"],
      "write": ["team-abc"]
    },
    "audit_logs": {
      "enabled": true
    },
    "incident_reports": {
      "alert_level": "high"
    }
  }
}
```

By crafting system messages with these components, the Security and Safety Agent can effectively monitor, validate, filter, and enforce compliance with ethical guidelines and security requirements in the proposed architecture and examples.

## 🌟 Additional future improvements

The proposed system can be further improved by integrating additional automated anomaly reporting, implementing task sequencing and parallel tasks, generating interim milestones, and incorporating real-time priority updates.

## 🏁 Conclusion

The Task-Driven Autonomous Agent System leverages GPT-4, Pinecone vector search, and the LangChain framework to efficiently complete, generate, and prioritize tasks. The modular, flexible, and scalable architecture ensures that the system can handle a wide range of complex tasks and adapt to future improvements. The incorporation of safety and ethical guidelines ensures that the system operates within acceptable constraints and does not cause unintended consequences.

## 📝 Additional Agent Pseudocode

### 📋 Task Manager Agent

```python
# Pseudocode for the Task Manager Agent

class TaskManagerAgent:
    def __init__(self):
        self.task_list = []

    def generate_and_prioritize_tasks(self):
        # Generate new tasks based on completed task results or external triggers
        new_tasks = self.generate_new_tasks()

        # Add new tasks to the task list
        self.add_and_prioritize_tasks(new_tasks)

    def generate_new_tasks(self, completed_task=None, completed_task_result=None):
        # Generate new tasks based on the completed task result
        # or external triggers, such as user input or system events
        new_tasks = []

        if completed_task and completed_task_result:
            # Analyze the completed task result and generate new tasks accordingly
            new_tasks = self.analyze_completed_task(completed_task, completed_task_result)
        else:
            # Generate new tasks based on external triggers
            new_tasks = self.generate_tasks_from_external_triggers()

        return new_tasks

    def add_and_prioritize_tasks(self, new_tasks):
        # Add new tasks to the task list
        self.task_list.extend(new_tasks)

        # Prioritize tasks based on their priority level, dependencies, and deadlines
        self.prioritize_tasks()

    def prioritize_tasks(self):
        # Sort the task list based on task priority, dependencies, and deadlines
        self.task_list.sort(key=lambda task: (task['task_priority'], task['task_dependencies'], task['task_deadline']))

    def get_next_task(self):
        # Get the task at the front of the task list
        next_task = self.task_list.pop(0)
        return next_task

    def analyze_completed_task(self, completed_task, completed_task_result):
        # Analyze the completed task result and generate new tasks accordingly
        # This is a placeholder for the actual analysis logic
        new_tasks = []
        return new_tasks

    def generate_tasks_from_external_triggers(self):
        # Generate new tasks based on external triggers, such as user input or system events
        # This is a placeholder for the actual task generation logic
        new_tasks = []
        return new_tasks

# Initialize and configure the Task Manager Agent
task_manager_agent = TaskManagerAgent()

# Main loop for the Task Manager Agent operation
while True:
    # Generate and prioritize tasks
    task_manager_agent.generate_and_prioritize_tasks()

    # Process the task at the front of the task list
    current_task = task_manager_agent.get_next_task()

    # Perform the necessary actions for the current task
    # This is a placeholder for the actual task execution logic

    # Check for system termination conditions, such as all tasks completed or a specific goal reached
    if system_termination_condition():
        break
```

The Task Manager Agent should be able to generate, prioritize, and schedule tasks in the proposed architecture based on this pseudocode.

### 🏃 Task Executor Agent

```python
# Pseudocode for the Task Executor Agent

class TaskExecutorAgent:
    def __init__(self):
        self.memory_context = None
        self.execution_context = None

    def process_task(self, task):
        self.current_task = task

    def set_memory_context(self, memory_context):
        self.memory_context = memory_context

    def set_execution_context(self, execution_context):
        self.execution_context = execution_context

    def complete_task(self, task):
        if task.task_type == "analysis":
            result = self.analyze_task(task)
        elif task.task_type == "implementation":
            result = self.implement_task(task)
        elif task.task_type == "testing":
            result = self.test_task(task)
        elif task.task_type == "deployment":
            result = self.deploy_task(task)
        else:
            raise ValueError(f"Unknown task type: {task.task_type}")

        return result

    def analyze_task(self, task):
        # Use GPT-4 and LangChain to analyze the task description and requirements
        analysis_result = analyze_with_gpt4_and_langchain(task.task_description)

        # Incorporate memory context and execution context in the analysis
        analysis_result = self.integrate_memory_and_execution_context(analysis_result, self.memory_context, self.execution_context)

        # Update memory manager with the analysis result
        self.memory_context.update_memory(task.task_id, "analysis_result", analysis_result)

        return analysis_result

    def implement_task(self, task):
        # Use GPT-4 and LangChain to generate implementation code or configuration changes
        implementation_result = implement_with_gpt4_and_langchain(task.task_description)

        # Incorporate memory context and execution context in the implementation
        implementation_result = self.integrate_memory_and_execution_context(implementation_result, self.memory_context, self.execution_context)

        # Update memory manager with the implementation result
        self.memory_context.update_memory(task.task_id, "implementation_result", implementation_result)

        return implementation_result

    def test_task(self, task):
        # Use GPT-4 and LangChain to generate test cases, and execute tests
        test_result = test_with_gpt4_and_langchain(task.task_description)

        # Incorporate memory context and execution context in the testing
        test_result = self.integrate_memory_and_execution_context(test_result, self.memory_context, self.execution_context)

        # Update memory manager with the test result
        self.memory_context.update_memory(task.task_id, "test_result", test_result)

        return test_result

    def deploy_task(self, task):
        # Use GPT-4 and LangChain to generate deployment steps and execute deployment
        deployment_result = deploy_with_gpt4_and_langchain(task.task_description)

        # Incorporate memory context and execution context in the deployment
        deployment_result = self.integrate_memory_and_execution_context(deployment_result, self.memory_context, self.execution_context)

        # Update memory manager with the deployment result
        self.memory_context.update_memory(task.task_id, "deployment_result", deployment_result)

        return deployment_result

    def integrate_memory_and_execution_context(self, result, memory_context, execution_context):
        # Update the result with relevant information from the memory and execution context
        result_with_context = update_result_with_context(result, memory_context, execution_context)

        return result_with_context
```

The agent processes tasks based on their type, using GPT-4 and LangChain to analyze, implement, test, and deploy tasks. The agent integrates the memory context and execution context throughout the process, ensuring that the task execution is informed by relevant information and resources. The memory manager is updated with the results at each stage, providing a record of the task progress and outcomes.

### 🧠 Memory Manager Agent

```python
# Pseudocode for the Memory Manager Agent

# Initialize and configure the Memory Manager Agent
memory_manager_agent = MemoryManagerAgent()
memory_storage = PineconeMemoryStorage()

# Main loop for the Memory Manager Agent operation
while True:

    # Receive a system message with memory-related instructions
    system_message = receive_system_message()

    # Extract necessary information from the system message
    memory_action = system_message["memory_action"]
    memory_key = system_message["memory_key"]
    memory_data = system_message["memory_data"]
    memory_metadata = system_message["memory_metadata"]
    memory_query = system_message["memory_query"]

    # Perform the requested memory action
    if memory_action == "store":
        memory_storage.store(memory_key, memory_data, memory_metadata)

    elif memory_action == "retrieve":
        if memory_query is not None:
            # Retrieve memory items based on the query
            memory_items = memory_storage.retrieve_by_query(memory_query)
        else:
            # Retrieve a single memory item by key
            memory_item = memory_storage.retrieve(memory_key)

    elif memory_action == "update":
        memory_storage.update(memory_key, memory_data, memory_metadata)

    elif memory_action == "delete":
        memory_storage.delete(memory_key)

    # Send a response with the result of the memory action
    send_response(memory_action, memory_key, memory_item)

    # Check for agent termination conditions, such as system shutdown or completion of all tasks
    if agent_termination_condition():
        break
```

The Memory Manager Agent will operate, manage memory items, and interact with the memory storage system (e.g., Pinecone). This will enable it to effectively provide necessary memory and context for task execution, store and retrieve task results, and enforce access controls and data retention policies in the proposed architecture and examples.

### ⚙️ Execution Context Agent

```python
# Pseudocode for the Execution Context Agent

class ExecutionContextAgent:

    def __init__(self):
        self.task_id = None
        self.context_action = None
        self.context_type = None
        self.context_target = None
        self.context_parameters = None
        self.context_metadata = None

    def set_system_message(self, system_message):
        self.task_id = system_message["task_id"]
        self.context_action = system_message["context_action"]
        self.context_type = system_message["context_type"]
        self.context_target = system_message["context_target"]
        self.context_parameters = system_message["context_parameters"]
        self.context_metadata = system_message["context_metadata"]

    def execute(self):
        if self.context_type == "file_system":
            self._execute_file_system()
        elif self.context_type == "api":
            self._execute_api()
        elif self.context_type == "unit_test_framework":
            self._execute_unit_test_framework()
        elif self.context_type == "external_resource":
            self._execute_external_resource()
        else:
            raise ValueError("Invalid context type")

    def _execute_file_system(self):
        if self.context_action == "read":
            file_path = self.context_target
            encoding = self.context_parameters.get("encoding", "utf-8")
            with open(file_path, "r", encoding=encoding) as file:
                content = file.read()
            return content
        elif self.context_action == "write":
            file_path = self.context_target
            content = self.context_parameters["content"]
            encoding = self.context_parameters.get("encoding", "utf-8")
            with open(file_path, "w", encoding=encoding) as file:
                file.write(content)
        else:
            raise ValueError("Invalid context action for file_system")

    def _execute_api(self):
        if self.context_action == "fetch_data":
            response = requests.get(
                self.context_target,
                headers=self.context_parameters.get("headers", {}),
                params=self.context_parameters.get("params", {}),
            )
            response.raise_for_status()
            return response.json()
        elif self.context_action == "send_data":
            response = requests.post(
                self.context_target,
                headers=self.context_parameters.get("headers", {}),
                json=self.context_parameters.get("data", {}),
            )
            response.raise_for_status()
            return response.json()
        else:
            raise ValueError("Invalid context action for API")

    def _execute_unit_test_framework(self):
        # Implement the specific unit test framework execution logic here
        pass

    def _execute_external_resource(self):
        if self.context_type == "github":
            if self.context_action == "create_pull_request":
                title = self.context_parameters["title"]
                body = self.context_parameters["body"]
                head = self.context_parameters["head"]
                base = self.context_parameters["base"]
                repo_url = self.context_target

                pr = github_api_wrapper.create_pull_request(repo_url, title, body, head, base)
                return pr
            else:
                raise ValueError("Invalid context action for GitHub")
        else:
            raise ValueError("Invalid external_resource type")

# Example usage:
execution_context_agent = ExecutionContextAgent()
system_message = {
    "task_id": "t123",
    "context_action": "read",
    "context_type": "file_system",
    "context_target": "/path/to/your/application/codebase/file.js",
    "context_parameters": {
        "encoding": "utf-8"
    },
    "context_metadata": {
        "access_permissions": {
            "read": ["team-abc"],
            "write": ["team-abc"]
        },
        "rate_limits": {
            "requests_per_second": 10
        },
        "retry_policy": {
            "max_attempts": 3,
            "backoff_factor": 2
        }
    }
}
execution_context_agent.set_system_message(system_message)
result = execution_context_agent.execute()
```

The agent will operate, allowing it to effectively interface with external execution environments, such as file systems, APIs, and unit test frameworks, and manage operations according to the architecture and proposed examples.

### 🔒 Security and Safety Agent

```python
# Pseudocode for the SecuritySafetyAgent

class SecuritySafetyAgent:
    def __init__(self):
        self.safety_rules = self.load_safety_rules()

    def load_safety_rules(self):
        # Load safety rules from a predefined source, e.g., a JSON file, a database, etc.
        safety_rules = [
            # Example rules
            {"rule_id": "r1", "rule_description": "Ensure no personally identifiable information (PII) is exposed", "rule_type": "data_privacy"},
            {"rule_id": "r2", "rule_description": "Ensure compliance with ethical guidelines", "rule_type": "ethics"}
        ]
        return safety_rules

    def monitor_and_filter_content(self, current_task, task_result):
        # Apply safety rules on the input and output of the current task
        for rule in self.safety_rules:
            if rule["rule_type"] == "data_privacy":
                self.ensure_data_privacy(current_task, task_result)
            elif rule["rule_type"] == "ethics":
                self.ensure_ethical_compliance(current_task, task_result)

    def ensure_data_privacy(self, current_task, task_result):
        # Implement data privacy checks, such as redacting or anonymizing PII in the task_result
        # This could include checking for email addresses, phone numbers, names, etc.
        task_result = self.redact_pii(task_result)

    def ensure_ethical_compliance(self, current_task, task_result):
        # Implement ethical compliance checks, such as detecting and removing harmful content, biases, or unethical behavior
        # This could include checking for offensive language, fairness, transparency, etc.
        task_result = self.remove_unethical_content(task_result)

    def redact_pii(self, task_result):
        # Implement PII redaction logic, e.g., replacing detected PII with placeholders or anonymized data
        # This is just a simple example, a more sophisticated implementation is needed for real-world use cases
        redacted_task_result = task_result.replace("<email>", "[REDACTED_EMAIL]")
        return redacted_task_result

    def remove_unethical_content(self, task_result):
        # Implement unethical content removal logic, e.g., filtering out offensive language, ensuring fairness, etc.
        # This is just a simple example, a more sophisticated implementation is needed for real-world use cases
        cleaned_task_result = task_result.replace("<offensive_term>", "[REMOVED]")
        return cleaned_task_result

# Example usage of the SecuritySafetyAgent
security_safety_agent = SecuritySafetyAgent()
security_safety_agent.monitor_and_filter_content(current_task, task_result)
```

By using this pseudocode within the system message of the SecuritySafetyAgent, it will have a better understanding of how to monitor and filter content, ensuring that the input and output generated by the system adhere to ethical and safety guidelines.

## Examples of the system in action

### 🎛️ Example of task execution (add chat box)

Task: "Add a chat box to my application and the chat box should be used to be able to interact with our customer services agents."

#### Task execution

When you provide the task to the system, it would progress through several steps involving the different agents to complete it. Here's an outline of the process:

1. Task Manager Agent: The Task Manager Agent receives the new task, which is to add a chat box to your application for interaction with customer service agents. It adds this task to the task list and prioritizes it according to the current list of tasks.

2. Task Executor Agent: The Task Executor Agent processes the task at the front of the task list, which is the chat box implementation task. It would use GPT-4 and the LangChain framework to understand the requirements and generate an implementation plan, such as determining the necessary UI components, backend services, and communication protocols for the chat box.

3. Memory Manager Agent: The Memory Manager Agent provides the necessary memory and context for the task execution. It might retrieve any relevant information stored in Pinecone, such as previous interactions with your application, existing customer service channels, or any prior chat box implementations.

4. Execution Context Agent: The Execution Context Agent interfaces with the external execution environments, such as your application's codebase, APIs, and any required libraries or frameworks. It would access and manage the file system to read and modify the application's code, data, or configuration files, as well as interface with APIs to fetch data or trigger actions based on the task requirements.

5. Task Executor Agent (Continued): With the necessary context, memory, and execution environment, the Task Executor Agent generates the appropriate code and configuration changes for implementing the chat box in your application. It might create new UI components, set up backend services, and establish communication protocols between the chat box and customer service agents.

6. Security and Safety Agent: Throughout the process, the Security and Safety Agent monitors and filters the content generated by the system to ensure that the implementation adheres to ethical and safety guidelines, as well as adhering to any data privacy and security measures necessary for protecting sensitive information.

7. Task Manager Agent (Continued): Once the chat box implementation task is completed, the Task Manager Agent generates new tasks based on the completed task result, such as testing the chat box functionality or integrating it with existing customer service tools. These new tasks are prioritized and added to the task list.

The system iteratively works through these steps until the chat box is successfully implemented in your application and all follow-up tasks are completed.

### 🔧 More advanced example of task execution (provide fix for problem in production)

Task: "Use the output from a Sentry stack trace from a production system where of an application experienced an unexpected error and provide a fix for the issue as a GitHub pull request."

#### Task execution

When you provide the task to analyze a Sentry stack trace from a production system, identify the issue, propose a fix, add test cases, verify the fix, and create a GitHub pull request, the system would progress through several steps involving the different agents to complete it. Here's an outline of the process:

1. Task Manager Agent: The Task Manager Agent receives the new task and breaks it down into subtasks, such as analyzing the stack trace, identifying the issue, proposing a fix, creating test cases, verifying the fix, and creating a GitHub pull request. It adds these subtasks to the task list and prioritizes them according to the current list of tasks.

2. Task Executor Agent: The Task Executor Agent processes the subtasks in the task list one by one, using GPT-4 and the LangChain framework. For example, it would first analyze the Sentry stack trace to understand the error, then reason about the issue and propose a fix for it.

3. Memory Manager Agent: The Memory Manager Agent provides the necessary memory and context for the task execution. It might retrieve any relevant information stored in Pinecone, such as previous error logs, similar issues in the past, or any related code changes.

4. Execution Context Agent: The Execution Context Agent interfaces with the external execution environments, such as the application's codebase, APIs, and any required libraries or frameworks. It would access and manage the file system to read and modify the application's code, data, or configuration files, as well as interface with APIs to fetch data or trigger actions based on the task requirements.

5. Task Executor Agent (Continued): With the necessary context, memory, and execution environment, the Task Executor Agent generates the appropriate code and configuration changes for the proposed fix. It would also create new test cases to cover the changes and verify that the fix will solve the identified problem. The system then reasons about why the change is sound, considering factors like code quality, maintainability, and performance.

6. Security and Safety Agent: Throughout the process, the Security and Safety Agent monitors and filters the content generated by the system to ensure that the implementation adheres to ethical and safety guidelines, as well as adhering to any data privacy and security measures necessary for protecting sensitive information.

7. Task Executor Agent (Final Step): Once the fix is verified and the system has reasoned about the soundness of the change, the Task Executor Agent creates a GitHub pull request with the proposed changes, providing a detailed description of the issue, the fix, test cases, and the reasoning behind the change.

8. Task Manager Agent (Continued): After the GitHub pull request is created, the Task Manager Agent generates new tasks based on the completed task result, such as monitoring the pull request for reviews, addressing feedback, and merging the changes after approval. These new tasks are prioritized and added to the task list.

The system iteratively works through these steps until the issue is resolved, the fix is verified, and the GitHub pull request is created and monitored for completion.

## ⚠️ Disclaimers

This is a work in progress and is not intended to be a complete or accurate representation of the system. It is intended to be a high-level overview of the system's capabilities and how it would work in practice. The system is still in the early stages of development and is not yet ready for production use.

## 🤝 Contributions

I'm looking for people who are interested in working on a system capable of executing and iterating on tasks such as these. If anyone is interested in contributing to the development of the system, please reach out to me.
# BabyAGI

> [!NOTE]
> The original BabyAGI from March 2023 introduced task planning as a method for developing autonomous agents. This project has been archived and moved to the [babyagi_archive](https://github.com/yoheinakajima/babyagi_archive) repo (September 2024 snapshot).

> [!CAUTION]
> This is a framework built by Yohei who has never held a job as a developer. The purpose of this repo is to share ideas and spark discussion and for experienced devs to play with. Not meant for production use. Use with cautioun.

---

This newest BabyAGI is an experimental framework for a self-building autonomous agent. Earlier efforts to expand BabyAGI have made it clear that the optimal way to build a general autonomous agent is to build the simplest thing that can build itself.

Check out [this introductory X/Twitter thread](https://x.com/yoheinakajima/status/1840678823681282228) for a simple overview.

The core is a new function framework (**functionz**) for storing, managing, and executing functions from a database. It offers a graph-based structure for tracking imports, dependent functions, and authentication secrets, with automatic loading and comprehensive logging capabilities. Additionally, it comes with a dashboard for managing functions, running updates, and viewing logs.

## Table of Contents

- [Quick Start](#quick-start)
- [Basic Usage](#basic-usage)
- [Function Metadata](#function-metadata)
- [Function Loading](#function-loading)
- [Key Dependencies](#key-dependencies)
- [Execution Environment](#execution-environment)
  - [Log](#log)
- [Dashboard](#dashboard)
- [Pre-loaded Functions](#pre-loaded-functions)
- [Future/Draft Features](#futuredraft-features)
- [API Reference](#api-reference)
- [Contributing](#contributing)
- [License](#license)

## Quick Start

To quickly check out the dashboard and see how it works:

1. **Install BabyAGI:**

    ```bash
    pip install babyagi
    ```

2. **Import BabyAGI and load the dashboard:**

    ```python
    import babyagi

    if __name__ == "__main__":
        app = babyagi.create_app('/dashboard')
        app.run(host='0.0.0.0', port=8080)
    ```

3. **Navigate to the dashboard:**

    Open your browser and go to `http://localhost:8080/dashboard` to access the BabyAGI dashboard.
    
## Basic Usage

Start by importing `babyagi` and registering your functions. Here's how to register two functions, where one depends on the other:

```python
import babyagi

# Register a simple function
@babyagi.register_function()
def world():
    return "world"

# Register a function that depends on 'world'
@babyagi.register_function(dependencies=["world"])
def hello_world():
    x = world()
    return f"Hello {x}!"

# Execute the function
print(babyagi.hello_world())  # Output: Hello world!

if __name__ == "__main__":
    app = babyagi.create_app('/dashboard')
    app.run(host='0.0.0.0', port=8080)
```

## Function Metadata

Functions can be registered with metadata to enhance their capabilities and manage their relationships. Here's a more comprehensive example of function metadata, showing logical usage of all fields:

```python
import babyagi

@babyagi.register_function(
    imports=["math"],
    dependencies=["circle_area"],
    key_dependencies=["openai_api_key"],
    metadata={
        "description": "Calculates the volume of a cylinder using the circle_area function."
    }
)
def cylinder_volume(radius, height):
    import math
    area = circle_area(radius)
    return area * height
```

**Available Metadata Fields:**

- `imports`: List of external libraries the function depends on.
- `dependencies`: List of other functions this function depends on.
- `key_dependencies`: List of secret keys required by the function.
- `metadata["description"]`: A description of what the function does.

## Function Loading

In addition to using `register_function`, you can use `load_function` to load plugins or draft packs of functions. BabyAGI comes with built-in function packs, or you can load your own packs by pointing to the file path.

You can find available function packs in `babyagi/functionz/packs`.

**Loading Custom Function Packs:**

```python
import babyagi

# Load your custom function pack
babyagi.load_functions("path/to/your/custom_functions.py")
```

This approach makes function building and management easier by organizing related functions into packs.

## Key Dependencies

You can store `key_dependencies` directly from your code or manage them via the dashboard.

**Storing Key Dependencies from Code:**

```python
import babyagi

# Add a secret key
babyagi.add_key_wrapper('openai_api_key', 'your_openai_api_key')
```

**Adding Key Dependencies via Dashboard:**

Navigate to the dashboard and use the **add_key_wrapper** feature to securely add your secret keys.

## Execution Environment

BabyAGI automatically loads essential function packs and manages their dependencies, ensuring a seamless execution environment. Additionally, it logs all activities, including the relationships between functions, to provide comprehensive tracking of function executions and dependencies.

### Log

BabyAGI implements a comprehensive logging system to track all function executions and their interactions. The logging mechanism ensures that every function call, including its inputs, outputs, execution time, and any errors, is recorded for monitoring and debugging purposes.

**Key Logging Features:**

- **Execution Tracking:** Logs when a function starts and finishes execution, including the function name, arguments, keyword arguments, and execution time.
  
- **Error Logging:** Captures and logs any errors that occur during function execution, providing detailed error messages for troubleshooting.

- **Dependency Management:** Automatically resolves and logs dependencies between functions, ensuring that all required functions and libraries are loaded before execution.

- **Trigger Logging:** Logs the execution of triggered functions, detailing which functions were triggered by others and their respective execution outcomes.

- **Comprehensive Records:** Maintains a history of all function executions, enabling users to review past activities, understand function relationships, and analyze performance metrics.

**How Triggers Work:**

Triggers are mechanisms that allow certain functions to be automatically executed in response to specific events or actions within the system. For example, when a function is added or updated, a trigger can initiate the generation of a description for that function.

Triggers enhance the autonomy of BabyAGI by enabling automated workflows and reducing the need for manual intervention. However, it's essential to manage triggers carefully to avoid unintended recursive executions or conflicts between dependent functions.

## Dashboard

The BabyAGI dashboard offers a user-friendly interface for managing functions, monitoring executions, and handling configurations. Key features include:

- **Function Management:** Register, deregister, and update functions directly from the dashboard.

- **Dependency Visualization:** View and manage dependencies between functions to understand their relationships.

- **Secret Key Management:** Add and manage secret keys securely through the dashboard interface.

- **Logging and Monitoring:** Access comprehensive logs of function executions, including inputs, outputs, and execution times.

- **Trigger Management:** Set up triggers to automate function executions based on specific events or conditions.

**Accessing the Dashboard:**

After running your application, navigate to `http://localhost:8080/dashboard` to access the BabyAGI dashboard.
## Pre-loaded Functions Summary

BabyAGI includes two pre-loaded function packs:

1. **Default Functions (`packs/default_functions.py`):**
   - **Function Execution:** Run, add, update, or retrieve functions and versions.
   - **Key Management:** Add and retrieve secret keys.
   - **Triggers:** Add triggers to execute functions based on others.
   - **Logs:** Retrieve logs with optional filters.

2. **AI Functions (`packs/ai_generator.py`):**
   - **AI Description & Embeddings:** Auto-generate descriptions and embeddings for functions.
   - **Function Selection:** Find or choose similar functions based on prompts.

## Running a Self-Building Agent

BabyAGI includes two experimental self-building agents, showcasing how the framework can help a self-building coding agent leverage existing functions to write new ones.

### 1. `process_user_input` in the `code_writing_functions` pack

This function first determines whether to use an existing function or generate new ones. If new functions are needed, it breaks them down into smaller reusable components and combines them into a final function.

Try this:

~~~python
import babyagi

babyagi.add_key_wrapper('openai_api_key', os.environ['OPENAI_API_KEY'])
babyagi.load_functions("drafts/code_writing_functions")

babyagi.process_user_input("Grab today's score from ESPN and email <NAME_EMAIL>")
~~~

When you run this, you will see the functions being generated in the shell and new functions will be available in the dashboard once completed.

### 2. `self_build` in the `self_build` pack

This function takes a user description and generates X distinct tasks that a user might ask an AI assistant. Each task is processed by `process_user_input`, creating new functions if no existing ones suffice.

Try this:

~~~python
import babyagi

babyagi.add_key_wrapper('openai_api_key', os.environ['OPENAI_API_KEY'])
babyagi.load_functions("drafts/code_writing_functions")
babyagi.load_functions("drafts/self_build")

babyagi.self_build("A sales person at an enterprise SaaS company.", 3)
~~~

This will generate 3 distinct tasks a salesperson might ask an AI assistant and create functions to handle those.

*The functions will be generated and stored in the dashboard, but note that the generated code is minimal and may need improvement.

![alt text](https://github.com/yoheinakajima/babyagi_staging/blob/main/self_build.png?raw=true)



**Warning:** These draft features are experimental concepts and may not function as intended. They require significant improvements and should be used with caution.


## Contributing

Contributions are greatly appreciatedly, but candidly I have not been great at managing PRs. Please be patient as things will move slow while I am working on this alone (on nights and weekends). I may start by building a small core crew before collaborating with a larger group.

If you are a dev, investor, friend of open-source and interesting supporting AI work I do, please fill [this form](https://forms.gle/UZLyT75HQULr8XNUA) (I have a few fun initiatives coming up!)

## License

BabyAGI is released under the MIT License. See the [LICENSE](LICENSE) file for more details.
