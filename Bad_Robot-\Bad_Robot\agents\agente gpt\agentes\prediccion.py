### agentes/prediccion.py - Predicción básica del estado futuro del entorno

def predecir_estado(estado_actual):
    """
    Predicción simple basada en reglas o valores actuales.
    Se puede reemplazar por modelos ML más complejos si se desea.
    """
    prediccion = estado_actual.copy()

    # Simulación: predicción simple basada en tendencia
    if estado_actual["clima"] == "tormenta":
        prediccion["clima"] = "lluvia"
    elif estado_actual["clima"] == "lluvia":
        prediccion["clima"] = "templado"
    else:
        prediccion["clima"] = "soleado"

    prediccion["comida"] = max(0, estado_actual["comida"] - 5)
    prediccion["depredadores"] = max(0, estado_actual["depredadores"] + 1)

    return prediccion
