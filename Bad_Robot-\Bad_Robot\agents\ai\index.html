<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>IA Asistente de Archivos</title>
    <style>
        body { font-family: 'Courier New', monospace; display: flex; background: #282c34; color: #abb2bf; }
        .container { padding: 20px; }
        #control-panel { flex: 1; border-right: 1px solid #444; }
        #file-viewer { flex: 1; }
        h1, h2 { color: #61afef; }
        textarea, input { width: 95%; background: #21252b; color: #abb2bf; border: 1px solid #444; padding: 10px; }
        button { background: #61afef; color: #282c34; border: none; padding: 10px 15px; cursor: pointer; margin: 5px; }
        button.reward-btn { background: #98c379; }
        button.penalty-btn { background: #e06c75; }
        #ai-log, #file-content { background: #21252b; padding: 15px; height: 300px; overflow-y: scroll; white-space: pre-wrap; border-radius: 5px; }
        .log-entry { margin-bottom: 10px; border-bottom: 1px dashed #444; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div id="control-panel" class="container">
        <h1>Panel de Control de la IA</h1>
        <h2>1. Da una orden a la IA:</h2>
        <textarea id="user-command" rows="3" placeholder="Ej: 'lista los archivos' o 'crea un reporte llamado reporte.txt con el contenido de datos_iniciales.txt'"></textarea>
        <button id="send-command-btn">Ejecutar Tarea</button>

        <h2>2. Log de Acciones y Pensamiento de la IA:</h2>
        <div id="ai-log">Esperando instrucciones...</div>

        <h2>3. Da tu feedback (Recompensa):</h2>
        <div id="feedback-controls" style="display: none;">
            <button id="reward-btn" class="reward-btn">Acción Correcta (+10)</button>
            <button id="penalty-btn" class="penalty-btn">Acción Incorrecta (-10)</button>
        </div>
    </div>

    <div id="file-viewer" class="container">
        <h1>Visor del Workspace (`ai_workspace/`)</h1>
        <h2 id="current-path">/</h2>
        <div id="file-content">El contenido del archivo o directorio se mostrará aquí.</div>
    </div>

    <script src="script.js"></script>
</body>
</html>