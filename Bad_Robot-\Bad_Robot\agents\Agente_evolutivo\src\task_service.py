import sqlite3

DATABASE_URL = 'tasks.db'

def get_db_connection():
    conn = sqlite3.connect(DATABASE_URL)
    conn.row_factory = sqlite3.Row
    return conn

# --- Lógica de Servicio ---

def get_all_tasks():
    """Obtiene todas las tareas de la base de datos."""
    conn = get_db_connection()
    tasks = conn.execute('SELECT * FROM tasks').fetchall()
    conn.close()
    return [dict(row) for row in tasks]

def get_task_by_id(task_id):
    """Obtiene una tarea por su ID."""
    conn = get_db_connection()
    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,)).fetchone()
    conn.close()
    return dict(task) if task else None

def create_task(title, completed=False):
    """Crea una nueva tarea en la base de datos."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO tasks (title, completed) VALUES (?, ?)', (title, completed))
    new_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return {'id': new_id, 'title': title, 'completed': completed}

def update_task(task_id, title, completed):
    """Actualiza una tarea existente."""
    conn = get_db_connection()
    conn.execute('UPDATE tasks SET title = ?, completed = ? WHERE id = ?', (title, completed, task_id))
    conn.commit()
    conn.close()
    return get_task_by_id(task_id)

def delete_task(task_id):
    """Elimina una tarea de la base de datos."""
    conn = get_db_connection()
    conn.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
    conn.commit()
    conn.close()
    return True