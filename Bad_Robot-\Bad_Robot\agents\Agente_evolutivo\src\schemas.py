from pydantic import BaseModel, Field
from typing import Optional

class TaskBase(BaseModel):
    """Esquema base para una tarea. Contiene los campos comunes."""
    title: str = Field(..., min_length=1, description="El título de la tarea no puede estar vacío.")
    completed: bool = False

class TaskCreate(TaskBase):
    """Esquema para la creación de una tarea. No se necesita más."""
    pass

class TaskUpdate(BaseModel):
    """Esquema para actualizar una tarea. Todos los campos son opcionales."""
    title: Optional[str] = Field(None, min_length=1)
    completed: Optional[bool] = None

class Task(TaskBase):
    """Esquema completo de una tarea, incluyendo el ID que genera la BD."""
    id: int

    class Config:
        # Permite que Pydantic funcione con objetos que no son dicts (como los de la BD)
        orm_mode = True 