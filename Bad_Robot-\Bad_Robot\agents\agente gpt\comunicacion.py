### main.py - Entrada principal con detección de entorno y selección dinámica

import platform
import os
import psutil
import csv
import random
from entornos import ecosistema, financiero, industrial, espacial
from agentes.base import Agente
from agentes.genetico import seleccionar_mejores, cruzar_genotipos, mutar_genotipo
from agentes.comunicacion import BusComunicacion
from config import seleccionar_entorno

class Mensaje:
    def __init__(self, emisor_id, contenido, tipo="info"):
        self.emisor_id = emisor_id
        self.contenido = contenido
        self.tipo = tipo  # puede ser: "alerta", "oferta", "info", etc.

class BusComunicacion:
    def __init__(self):
        self.mensajes = []

    def enviar(self, mensaje):
        self.mensajes.append(mensaje)

    def recibir_para(self, receptor_id=None):
        recibidos = [m for m in self.mensajes]
        self.mensajes.clear()
        return recibidos

def detectar_entorno_maquina():
    info = {
        "sistema": platform.system(),
        "nucleo": platform.release(),
        "cpu_cores": psutil.cpu_count(logical=False),
        "ram_gb": round(psutil.virtual_memory().total / (1024**3), 2),
    }
    return info


def main():
    os.makedirs("logs", exist_ok=True)
    csv_log = open("logs/simulacion.csv", mode="w", newline="")
    writer = csv.writer(csv_log)
    writer.writerow(["ciclo", "agente_id", "accion", "clima", "comida", "depredadores"])

    info_maquina = detectar_entorno_maquina()
    entorno_id = seleccionar_entorno(info_maquina)

    if entorno_id == "ecosistema":
        entorno = ecosistema.Ecosistema()
    elif entorno_id == "financiero":
        entorno = financiero.MercadoFinanciero()
    elif entorno_id == "industrial":
        entorno = industrial.EntornoIndustrial()
    elif entorno_id == "espacial":
        entorno = espacial.MisionEspacial()
    else:
        raise ValueError("Entorno no reconocido")

    bus = BusComunicacion()
    agentes = [Agente(id=i, entorno=entorno, bus=bus) for i in range(5)]

    for ciclo in range(100):  # ciclos de simulación
        observaciones = entorno.obtener_estado()

        # Comunicación
        for agente in agentes:
            bus.enviar({"id": agente.id, "estado": observaciones})

        mensajes = bus.recibir_todos()

        for agente in agentes:
            agente.mensajes = mensajes  # contexto social
            accion = agente.decidir(observaciones)
            entorno.aplicar_accion(agente.id, accion)
            writer.writerow([ciclo, agente.id, accion, observaciones["clima"], observaciones["comida"], observaciones["depredadores"]])

        entorno.actualizar()

        # Evolución cada 20 ciclos
        if ciclo > 0 and ciclo % 20 == 0:
            top = seleccionar_mejores(agentes, entorno.recompensas)
            nuevos_agentes = []
            for i in range(len(agentes)):
                p1, p2 = random.sample(top, 2)
                nuevo_gen = mutar_genotipo(cruzar_genotipos(p1.genotipo, p2.genotipo))
                nuevos_agentes.append(Agente(id=i, entorno=entorno, genotipo=nuevo_gen, bus=bus))
            agentes = nuevos_agentes

    csv_log.close()
    entorno.mostrar_resultados()


if __name__ == "__main__":
    main()
