// --- Selectores de elementos del DOM (igual que antes, pero con los nuevos) ---
const userCommandEl = document.getElementById('user-command');
const sendCommandBtn = document.getElementById('send-command-btn');
const aiLogEl = document.getElementById('ai-log');
const feedbackControlsEl = document.getElementById('feedback-controls');
const rewardBtn = document.getElementById('reward-btn');
const penaltyBtn = document.getElementById('penalty-btn');
const executionConfirmationEl = document.getElementById('execution-confirmation');
const confirmExecuteBtn = document.getElementById('confirm-execute-btn');
const cancelExecuteBtn = document.getElementById('cancel-execute-btn');
const filenameEl = document.getElementById('current-filename');
const codeContentEl = document.getElementById('code-content');
const executionOutputEl = document.getElementById('execution-output');

const API_URL = 'http://127.0.0.1:5000';
let currentTask = '';
let lastActionData = null; // Guardar datos para la confirmación

// --- Event Listeners ---
sendCommandBtn.addEventListener('click', startTask);
rewardBtn.addEventListener('click', () => provideFeedback(10));
penaltyBtn.addEventListener('click', () => provideFeedback(-10));
confirmExecuteBtn.addEventListener('click', confirmAndContinue);
cancelExecuteBtn.addEventListener('click', () => {
    executionConfirmationEl.style.display = 'none';
    logMessage("👨‍🏫", "Ejecución cancelada por el usuario.");
});

// --- Funciones de Comunicación con el Backend ---
async function startTask() {
    currentTask = userCommandEl.value;
    if (!currentTask) return alert("Por favor, introduce una orden.");
    
    logMessage("🤖", `Nueva tarea: "${currentTask}"`);
    sendCommandBtn.disabled = true;
    hideAllControls();

    const response = await fetch(`${API_URL}/start_task`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command: currentTask })
    });
    handleAIResponse(await response.json());
}

async function provideFeedback(reward) {
    logMessage("👨‍🏫", `Feedback enviado: ${reward > 0 ? 'Positivo' : 'Negativo'} (${reward})`);
    hideAllControls();

    const response = await fetch(`${API_URL}/feedback_and_continue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reward: reward, command: currentTask, confirmation_data: null })
    });
    handleAIResponse(await response.json());
}

async function confirmAndContinue() {
    logMessage("👨‍🏫", "Confirmación de ejecución enviada.");
    hideAllControls();

    const response = await fetch(`${API_URL}/feedback_and_continue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            reward: 1, // Confirmar es como un pequeño feedback positivo
            command: currentTask, 
            confirmation_data: lastActionData // Enviamos los datos de la acción a confirmar
        })
    });
    handleAIResponse(await response.json());
}

// --- Lógica de la Interfaz ---
function handleAIResponse(data) {
    sendCommandBtn.disabled = false;
    if (data.error) {
        logMessage("🔥", `Error del servidor: ${data.error}`);
        return;
    }

    logMessage("💡", `Decisión: ${data.decision_type}. Razón: ${data.reason}`);
    logMessage("⚡️", `Acción Planeada: ${data.action_name}(${JSON.stringify(data.action_params || {})})`);

    // Si la acción requiere confirmación, nos detenemos y esperamos al usuario
    if (data.requires_confirmation) {
        logMessage("⚠️", "Esta acción requiere tu confirmación para proceder.");
        lastActionData = data; // Guardamos los datos de la acción
        executionConfirmationEl.style.display = 'block';
        return;
    }

    // Actualizar la interfaz con los resultados
    updateUI(data.result);

    if (data.task_complete) {
        logMessage("✅", "La IA considera que la tarea ha finalizado.");
        hideAllControls();
    } else {
        feedbackControlsEl.style.display = 'block';
    }
}

function updateUI(result) {
    if (!result) return;
    
    logMessage("📄", `Resultado de la acción: ${result.message}`);
    
    filenameEl.textContent = result.filename || "Ningún archivo seleccionado";
    codeContentEl.textContent = result.code_content || "N/A";
    
    let outputHtml = result.stdout || "";
    if (result.stderr) {
        outputHtml += `<br><span class="error-log"><strong>ERROR:</strong><br>${result.stderr}</span>`;
    }
    executionOutputEl.innerHTML = outputHtml || "Salida del programa...";
}

function logMessage(icon, text) {
    aiLogEl.innerHTML += `<div class="log-entry"><strong>${icon}</strong> ${text}</div>`;
    aiLogEl.scrollTop = aiLogEl.scrollHeight;
}

function hideAllControls() {
    feedbackControlsEl.style.display = 'none';
    executionConfirmationEl.style.display = 'none';
}