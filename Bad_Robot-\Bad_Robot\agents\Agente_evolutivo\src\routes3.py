import sqlite3
from flask import Blueprint, request, jsonify, abort

tasks_bp = Blueprint('tasks', __name__)

# Función para obtener una conexión a la base de datos
def get_db_connection():
    conn = sqlite3.connect('tasks.db')
    # Esto hace que los resultados de la BD vengan como diccionarios en lugar de tuplas
    conn.row_factory = sqlite3.Row
    return conn

# Listar todas las tareas
@tasks_bp.route('/tasks', methods=['GET'])
def get_tasks():
    conn = get_db_connection()
    tasks = conn.execute('SELECT * FROM tasks').fetchall()
    conn.close()
    # Convertimos los objetos Row a una lista de diccionarios para poder pasarlos a JSON
    return jsonify([dict(row) for row in tasks])

# Crear una nueva tarea
@tasks_bp.route('/tasks', methods=['POST'])
def add_task():
    data = request.get_json()
    if not data or not 'title' in data:
        abort(400, description="El campo 'title' es requerido.")

    title = data['title']
    completed = data.get('completed', False) # Por defecto es False

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO tasks (title, completed) VALUES (?, ?)', (title, completed))
    new_id = cursor.lastrowid
    conn.commit()
    conn.close()

    return jsonify({'id': new_id, 'title': title, 'completed': completed}), 201

# Obtener una tarea específica
@tasks_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    conn = get_db_connection()
    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,)).fetchone()
    conn.close()
    if task is None:
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")
    return jsonify(dict(task))

# Actualizar una tarea existente
@tasks_bp.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    conn = get_db_connection()
    # Primero, verificamos que la tarea exista
    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,)).fetchone()
    if task is None:
        conn.close()
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")

    data = request.get_json()
    title = data.get('title', task['title'])
    completed = data.get('completed', task['completed'])

    conn.execute('UPDATE tasks SET title = ?, completed = ? WHERE id = ?', (title, completed, task_id))
    conn.commit()
    
    updated_task = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,)).fetchone()
    conn.close()

    return jsonify(dict(updated_task))

# Eliminar una tarea
@tasks_bp.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    conn = get_db_connection()
    # Verificamos que exista antes de borrar para saber si fue exitoso
    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (task_id,)).fetchone()
    if task is None:
        conn.close()
        abort(404, description=f"Tarea con ID {task_id} no encontrada.")

    conn.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
    conn.commit()
    conn.close()
    return '', 204